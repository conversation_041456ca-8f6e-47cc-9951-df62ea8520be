package com.trs.ai.moye.common.utils;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.contains;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class HttpFileUtilsTest {

    @Mock
    private HttpServletResponse response;

    @Mock
    private ServletOutputStream outputStream;

    @Test
    void exportFileContent_ShouldSetCorrectHeadersAndWriteContent() throws IOException {
        String content = "Test content";
        String fileName = "test_file.txt";

        when(response.getOutputStream()).thenReturn(outputStream);

        HttpFileUtils.exportFileContent(response, content, fileName);

        verify(response).setContentType("application/octet-stream; charset=UTF-8");
        verify(response).setHeader(eq("Content-Disposition"),
            eq("attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.name())));
        verify(outputStream).write(content.getBytes(StandardCharsets.UTF_8));
        verify(response).flushBuffer();
    }

    @Test
    void exportFileContent_ShouldThrowIllegalArgumentException_WhenResponseIsNull() {
        assertThrows(IllegalArgumentException.class, () ->
            HttpFileUtils.exportFileContent(null, "content", "file.txt"));
    }

    @Test
    void exportFileContent_ShouldThrowIllegalArgumentException_WhenContentIsNull() {
        assertThrows(IllegalArgumentException.class, () ->
            HttpFileUtils.exportFileContent(response, null, "file.txt"));
    }

    @Test
    void exportFileContent_ShouldThrowIllegalArgumentException_WhenFileNameIsNull() {
        assertThrows(IllegalArgumentException.class, () ->
            HttpFileUtils.exportFileContent(response, "content", null));
    }

    @Test
    void exportFileContent_ShouldSanitizeFileName() throws IOException {
        String content = "Test content";
        String fileName = "test/file<>:\"\\|?*.txt";
        String expectedSanitizedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);

        when(response.getOutputStream()).thenReturn(outputStream);
        HttpFileUtils.exportFileContent(response, content, fileName);
        verify(response).setHeader(eq("Content-Disposition"), contains(expectedSanitizedFileName));
    }

    @Test
    void exportFileContent_ShouldHandleIOException() throws IOException {
        String content = "Test content";
        String fileName = "test_file.txt";

        when(response.getOutputStream()).thenThrow(new IOException("Test IO Exception"));

        assertThrows(IOException.class, () ->
            HttpFileUtils.exportFileContent(response, content, fileName));
    }
}