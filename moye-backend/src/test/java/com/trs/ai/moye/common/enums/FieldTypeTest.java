package com.trs.ai.moye.common.enums;

import com.trs.ai.moye.common.constants.TimeFormat;
import com.trs.moye.base.common.enums.FieldType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * 字段类型检验
 *
 * <AUTHOR>
 * @since 2024/10/14 11:14
 */
class FieldTypeTest {

    @Test
    void charTest() {
        String value1 = "123";
        String value2 = "1";
        Assertions.assertNull(FieldType.CHAR.parseByObjectType(value1));
        Assertions.assertEquals("1", FieldType.CHAR.parseByObjectType(value2));
    }

    @Test
    void intTest() {
        String value1 = "abc";
        String value2 = "123";
        Assertions.assertNull(FieldType.INT.parseByObjectType(value1));
        Assertions.assertEquals(123, FieldType.INT.parseByObjectType(value2));
    }

    @Test
    void longTest() {
        String value1 = "abc";
        String value2 = "123456";
        Assertions.assertNull(FieldType.LONG.parseByObjectType(value1));
        Assertions.assertEquals(123456L, FieldType.LONG.parseByObjectType(value2));
    }

    @Test
    void floatTest() {
        String value1 = "abc";
        String value2 = "1.23";
        Float f = Float.parseFloat(value2);
        Assertions.assertNull(FieldType.FLOAT.parseByObjectType(value1));
        Assertions.assertEquals(f, FieldType.FLOAT.parseByObjectType(value2));
    }

    @Test
    void doubleTest() {
        String value1 = "abc";
        String value2 = "1.2345";
        Double d = Double.parseDouble(value2);
        Assertions.assertNull(FieldType.DOUBLE.parseByObjectType(value1));
        Assertions.assertEquals(d, FieldType.DOUBLE.parseByObjectType(value2));
    }

    @Test
    void dateTest() {
        String value1 = "1";
        String value2 = "2024-10-14";
        LocalDate date = LocalDate.parse(value2, TimeFormat.DEFAULT_DATE_FORMATTER);
        Assertions.assertNull(FieldType.DATE.parseByObjectType(value1));
        Assertions.assertEquals(date, FieldType.DATE.parseByObjectType(value2));
    }

    @Test
    void dateTimeTest() {
        String value1 = "1";
        String value2 = "2024-10-14 11:15:00";
        LocalDateTime dateTime = LocalDateTime.parse(value2, TimeFormat.DEFAULT_FORMATTER);
        Assertions.assertNull(FieldType.DATETIME.parseByObjectType(value1));
        Assertions.assertEquals(dateTime, FieldType.DATETIME.parseByObjectType(value2));
    }
}
