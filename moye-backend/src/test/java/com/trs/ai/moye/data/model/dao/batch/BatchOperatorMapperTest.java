package com.trs.ai.moye.data.model.dao.batch;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.ai.moye.data.ability.dao.AbilityMapper;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.Schema.BaseTypeSchema;
import com.trs.moye.ability.entity.operator.BatchOperator;
import com.trs.moye.ability.entity.operator.OperatorRowType;
import com.trs.moye.ability.enums.AbilityTestStatus;
import com.trs.moye.ability.enums.AbilityType;
import com.trs.moye.ability.enums.AbilityUpdateStatus;
import java.util.List;
import javax.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

/**
 * BatchOperatorMapperTest
 *
 * <AUTHOR>
 * @since 2025/3/25 18:02
 */
@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class BatchOperatorMapperTest {

    @Resource
    private BatchOperatorMapper batchOperatorMapper;
    @Resource
    private AbilityMapper abilityMapper;
    @Resource
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        jdbcTemplate.execute("TRUNCATE TABLE batch_operator");
        jdbcTemplate.execute("TRUNCATE TABLE ability");

        // 造ability数据
        Ability ability = new Ability();
        ability.setId(1);
        ability.setEnName("Ability 1");
        ability.setZhName("能力1");
        ability.setDescription("Description of Ability 1");
        ability.setType(AbilityType.BATCH);
        ability.setPath("/path/to/ability1");
        ability.setOperatorCategoryId(1);
        ability.setIconName("icon1");
        ability.setUpdateStatus(AbilityUpdateStatus.UPDATED);
        ability.setTestStatus(AbilityTestStatus.UNTESTED);
        ability.setInputSchema(new BaseTypeSchema());
        ability.setOutputSchema(new BaseTypeSchema());
        ability.setInputSize(1);
        abilityMapper.insert(ability);

        // 造operator数据
        OperatorRowType inputFields = new OperatorRowType();
        BaseTypeSchema schema = new BaseTypeSchema();
        schema.setEnName("age");
        inputFields.put("age", schema);
        List<BatchOperator> operators = List.of(
            BatchOperator.builder().arrangementId(1).dataModelId(1).displayId(1L).name("Operator 1").abilityId(ability.getId()).enabled(true).inputFields(inputFields).build(),
            BatchOperator.builder().arrangementId(1).dataModelId(1).displayId(2L).name("Operator 2").abilityId(ability.getId()).enabled(true).build(),
            BatchOperator.builder().arrangementId(2).dataModelId(2).displayId(3L).name("Operator 3").abilityId(ability.getId()).enabled(true).build()
        );
        batchOperatorMapper.insert(operators);
    }

    @Test
    void deleteByArrangementId() {
        int arrangementId = 1;
        List<BatchOperator> operators = batchOperatorMapper.selectByArrangementId(arrangementId);
        assertFalse(operators.isEmpty());

        batchOperatorMapper.deleteByArrangementId(arrangementId);
        List<BatchOperator> deletedOperators = batchOperatorMapper.selectByArrangementId(arrangementId);
        assertTrue(deletedOperators.isEmpty());
    }

    @Test
    void deleteByDataModelId() {
        int arrangementId = 1;
        int dataModelId = 1;
        List<BatchOperator> operators = batchOperatorMapper.selectByArrangementId(arrangementId);
        assertFalse(operators.isEmpty());

        batchOperatorMapper.deleteByDataModelId(dataModelId);
        List<BatchOperator> deletedOperators = batchOperatorMapper.selectByArrangementId(arrangementId);
        assertTrue(deletedOperators.isEmpty());
    }

    @Test
    void selectByArrangementId() {
        int arrangementId = 1;
        List<BatchOperator> operators = batchOperatorMapper.selectByArrangementId(arrangementId);
        assertEquals(2, operators.size());
        assertEquals(1, operators.get(0).getArrangementId());
        assertEquals("Ability 1", operators.get(0).getAbility().getEnName());
        assertEquals(1, operators.get(0).getAbility().getInputSize());
    }
}