package com.trs.ai.moye.monitor.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.response.ResponseMessage;
import com.trs.ai.moye.common.web.response.CustomerResponseBodyConvert;
import com.trs.ai.moye.common.web.response.GlobalExceptionProcessor;
import com.trs.ai.moye.monitor.request.MonitorEventPeriodRequest;
import com.trs.ai.moye.monitor.response.MonitorEventPeriodResponse;
import com.trs.ai.moye.monitor.service.OdsMonitorService;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.monitor.enums.MonitorConfigType;
import java.time.LocalDateTime;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

/**
 * OdsMonitorControllerTest
 *
 * <AUTHOR>
 * @since 2025/3/7 15:12
 */
@ExtendWith(MockitoExtension.class)
class OdsMonitorControllerTest {

    @InjectMocks
    private OdsMonitorController odsMonitorController;

    private MockMvc mockMvc;

    @Mock
    private OdsMonitorService odsMonitorService;

    @BeforeEach
    public void setUp(){
        mockMvc = MockMvcBuilders.standaloneSetup(odsMonitorController)
            .setControllerAdvice(new CustomerResponseBodyConvert(), new GlobalExceptionProcessor())
            .setMessageConverters(new MappingJackson2HttpMessageConverter(JsonUtils.OBJECT_MAPPER))
            .build();
    }

    @Test
    void testOdsMonitorDetailPeriods_Lag() throws Exception {
        // 模拟请求参数
        MonitorConfigType monitorType = MonitorConfigType.LAG;
        Integer dataModelId = 1;
        MonitorEventPeriodRequest request = new MonitorEventPeriodRequest();

        // 创建模拟的响应数据
        MonitorEventPeriodResponse responseItem = new MonitorEventPeriodResponse();
        responseItem.setStartTime(LocalDateTime.now());
        responseItem.setEndTime(LocalDateTime.now().plusHours(1));
        responseItem.setMonitorCount(10L);
        PageResponse<MonitorEventPeriodResponse> expectedResponse = new PageResponse<>();
        expectedResponse.setTotal(1);
        expectedResponse.setItems(List.of(responseItem));
        expectedResponse.setPageNum(1);
        expectedResponse.setPageSize(10);

        // 模拟服务层返回
        when(odsMonitorService.odsMonitorDetailPeriods(monitorType, dataModelId, request)).thenReturn(expectedResponse);

        // 执行测试方法
        String responseContent = mockMvc.perform(
                post(String.format("/monitor/data-model/%s/detail/periods/%s", monitorType, dataModelId))
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JsonUtils.toJsonString(request)))
            .andExpect(status().isOk())
            .andReturn().getResponse().getContentAsString();

        // 验证结果
        ResponseMessage responseMessage = JsonUtils.parseObject(responseContent, ResponseMessage.class);
        assertNotNull(responseMessage);
        assertTrue(responseMessage.isSuccess());

        PageResponse<?> actualResponse = JsonUtils.getObjectMapper()
            .convertValue(responseMessage.getData(), PageResponse.class);
        assertEquals(expectedResponse.getTotal(), actualResponse.getTotal());
        assertEquals(expectedResponse.getPageNum(), actualResponse.getPageNum());
        assertEquals(expectedResponse.getPageSize(), actualResponse.getPageSize());
    }

}