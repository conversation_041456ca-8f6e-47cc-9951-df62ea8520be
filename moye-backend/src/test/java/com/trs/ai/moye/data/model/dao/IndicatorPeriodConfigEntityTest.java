package com.trs.ai.moye.data.model.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.indicator.entity.DailyStatRange;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfig;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfigEntity;
import com.trs.moye.base.data.indicator.entity.IntervalStatRange;
import com.trs.moye.base.data.indicator.entity.MonthlyStatRange;
import com.trs.moye.base.data.indicator.entity.StatRange;
import com.trs.moye.base.data.indicator.entity.WeeklyStatRange;
import com.trs.moye.base.data.indicator.enums.IndicatorPeriodType;
import java.time.LocalTime;
import java.util.Map;
import org.junit.jupiter.api.Test;

class IndicatorPeriodConfigEntityTest {

    @Test
    void testDailyConfigEntityDeserialization() {
        String json = """
            {
              "periodType": "DAILY",
              "config": {
                "trigger": {
                  "time": "00:00:00"
                },
                "statRange": {
                  "endTime": "23:59:59",
                  "startTime": "00:00:00"
                }
              },
              "updateTime": "2025-05-20 05:53:12"
            }
            """;

        IndicatorPeriodConfigEntity entity = JsonUtils.parseObject(json, IndicatorPeriodConfigEntity.class);

        assertNotNull(entity);
        assertEquals(IndicatorPeriodType.DAILY, entity.getPeriodType());
        assertNotNull(entity.getConfig());
        assertNotNull(entity.getUpdateTime());

        IndicatorPeriodConfig<?> config = entity.getConfig();
        assertInstanceOf(DailyStatRange.class, config.getStatRange());

        DailyStatRange range = (DailyStatRange) config.getStatRange();
        assertEquals(LocalTime.MIN, range.getStartTime());
        assertEquals(LocalTime.MAX, range.getEndTime());

        assertEquals(LocalTime.MIN, config.getTrigger().getTime());
    }

    @Test
    void testWeeklyConfigEntityDeserialization() {
        String json = """
            {
              "periodType": "WEEKLY",
              "config": {
                "trigger": {
                  "day": 1,
                  "time": "00:00:00"
                },
                "statRange": {
                  "endDayOfWeek": 7,
                  "endTime": "23:59:59",
                  "startDayOfWeek": 1,
                  "startTime": "00:00:00"
                }
              },
              "updateTime": "2025-05-20 05:53:12"
            }
            """;

        IndicatorPeriodConfigEntity entity = JsonUtils.parseObject(json, IndicatorPeriodConfigEntity.class);

        assertNotNull(entity);
        assertEquals(IndicatorPeriodType.WEEKLY, entity.getPeriodType());

        IndicatorPeriodConfig<?> config = entity.getConfig();
        assertInstanceOf(WeeklyStatRange.class, config.getStatRange());

        WeeklyStatRange range = (WeeklyStatRange) config.getStatRange();
        assertEquals(1, range.getStartDayOfWeek().getValue());
        assertEquals(7, range.getEndDayOfWeek().getValue());
        assertEquals(LocalTime.MIN, range.getStartTime());
        assertEquals(LocalTime.MAX, range.getEndTime());

        assertEquals("1", config.getTrigger().getDay());
        assertEquals(LocalTime.of(0, 0, 0), config.getTrigger().getTime());
    }

    @Test
    void testMonthlyConfigEntityDeserialization() {
        String json = """
            {
              "periodType": "MONTHLY",
              "config": {
                "trigger": {
                  "day": 1,
                  "time": "00:00:00"
                },
                "statRange": {
                  "endDay": "last_day",
                  "endTime": "23:59:59",
                  "startDay": 1,
                  "startTime": "00:00:00"
                }
              },
              "updateTime": "2025-05-20 05:53:12"
            }
            """;

        IndicatorPeriodConfigEntity entity = JsonUtils.parseObject(json, IndicatorPeriodConfigEntity.class);

        assertNotNull(entity);
        assertEquals(IndicatorPeriodType.MONTHLY, entity.getPeriodType());

        IndicatorPeriodConfig<?> config = entity.getConfig();
        assertInstanceOf(MonthlyStatRange.class, config.getStatRange());

        MonthlyStatRange range = (MonthlyStatRange) config.getStatRange();
        assertEquals(1, range.getStartDay());
        assertEquals("last_day", range.getEndDay());
        assertEquals(LocalTime.MIN, range.getStartTime());
        assertEquals(LocalTime.MAX, range.getEndTime());

        assertEquals("1", config.getTrigger().getDay());
        assertEquals(LocalTime.MIN, config.getTrigger().getTime());
    }

    @Test
    void testQuarterlyConfigEntityDeserialization() {
        String json = """
            {
              "periodType": "QUARTERLY",
              "config": {
                "trigger": {
                  "day": 1,
                  "time": "09:00:00",
                  "offsetMonths": 1
                },
                "statRange": {
                  "endDay": "last_day",
                  "endTime": "23:59:59",
                  "startDay": 1,
                  "startTime": "00:00:00",
                  "startMonth": 1,
                  "durationMonths": 3
                }
              },
              "updateTime": "2025-05-20 05:53:12"
            }
            """;

        IndicatorPeriodConfigEntity entity = JsonUtils.parseObject(json, IndicatorPeriodConfigEntity.class);

        assertNotNull(entity);
        assertEquals(IndicatorPeriodType.QUARTERLY, entity.getPeriodType());

        IndicatorPeriodConfig<?> config = entity.getConfig();
        assertInstanceOf(IntervalStatRange.class, config.getStatRange());

        IntervalStatRange range = (IntervalStatRange) config.getStatRange();
        assertEquals(1, range.getStartDay());
        assertEquals("last_day", range.getEndDay());
        assertEquals(1, range.getStartMonth());
        assertEquals(3, range.getDurationMonths());
        assertEquals(LocalTime.MIN, range.getStartTime());
        assertEquals(LocalTime.MAX, range.getEndTime());

        assertEquals("1", config.getTrigger().getDay());
        assertEquals(1, config.getTrigger().getOffsetMonths());
        assertEquals(LocalTime.of(9, 0, 0), config.getTrigger().getTime());
    }

    @Test
    void testSemiAnnuallyAndYearlyConfigEntityDeserialization() {
        // 半年度配置
        String semiAnnuallyJson = """
            {
              "periodType": "SEMIANNUALLY",
              "config": {
                "trigger": {
                  "day": 1,
                  "time": "00:00:00",
                  "offsetMonths": 1
                },
                "statRange": {
                  "endDay": "last_day",
                  "endTime": "23:59:59",
                  "startDay": 1,
                  "startTime": "00:00:00",
                  "startMonth": 1,
                  "durationMonths": 6
                }
              },
              "updateTime": "2025-05-20 05:53:12"
            }
            """;

        // 测试半年度配置
        IndicatorPeriodConfigEntity semiEntity = JsonUtils.parseObject(semiAnnuallyJson,
            IndicatorPeriodConfigEntity.class);
        assertNotNull(semiEntity);
        assertEquals(IndicatorPeriodType.SEMIANNUALLY, semiEntity.getPeriodType());
        IntervalStatRange semiRange = (IntervalStatRange) semiEntity.getConfig().getStatRange();
        assertEquals(6, semiRange.getDurationMonths());

        // 测试年度配置
        // 年度配置
        String yearlyJson = """
            {
              "periodType": "YEARLY",
              "config": {
                "trigger": {
                  "day": 1,
                  "time": "00:00:00",
                  "offsetMonths": 1
                },
                "statRange": {
                  "endDay": "last_day",
                  "endTime": "23:59:59",
                  "startDay": 1,
                  "startTime": "00:00:00",
                  "startMonth": 1,
                  "durationMonths": 12
                }
              },
              "updateTime": "2025-05-20 05:53:12"
            }
            """;
        IndicatorPeriodConfigEntity yearlyEntity = JsonUtils.parseObject(yearlyJson, IndicatorPeriodConfigEntity.class);
        assertNotNull(yearlyEntity);
        assertEquals(IndicatorPeriodType.YEARLY, yearlyEntity.getPeriodType());
        IntervalStatRange yearlyRange = (IntervalStatRange) yearlyEntity.getConfig().getStatRange();
        assertEquals(12, yearlyRange.getDurationMonths());
    }

    @Test
    void testAllConfigsFromDatabase() {
        // 模拟数据库中的所有配置数据
        Map<String, String> dbConfigs = Map.of(
            "DAILY", """
                {"trigger": {"time": "00:00:00"}, "statRange": {"endTime": "23:59:59", "startTime": "00:00:00"}}""",
            "WEEKLY", """
                {"trigger": {"day": 1, "time": "00:00:00"}, "statRange": {"endDayOfWeek": 7, "endTime": "23:59:59", "startDayOfWeek": 1, "startTime": "00:00:00"}}""",
            "MONTHLY", """
                {"trigger": {"day": 1, "time": "00:00:00"}, "statRange": {"endDay": "last_day", "endTime": "23:59:59", "startDay": 1, "startTime": "00:00:00"}}""",
            "QUARTERLY", """
                {"trigger": {"day": 1, "time": "09:00:00", "offsetMonths": 1}, "statRange": {"endDay": "last_day", "endTime": "23:59:59", "startDay": 1, "startTime": "00:00:00", "startMonth": 1, "durationMonths": 3}}""",
            "SEMIANNUALLY", """
                {"trigger": {"day": 1, "time": "00:00:00", "offsetMonths": 1}, "statRange": {"endDay": "last_day", "endTime": "23:59:59", "startDay": 1, "startTime": "00:00:00", "startMonth": 1, "durationMonths": 6}}""",
            "YEARLY", """
                {"trigger": {"day": 1, "time": "00:00:00", "offsetMonths": 1}, "statRange": {"endDay": "last_day", "endTime": "23:59:59", "startDay": 1, "startTime": "00:00:00", "startMonth": 1, "durationMonths": 12}}"""
        );

        // 测试每种配置类型
        for (Map.Entry<String, String> entry : dbConfigs.entrySet()) {
            String periodTypeStr = entry.getKey();
            String configJson = entry.getValue();
            IndicatorPeriodType periodType = IndicatorPeriodType.valueOf(periodTypeStr);

            // 创建一个完整的实体JSON
            String entityJson = String.format(
                """
                    {
                      "periodType": "%s",
                      "config": %s,
                      "updateTime": "2025-05-20 05:53:12"
                    }
                    """, periodTypeStr, configJson);

            try {
                IndicatorPeriodConfigEntity entity = JsonUtils.parseObject(entityJson,
                    IndicatorPeriodConfigEntity.class);

                assertNotNull(entity);
                assertEquals(periodType, entity.getPeriodType());
                assertNotNull(entity.getConfig());

                StatRange range = entity.getConfig().getStatRange();

                // 验证正确的StatRange类型
                switch (periodType) {
                    case DAILY:
                        assertInstanceOf(DailyStatRange.class, range);
                        break;
                    case WEEKLY:
                        assertInstanceOf(WeeklyStatRange.class, range);
                        break;
                    case MONTHLY:
                        assertInstanceOf(MonthlyStatRange.class, range);
                        break;
                    case QUARTERLY:
                    case SEMIANNUALLY:
                    case YEARLY:
                        assertInstanceOf(IntervalStatRange.class, range);
                        IntervalStatRange intervalRange = (IntervalStatRange) range;
                        if (periodType == IndicatorPeriodType.QUARTERLY) {
                            assertEquals(3, intervalRange.getDurationMonths());
                        } else if (periodType == IndicatorPeriodType.SEMIANNUALLY) {
                            assertEquals(6, intervalRange.getDurationMonths());
                        } else {
                            assertEquals(12, intervalRange.getDurationMonths());
                        }
                        break;
                    default:
                        break;
                }
            } catch (Exception e) {
                throw new AssertionError("反序列化失败: " + periodTypeStr, e);
            }
        }
    }
}