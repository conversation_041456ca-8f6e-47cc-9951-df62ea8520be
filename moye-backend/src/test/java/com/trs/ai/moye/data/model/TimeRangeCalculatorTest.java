package com.trs.ai.moye.data.model;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.trs.ai.moye.out.entity.OutIndicatorTimeRangeParams;
import com.trs.ai.moye.out.request.OutIndicatorTimeRangeRequest;
import com.trs.ai.moye.out.util.indicator.TimeRangeCalculator;
import com.trs.moye.base.data.indicator.entity.DailyStatRange;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfig;
import com.trs.moye.base.data.indicator.entity.IntervalStatRange;
import com.trs.moye.base.data.indicator.entity.MonthlyStatRange;
import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.entity.TriggerConfig;
import com.trs.moye.base.data.indicator.entity.WeeklyStatRange;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

class TimeRangeCalculatorTest {

    private IndicatorPeriodConfig<WeeklyStatRange> weeklyConfig;
    private IndicatorPeriodConfig<MonthlyStatRange> monthlyConfig;

    @BeforeEach
    void setUp() {

        // 周统计配置：每周四16:00触发，统计上周四16:00到本周四16:00
        weeklyConfig = new IndicatorPeriodConfig<>(
            new WeeklyStatRange(
                DayOfWeek.THURSDAY,
                LocalTime.of(16, 0),
                DayOfWeek.THURSDAY,
                LocalTime.of(16, 0)
            ),
            new TriggerConfig(
                null,
                null,
                DayOfWeek.THURSDAY,
                LocalTime.of(16, 0)
            )
        );

        // 月统计配置：每月5日12:00触发，统计上月1日到最后一天
        monthlyConfig = new IndicatorPeriodConfig<>(
            new MonthlyStatRange(
                1,
                LocalTime.MIN,
                "last_day",
                LocalTime.MAX
            ),
            new TriggerConfig(
                1,
                "5",
                null,
                LocalTime.NOON
            )
        );
    }

    // ==================== 周统计测试 ====================

    @Test
    void testWeeklySinglePeriod() {
        // 构造2025年第20周参数
        OutIndicatorTimeRangeParams param = new OutIndicatorTimeRangeParams();
        param.setYear(2025);
        param.setWeek(20);

        StatisticPeriod result = TimeRangeCalculator.calculatePeriod(param, weeklyConfig);

        // 预期：2025年5月8日16:00 到 2025年5月15日16:00
        // 触发时间：2025年5月15日16:00（第20周的周四）
        // 统计范围：上周四16:00（5月8日）到本周四16:00（5月15日）
        LocalDateTime expectedStart = LocalDateTime.of(2025, 5, 8, 16, 0);
        LocalDateTime expectedEnd = LocalDateTime.of(2025, 5, 15, 16, 0);

        assertEquals(expectedStart, result.getStartTime());
        assertEquals(expectedEnd, result.getEndTime());
    }

    @Test
    void testWeeklyRange() {
        OutIndicatorTimeRangeRequest request = new OutIndicatorTimeRangeRequest();

        // 开始：2025年第20周
        OutIndicatorTimeRangeParams beginParam = new OutIndicatorTimeRangeParams();
        beginParam.setYear(2025);
        beginParam.setWeek(20);
        request.setBeginTime(beginParam);

        // 结束：2025年第22周
        OutIndicatorTimeRangeParams endParam = new OutIndicatorTimeRangeParams();
        endParam.setYear(2025);
        endParam.setWeek(22);
        request.setEndTime(endParam);

        StatisticPeriod result = TimeRangeCalculator.calculateRange(request, weeklyConfig);

        // 预期范围：第20周开始时间到第22周结束时间
        // 20周开始：2025-05-08 16:00
        // 22周结束：2025-05-29 16:00
        LocalDateTime expectedStart = LocalDateTime.of(2025, 5, 8, 16, 0);
        LocalDateTime expectedEnd = LocalDateTime.of(2025, 5, 29, 16, 0);

        assertEquals(expectedStart, result.getStartTime());
        assertEquals(expectedEnd, result.getEndTime());
    }

    // ==================== 月统计测试 ====================

    @Test
    void testMonthlySinglePeriod() {
        // 构造2025年5月参数
        OutIndicatorTimeRangeParams param = new OutIndicatorTimeRangeParams();
        param.setYear(2025);
        param.setMonth(5);

        StatisticPeriod result = TimeRangeCalculator.calculatePeriod(param, monthlyConfig);

        // 预期：统计2025年5月数据
        // 触发时间：2025年6月5日12:00（配置的触发时间）
        // 统计范围：5月1日00:00 到 6月1日00:00
        LocalDateTime expectedStart = LocalDateTime.of(2025, 5, 1, 0, 0);
        LocalDateTime expectedEnd = LocalDateTime.of(2025, 6, 1, 0, 0);

        assertEquals(expectedStart, result.getStartTime());
        assertEquals(expectedEnd, result.getEndTime());
    }

    @Test
    void testMonthlySinglePeriod2() {
        // 构造2025年1月参数
        OutIndicatorTimeRangeParams param = new OutIndicatorTimeRangeParams();
        param.setYear(2025);
        param.setMonth(1);
        IndicatorPeriodConfig<?> config = new IndicatorPeriodConfig<>(
            new MonthlyStatRange(
                21,
                LocalTime.of(16, 0),
                "21",
                LocalTime.of(16, 0)
            ),
            new TriggerConfig(
                0,
                "21",
                null,
                LocalTime.of(16, 0)
            )
        );
        StatisticPeriod result = TimeRangeCalculator.calculatePeriod(param, config);

        // 预期：统计2025年1月数据
        // 触发时间：2025年1月21日16:00（配置的触发时间）
        // 统计范围：2024年12月21日16:00 到 2025年1月21日16:00
        LocalDateTime expectedStart = LocalDateTime.of(2024, 12, 21, 16, 0);
        LocalDateTime expectedEnd = LocalDateTime.of(2025, 1, 21, 16, 0);

        assertEquals(expectedStart, result.getStartTime());
        assertEquals(expectedEnd, result.getEndTime());
    }

    @Test
    void testMonthlyRange() {
        OutIndicatorTimeRangeRequest request = new OutIndicatorTimeRangeRequest();

        // 开始：2025年1月
        OutIndicatorTimeRangeParams beginParam = new OutIndicatorTimeRangeParams();
        beginParam.setYear(2025);
        beginParam.setMonth(1);
        request.setBeginTime(beginParam);

        // 结束：2025年3月
        OutIndicatorTimeRangeParams endParam = new OutIndicatorTimeRangeParams();
        endParam.setYear(2025);
        endParam.setMonth(3);
        request.setEndTime(endParam);

        StatisticPeriod result = TimeRangeCalculator.calculateRange(request, monthlyConfig);

        // 预期范围：
        // 开始：2025-1-01 00:00 (2025年1月对应的统计周期开始时间)
        // 结束：2025-04-01 00:00 (2025年3月对应的统计周期结束时间)
        LocalDateTime expectedStart = LocalDateTime.of(2025, 1, 1, 0, 0);
        LocalDateTime expectedEnd = LocalDateTime.of(2025, 4, 1, 0, 0);

        assertEquals(expectedStart, result.getStartTime());
        assertEquals(expectedEnd, result.getEndTime());
    }

    // ==================== 季度统计测试 ====================

    @Test
    void testQuarterlySinglePeriod() {
        // 创建季度统计配置
        IndicatorPeriodConfig<?> config = new IndicatorPeriodConfig<>(
            new IntervalStatRange(
                1, 1, LocalTime.MIN, 3, "last_day", LocalTime.MAX
            ),
            new TriggerConfig(
                1, "10", null, LocalTime.of(9, 0)
            )
        );

        // 构造2025年第2季度参数
        OutIndicatorTimeRangeParams param = new OutIndicatorTimeRangeParams();
        param.setYear(2025);
        param.setQuarter(2);

        StatisticPeriod result = TimeRangeCalculator.calculatePeriod(param, config);

        // 预期：统计2025年4-6月数据
        // 触发时间：2025年7月10日09:00（结束月+1个月offset）
        // 统计范围：4月1日00:00 到 7月1日00:00
        LocalDateTime expectedStart = LocalDateTime.of(2025, 4, 1, 0, 0);
        LocalDateTime expectedEnd = LocalDateTime.of(2025, 7, 1, 0, 0);

        assertEquals(expectedStart, result.getStartTime());
        assertEquals(expectedEnd, result.getEndTime());
    }

    // ==================== 半年统计测试 ====================

    @Test
    void testSemiannualSinglePeriod() {
        // 创建半年统计配置
        IndicatorPeriodConfig<?> config = new IndicatorPeriodConfig<>(
            new IntervalStatRange(
                1, 1, LocalTime.MIN, 6, "last_day", LocalTime.MAX
            ),
            new TriggerConfig(
                1, "10", null, LocalTime.of(9, 0)
            )
        );

        // 构造2025年下半年参数
        OutIndicatorTimeRangeParams param = new OutIndicatorTimeRangeParams();
        param.setYear(2025);
        param.setSemiannual(2);

        StatisticPeriod result = TimeRangeCalculator.calculatePeriod(param, config);

        // 预期：统计2025年7-12月数据
        // 触发时间：2026年1月10日09:00（结束月+1个月offset）
        // 统计范围：7月1日00:00 到 2026年1月1日00:00
        LocalDateTime expectedStart = LocalDateTime.of(2025, 7, 1, 0, 0);
        LocalDateTime expectedEnd = LocalDateTime.of(2026, 1, 1, 0, 0);

        assertEquals(expectedStart, result.getStartTime());
        assertEquals(expectedEnd, result.getEndTime());
    }

    // ==================== 日期格式测试 ====================

    @Test
    void testDailySinglePeriod() {
        // 创建日统计配置
        IndicatorPeriodConfig<?> config = new IndicatorPeriodConfig<>(
            new DailyStatRange(LocalTime.MIN, LocalTime.MAX),
            new TriggerConfig(null, null, null, LocalTime.of(12, 30))
        );

        // 构造具体日期参数
        OutIndicatorTimeRangeParams param = new OutIndicatorTimeRangeParams();
        param.setDate("2025-05-28");

        StatisticPeriod result = TimeRangeCalculator.calculatePeriod(param, config);

        // 预期：统计前一天数据
        // 触发时间：2025-05-28 12:30
        // 统计范围：2025-05-27 00:00 到 2025-05-28 00:00
        LocalDateTime expectedStart = LocalDateTime.of(2025, 5, 27, 0, 0);
        LocalDateTime expectedEnd = LocalDateTime.of(2025, 5, 28, 0, 0);

        assertEquals(expectedStart, result.getStartTime());
        assertEquals(expectedEnd, result.getEndTime());
    }

    @Test
    void testDailyRange() {

        OutIndicatorTimeRangeRequest request = new OutIndicatorTimeRangeRequest();

        // 开始：2025-05-20
        OutIndicatorTimeRangeParams beginParam = new OutIndicatorTimeRangeParams();
        beginParam.setDate("2025-05-20");
        request.setBeginTime(beginParam);

        // 结束：2025-05-22
        OutIndicatorTimeRangeParams endParam = new OutIndicatorTimeRangeParams();
        endParam.setDate("2025-05-22");
        request.setEndTime(endParam);

        // 创建日统计配置
        IndicatorPeriodConfig<?> config = new IndicatorPeriodConfig<>(
            new DailyStatRange(LocalTime.MIN, LocalTime.MAX),
            new TriggerConfig(null, null, null, LocalTime.of(12, 30))
        );
        StatisticPeriod result = TimeRangeCalculator.calculateRange(request, config);

        // 预期范围：
        // 开始：2025-05-19 00:00 (2025-05-20对应的前一天开始)
        // 结束：2025-05-22 00:00 (2025-05-22对应的前一天结束)
        LocalDateTime expectedStart = LocalDateTime.of(2025, 5, 19, 0, 0);
        LocalDateTime expectedEnd = LocalDateTime.of(2025, 5, 22, 0, 0);

        assertEquals(expectedStart, result.getStartTime());
        assertEquals(expectedEnd, result.getEndTime());
    }

    // ==================== 特殊配置测试 ====================

    @Test
    void testQuarterlyCustomConfig() {
        // 使用数据库中的季度配置（12月起始）
        IndicatorPeriodConfig<?> config = new IndicatorPeriodConfig<>(
            new IntervalStatRange(
                12, 21, LocalTime.of(16, 0), 3, "21", LocalTime.of(16, 0)
            ),
            new TriggerConfig(
                0, "21", null, LocalTime.of(16, 0)
            )
        );

        // 2025年第1季度（对于12月起始，Q1=12月-3月）
        OutIndicatorTimeRangeParams param = new OutIndicatorTimeRangeParams();
        param.setYear(2025);
        param.setQuarter(1);

        StatisticPeriod result = TimeRangeCalculator.calculatePeriod(param, config);

        // 预期：统计2024年12月21日16:00 到 2025年3月21日16:00
        LocalDateTime expectedStart = LocalDateTime.of(2024, 12, 21, 16, 0);
        LocalDateTime expectedEnd = LocalDateTime.of(2025, 3, 21, 16, 0);

        assertEquals(expectedStart, result.getStartTime());
        assertEquals(expectedEnd, result.getEndTime());
    }

    @Test
    void testSemiannualCustomConfig() {
        // 使用数据库中的半年配置（12月起始）
        IndicatorPeriodConfig<?> config = new IndicatorPeriodConfig<>(
            new IntervalStatRange(
                12, 21, LocalTime.of(16, 0), 6, "21", LocalTime.of(16, 0)
            ),
            new TriggerConfig(
                0, "21", null, LocalTime.of(16, 0)
            )
        );

        // 2025年第1个半年（对于12月起始，H1=12月-5月）
        OutIndicatorTimeRangeParams param = new OutIndicatorTimeRangeParams();
        param.setYear(2025);
        param.setSemiannual(1);

        StatisticPeriod result = TimeRangeCalculator.calculatePeriod(param, config);

        // 预期：统计2024年12月21日16:00 到 2025年5月21日16:00
        LocalDateTime expectedStart = LocalDateTime.of(2024, 12, 21, 16, 0);
        LocalDateTime expectedEnd = LocalDateTime.of(2025, 6, 21, 16, 0);

        assertEquals(expectedStart, result.getStartTime());
        assertEquals(expectedEnd, result.getEndTime());
    }

    // ==================== 异常情况测试 ====================

    @Test
    void testMissingParam() {
        OutIndicatorTimeRangeParams param = new OutIndicatorTimeRangeParams();

        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> TimeRangeCalculator.calculatePeriod(param, weeklyConfig)
        );

        assertEquals("按周统计时，year和week参数不能为空", exception.getMessage());
    }

    @ParameterizedTest
    @MethodSource("provideBoundaryCases")
    void testBoundaryCases(OutIndicatorTimeRangeParams param,
        LocalDateTime expectedStart,
        LocalDateTime expectedEnd) {

        StatisticPeriod result = TimeRangeCalculator.calculatePeriod(param, weeklyConfig);

        assertEquals(expectedStart, result.getStartTime());
        assertEquals(expectedEnd, result.getEndTime());
    }

    private static Stream<Arguments> provideBoundaryCases() {
        return Stream.of(
            // 闰年测试 (2024年第9周)
            Arguments.of(
                createWeekParam(2024, 9),
                // 触发时间: 2024-02-29 16:00 (第9周周四)
                // 统计范围: 2024-02-22 16:00 (前一周周四) 到 2024-02-29 16:00
                LocalDateTime.of(2024, 2, 22, 16, 0),
                LocalDateTime.of(2024, 2, 29, 16, 0)
            ),

            // 年度第一周 (2025年第1周)
            Arguments.of(
                createWeekParam(2025, 1),
                // 触发时间: 2025-1-2 16:00
                // 统计范围: 2024-12-26 16:00 到 2025-1-2 16:00
                LocalDateTime.of(2024, 12, 26, 16, 0),
                LocalDateTime.of(2025, 1, 2, 16, 0)
            ),

            // 年度最后一周 (2025年第52周)
            Arguments.of(
                createWeekParam(2025, 52),
                // 触发时间: 2025-12-25 16:00
                // 统计范围: 2025-12-18 16:00 到 2025-12-25 16:00
                LocalDateTime.of(2025, 12, 18, 16, 0),
                LocalDateTime.of(2025, 12, 25, 16, 0)
            ),

            // 跨年周 (2026年第1周)
            Arguments.of(
                createWeekParam(2026, 1),
                // 触发时间: 2026-1-1 16:00
                // 统计范围: 2025-12-25 16:00 到 2026-1-1 16:00
                LocalDateTime.of(2025, 12, 25, 16, 0),
                LocalDateTime.of(2026, 1, 1, 16, 0)
            )
        );
    }

    private static OutIndicatorTimeRangeParams createWeekParam(int year, int week) {
        OutIndicatorTimeRangeParams param = new OutIndicatorTimeRangeParams();
        param.setYear(year);
        param.setWeek(week);
        return param;
    }
}