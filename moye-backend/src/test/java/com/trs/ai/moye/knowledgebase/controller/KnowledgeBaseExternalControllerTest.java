package com.trs.ai.moye.knowledgebase.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.trs.ai.moye.common.web.response.CustomerResponseBodyConvert;
import com.trs.ai.moye.common.web.response.GlobalExceptionProcessor;
import com.trs.ai.moye.knowledgebase.request.external.ExternalAddDataRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalAttrDataRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalConditionRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalDeleteDataRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalMultiSearchRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalPageListRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalUpdateDataRequest;
import com.trs.ai.moye.knowledgebase.service.KnowledgeBaseExternalDataService;
import com.trs.moye.base.common.request.PageParams;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.knowledgebase.entity.EntityColumn;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

/**
 * KnowledgeBaseExternalControllerTest
 *
 * <AUTHOR>
 * @since 2025/6/5 15:32
 */
@ExtendWith(MockitoExtension.class)
class KnowledgeBaseExternalControllerTest {

    @InjectMocks
    private KnowledgeBaseExternalController knowledgeBaseExternalController;

    @Mock
    private KnowledgeBaseExternalDataService service;

    private MockMvc mockMvc;

    @BeforeEach
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(knowledgeBaseExternalController)
            .setControllerAdvice(new CustomerResponseBodyConvert(), new GlobalExceptionProcessor())
            .setMessageConverters(new MappingJackson2HttpMessageConverter(JsonUtils.OBJECT_MAPPER))
            .build();
    }


    /**
     * {@link KnowledgeBaseExternalController#getDataPageList(ExternalPageListRequest)}
     */
    @Test
    void getDataPageList_validRequest() throws Exception {
        ExternalPageListRequest request = new ExternalPageListRequest();
        request.setType(KnowledgeBaseType.ENTITY);
        request.setEnName("testEntity");

        PageResponse<Map<String, Object>> mockResponse = new PageResponse<>();
        when(service.getPageList(any(ExternalPageListRequest.class))).thenReturn(mockResponse);

        mockMvc.perform(post("/knowledge-base/external/data/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(request)))
            .andExpect(status().isOk());
    }

    @Test
    void getDataPageList_invalidRequest() throws Exception {
        // Case 1: Missing type
        ExternalPageListRequest requestMissingType = new ExternalPageListRequest();
        requestMissingType.setType(null);
        requestMissingType.setEnName("testEntity");

        mockMvc.perform(post("/knowledge-base/external/data/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(requestMissingType)))
            .andExpect(status().isInternalServerError());

        // Case 2: Empty enName
        ExternalPageListRequest requestEmptyEnName = new ExternalPageListRequest();
        requestEmptyEnName.setType(KnowledgeBaseType.ENTITY);
        requestEmptyEnName.setEnName("");

        mockMvc.perform(post("/knowledge-base/external/data/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(requestEmptyEnName)))
            .andExpect(status().isInternalServerError());
    }


    /**
     * {@link KnowledgeBaseExternalController#getConditionPageList(ExternalConditionRequest)}
     */
    @Test
    void getConditionPageList_validRequest() throws Exception {
        ExternalConditionRequest request = new ExternalConditionRequest();
        request.setType(KnowledgeBaseType.ENTITY);
        request.setEnName("testEntity");

        PageResponse<Map<String, Object>> mockResponse = new PageResponse<>();
        when(service.getConditionPageList(any(ExternalConditionRequest.class))).thenReturn(mockResponse);

        mockMvc.perform(post("/knowledge-base/external/data/list/conditions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(request)))
            .andExpect(status().isOk());
    }

    @Test
    void getConditionPageList_invalidRequest() throws Exception {
        // Case 1: Missing type
        ExternalConditionRequest requestMissingType = new ExternalConditionRequest();
        requestMissingType.setType(null);
        requestMissingType.setEnName("testEntity");

        mockMvc.perform(post("/knowledge-base/external/data/list/conditions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(requestMissingType)))
            .andExpect(status().isInternalServerError());

        // Case 2: Empty enName
        ExternalConditionRequest requestEmptyEnName = new ExternalConditionRequest();
        requestEmptyEnName.setType(KnowledgeBaseType.ENTITY);
        requestEmptyEnName.setEnName("");

        mockMvc.perform(post("/knowledge-base/external/data/list/conditions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(requestEmptyEnName)))
            .andExpect(status().isInternalServerError());
    }

    /**
     * {@link KnowledgeBaseExternalController#getMultiSearchPageList(ExternalMultiSearchRequest)}
     */
    @Test
    void getMultiSearchPageList_validRequest() throws Exception {
        ExternalMultiSearchRequest request = new ExternalMultiSearchRequest();
        request.setType(KnowledgeBaseType.ENTITY);
        request.setEnName("testEntity");

        PageResponse<Map<String, Object>> mockResponse = new PageResponse<>();
        when(service.getMultiSearchPageList(any(ExternalMultiSearchRequest.class))).thenReturn(mockResponse);

        mockMvc.perform(post("/knowledge-base/external/data/list/multi-search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(request)))
            .andExpect(status().isOk());
    }

    @Test
    void getMultiSearchPageList_invalidRequest() throws Exception {
        // Case 1: Missing type
        ExternalMultiSearchRequest requestMissingType = new ExternalMultiSearchRequest();
        requestMissingType.setType(null);
        requestMissingType.setEnName("testEntity");
        requestMissingType.setPageParams(new PageParams());

        mockMvc.perform(post("/knowledge-base/external/data/list/multi-search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(requestMissingType)))
            .andExpect(status().isInternalServerError());

        // Case 2: Empty enName
        ExternalMultiSearchRequest requestEmptyEnName = new ExternalMultiSearchRequest();
        requestEmptyEnName.setType(KnowledgeBaseType.ENTITY);
        requestEmptyEnName.setEnName("");
        requestEmptyEnName.setPageParams(new PageParams());

        mockMvc.perform(post("/knowledge-base/external/data/list/multi-search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(requestEmptyEnName)))
            .andExpect(status().isInternalServerError());
    }


    /**
     * {@link KnowledgeBaseExternalController#deleteData(ExternalDeleteDataRequest)}
     */
    @Test
    void deleteData_validRequest() throws Exception {
        ExternalDeleteDataRequest request = new ExternalDeleteDataRequest();
        request.setType(KnowledgeBaseType.ENTITY);
        request.setEnName("testEntity");
        request.setDataIds(Collections.singletonList(1));

        mockMvc.perform(delete("/knowledge-base/external/data")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(request)))
            .andExpect(status().isOk());
    }

    @Test
    void deleteData_invalidRequest() throws Exception {
        // Case 1: Missing type
        ExternalDeleteDataRequest requestMissingType = new ExternalDeleteDataRequest();
        requestMissingType.setType(null);
        requestMissingType.setEnName("testEntity");
        requestMissingType.setDataIds(Collections.singletonList(1));

        mockMvc.perform(delete("/knowledge-base/external/data")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(requestMissingType)))
            .andExpect(status().isInternalServerError());

        // Case 2: Empty enName
        ExternalDeleteDataRequest requestEmptyEnName = new ExternalDeleteDataRequest();
        requestEmptyEnName.setType(KnowledgeBaseType.ENTITY);
        requestEmptyEnName.setEnName("");
        requestEmptyEnName.setDataIds(Collections.singletonList(1));

        mockMvc.perform(delete("/knowledge-base/external/data")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(requestEmptyEnName)))
            .andExpect(status().isInternalServerError());

        // Case 3: Empty dataIds
        ExternalDeleteDataRequest requestEmptyDataIds = new ExternalDeleteDataRequest();
        requestEmptyDataIds.setType(KnowledgeBaseType.ENTITY);
        requestEmptyDataIds.setEnName("testEntity");
        requestEmptyDataIds.setDataIds(Collections.emptyList());

        mockMvc.perform(delete("/knowledge-base/external/data")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(requestEmptyDataIds)))
            .andExpect(status().isInternalServerError());
    }


    /**
     * {@link KnowledgeBaseExternalController#addData(ExternalAddDataRequest)}
     */
    @Test
    void addData_validRequest() throws Exception {
        ExternalAddDataRequest request = new ExternalAddDataRequest();
        request.setType(KnowledgeBaseType.ENTITY);
        request.setEnName("testEntity");
        request.setFieldValueList(List.of(new EntityColumn("name", "value")));

        when(service.addData(request.getType(), request.getEnName(), request.getFieldValueList()))
            .thenReturn(1L);

        mockMvc.perform(post("/knowledge-base/external/data")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(request)))
            .andExpect(status().isOk());
    }

    @Test
    void addData_invalidRequest() throws Exception {
        // Case 1: Missing type
        ExternalAddDataRequest requestMissingType = new ExternalAddDataRequest();
        requestMissingType.setType(null);
        requestMissingType.setEnName("testEntity");
        requestMissingType.setFieldValueList(Collections.emptyList());

        mockMvc.perform(post("/knowledge-base/external/data")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(requestMissingType)))
            .andExpect(status().isInternalServerError());

        // Case 2: Empty enName
        ExternalAddDataRequest requestEmptyEnName = new ExternalAddDataRequest();
        requestEmptyEnName.setType(KnowledgeBaseType.ENTITY);
        requestEmptyEnName.setEnName("");
        requestEmptyEnName.setFieldValueList(Collections.emptyList());

        mockMvc.perform(post("/knowledge-base/external/data")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(requestEmptyEnName)))
            .andExpect(status().isInternalServerError());

        // Case 3: Null fieldValueList
        ExternalAddDataRequest requestNullFieldValueList = new ExternalAddDataRequest();
        requestNullFieldValueList.setType(KnowledgeBaseType.ENTITY);
        requestNullFieldValueList.setEnName("testEntity");
        requestNullFieldValueList.setFieldValueList(null);

        mockMvc.perform(post("/knowledge-base/external/data")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(requestNullFieldValueList)))
            .andExpect(status().isInternalServerError());
    }


    /**
     * {@link KnowledgeBaseExternalController#updateData(ExternalUpdateDataRequest)}
     */
    @Test
    void updateData_validRequest() throws Exception {
        ExternalUpdateDataRequest request = new ExternalUpdateDataRequest();
        request.setType(KnowledgeBaseType.ENTITY);
        request.setEnName("testEntity");
        request.setDataId(1);
        request.setFieldValueList(List.of(new EntityColumn("name", "value")));

        mockMvc.perform(put("/knowledge-base/external/data")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(request)))
            .andExpect(status().isOk());
    }

    @Test
    void updateData_invalidRequest() throws Exception {
        // Case 1: Missing type
        ExternalUpdateDataRequest requestMissingType = new ExternalUpdateDataRequest();
        requestMissingType.setType(null);
        requestMissingType.setEnName("testEntity");
        requestMissingType.setDataId(1);
        requestMissingType.setFieldValueList(Collections.emptyList());

        mockMvc.perform(put("/knowledge-base/external/data")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(requestMissingType)))
            .andExpect(status().isInternalServerError());

        // Case 2: Empty enName
        ExternalUpdateDataRequest requestEmptyEnName = new ExternalUpdateDataRequest();
        requestEmptyEnName.setType(KnowledgeBaseType.ENTITY);
        requestEmptyEnName.setEnName("");
        requestEmptyEnName.setDataId(1);
        requestEmptyEnName.setFieldValueList(Collections.emptyList());

        mockMvc.perform(put("/knowledge-base/external/data")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(requestEmptyEnName)))
            .andExpect(status().isInternalServerError());

        // Case 3: Null dataId
        ExternalUpdateDataRequest requestNullDataId = new ExternalUpdateDataRequest();
        requestNullDataId.setType(KnowledgeBaseType.ENTITY);
        requestNullDataId.setEnName("testEntity");
        requestNullDataId.setDataId(null);
        requestNullDataId.setFieldValueList(Collections.emptyList());

        mockMvc.perform(put("/knowledge-base/external/data")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(requestNullDataId)))
            .andExpect(status().isInternalServerError());
    }

    /**
     * {@link KnowledgeBaseExternalController#getAttrData(ExternalAttrDataRequest)}
     */
    @Test
    void getAttrData_validRequest() throws Exception {
        ExternalAttrDataRequest request = new ExternalAttrDataRequest();
        request.setType(KnowledgeBaseType.ENTITY);
        request.setEnName("testEntity");
        request.setAttrName("attrName");

        List<String> mockResponse = List.of("value1", "value2");
        when(service.getAttrData(any(ExternalAttrDataRequest.class))).thenReturn(mockResponse);

        mockMvc.perform(post("/knowledge-base/external/attr/values")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(request)))
            .andExpect(status().isOk());
    }

    @Test
    void getAttrData_invalidRequest() throws Exception {
        // Case 1: Missing type
        ExternalAttrDataRequest requestMissingType = new ExternalAttrDataRequest();
        requestMissingType.setType(null);
        requestMissingType.setEnName("testEntity");
        requestMissingType.setAttrName("attrName");

        mockMvc.perform(post("/knowledge-base/external/attr/values")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(requestMissingType)))
            .andExpect(status().isInternalServerError());

        // Case 2: Empty enName
        ExternalAttrDataRequest requestEmptyEnName = new ExternalAttrDataRequest();
        requestEmptyEnName.setType(KnowledgeBaseType.ENTITY);
        requestEmptyEnName.setEnName("");
        requestEmptyEnName.setAttrName("attrName");

        mockMvc.perform(post("/knowledge-base/external/attr/values")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(requestEmptyEnName)))
            .andExpect(status().isInternalServerError());

        // Case 3: Empty attrName
        ExternalAttrDataRequest requestEmptyAttrName = new ExternalAttrDataRequest();
        requestEmptyAttrName.setType(KnowledgeBaseType.ENTITY);
        requestEmptyAttrName.setEnName("testEntity");
        requestEmptyAttrName.setAttrName("");

        mockMvc.perform(post("/knowledge-base/external/attr/values")
               .contentType(MediaType.APPLICATION_JSON)
               .content(JsonUtils.toJsonString(requestEmptyAttrName)))
            .andExpect(status().isInternalServerError());
    }
}