package com.trs.ai.moye.data.service.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.ai.moye.data.service.dto.DataServiceDto;
import com.trs.ai.moye.data.service.entity.DataService;
import com.trs.ai.moye.data.service.enums.ServiceCreateMode;
import com.trs.ai.moye.data.service.enums.ServiceHealthStatus;
import com.trs.ai.moye.data.service.enums.ServicePublishStatus;
import com.trs.ai.moye.data.service.request.CheckNameDataServiceRequest;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Resource;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

/**
 * Data Service Mapper 测试
 *
 * <AUTHOR>
 * @since 2024/10/15 14:39:05
 */
@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class DataServiceMapperTest {

    @Resource
    private DataServiceMapper dataServiceMapper;
    @Resource
    private DataStorageMapper dataStorageMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        jdbcTemplate.execute("TRUNCATE TABLE data_service");
    }

    @Test
    void checkName() {
        // Arrange
        DataService dataService1 = buildDataService("Test Code", "Existing Service", 1, 1);
        dataServiceMapper.insert(dataService1);

        // Act
        CheckNameDataServiceRequest dataService2 = new CheckNameDataServiceRequest(null, "Existing Service", 1);
        boolean exists = dataServiceMapper.checkName(dataService2);
        dataService2.setName("Non-existing Category");
        boolean notExists = dataServiceMapper.checkName(dataService2);

        // Assert
        assertTrue(exists);
        assertFalse(notExists);
    }

    @Test
    void selectByCode() {
        // Arrange
        DataService dataService = buildDataService("Test Code", "Test Service", 1, 1);
        dataServiceMapper.insert(dataService);

        // Act
        DataService result = dataServiceMapper.selectByCode(dataService.getCode());

        assertNotNull(result);
        assertEquals("Test Service", result.getName());
        assertEquals(ServiceCreateMode.GUIDE_MODE, result.getCreateMode());
        assertEquals(ServicePublishStatus.UNRELEASED, result.getPublishStatus());
        assertEquals(ServiceHealthStatus.HEALTHY, result.getHealthStatus());
    }

    @Test
    void selectDtoById() {
        // Arrange
        DataStorage dataStorage = new DataStorage();
        dataStorage.setEnName("Test Storage");
        dataStorage.setConnectionId(1);
        dataStorage.setDataModelId(1);
        dataStorageMapper.insert(dataStorage);
        DataService dataService = buildDataService("Test Code", "Test Service", dataStorage.getConnectionId(),
            dataStorage.getId());
        dataServiceMapper.insert(dataService);

        // Act
        DataServiceDto result = dataServiceMapper.selectDtoById(dataService.getId());

        assertNotNull(result);
        assertEquals("Test Service", result.getName());
        assertEquals(ServiceCreateMode.GUIDE_MODE, result.getCreateMode());
        assertEquals(ServicePublishStatus.UNRELEASED, result.getPublishStatus());
        assertEquals(ServiceHealthStatus.HEALTHY, result.getHealthStatus());
        assertEquals("Test Storage", result.getDataStorage().getEnName());
    }

    @NotNull
    private static DataService buildDataService(String code, String name, Integer connectionId, Integer dataStorageId) {
        DataService dataService = new DataService();
        dataService.setCode(code);
        dataService.setName(name);
        dataService.setDescription("Description");
        dataService.setConnectionId(connectionId);
        dataService.setCategoryId(1);
        dataService.setStorageId(dataStorageId);
        dataService.setCreateMode(ServiceCreateMode.GUIDE_MODE);
        dataService.setPublishStatus(ServicePublishStatus.UNRELEASED);
        dataService.setHealthStatus(ServiceHealthStatus.HEALTHY);
        return dataService;
    }

    @Test
    void selectDtoByCode() {
        // Arrange
        DataStorage dataStorage = new DataStorage();
        dataStorage.setEnName("Test Storage");
        dataStorage.setConnectionId(1);
        dataStorage.setDataModelId(1);
        dataStorageMapper.insert(dataStorage);

        DataService dataService = buildDataService("Test Code", "Test Service", dataStorage.getConnectionId(),
            dataStorage.getId());
        dataServiceMapper.insert(dataService);

        // Act
        DataServiceDto result = dataServiceMapper.selectDtoByCode(dataService.getCode());

        assertNotNull(result);
        assertEquals("Test Service", result.getName());
        assertEquals("Test Code", result.getCode());
        assertEquals(ServiceCreateMode.GUIDE_MODE, result.getCreateMode());
        assertEquals(ServicePublishStatus.UNRELEASED, result.getPublishStatus());
        assertEquals(ServiceHealthStatus.HEALTHY, result.getHealthStatus());
        assertEquals("Test Storage", result.getDataStorage().getEnName());
    }

    @Test
    void updatePublishStatusById() {
        // Arrange
        DataService dataService = buildDataService("Test Code", "Test Service", 1, 1);
        dataServiceMapper.insert(dataService);

        // Act
        dataServiceMapper.updatePublishStatusById(dataService.getId(), ServicePublishStatus.RELEASED);

        // Assert
        DataService result = dataServiceMapper.selectById(dataService.getId());
        assertEquals(ServicePublishStatus.RELEASED, result.getPublishStatus());
    }

    @Test
    void selectByStorageIds() {
        // Arrange
        DataService dataService1 = buildDataService("Test Code1", "Test Service1", 1, 1);
        dataServiceMapper.insert(dataService1);

        DataService dataService2 = buildDataService("Test Code2", "Test Service2", 1, 2);
        dataServiceMapper.insert(dataService2);

        // Act
        List<DataService> result = dataServiceMapper.selectByStorageIds(
            Arrays.asList(dataService1.getStorageId(), dataService2.getStorageId()));

        // Assert
        assertEquals(2, result.size());
        assertEquals("Test Service1", result.get(0).getName());
        assertEquals("Test Service2", result.get(1).getName());
    }

    @Test
    void selectDtoByStorageIds() {
        // Arrange
        DataStorage dataStorage1 = new DataStorage();
        dataStorage1.setEnName("Test Storage1");
        dataStorage1.setConnectionId(1);
        dataStorage1.setDataModelId(1);
        dataStorageMapper.insert(dataStorage1);

        DataStorage dataStorage2 = new DataStorage();
        dataStorage2.setEnName("Test Storage2");
        dataStorage2.setConnectionId(1);
        dataStorage2.setDataModelId(1);
        dataStorageMapper.insert(dataStorage2);

        DataService dataService1 = buildDataService("Test Code1", "Test Service1", dataStorage1.getConnectionId(),
            dataStorage1.getId());
        dataServiceMapper.insert(dataService1);

        DataService dataService2 = buildDataService("Test Code2", "Test Service2", dataStorage2.getConnectionId(),
            dataStorage2.getId());
        dataServiceMapper.insert(dataService2);

        // Act
        List<DataServiceDto> result = dataServiceMapper.selectDtoByStorageIds(
            Arrays.asList(dataStorage1.getId(), dataStorage2.getId()));

        // Assert
        assertEquals(2, result.size());
        assertEquals("Test Service1", result.get(0).getName());
        assertEquals("Test Service2", result.get(1).getName());
    }
}