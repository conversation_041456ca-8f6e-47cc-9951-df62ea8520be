package com.trs.ai.moye.backstage.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.trs.ai.moye.backstage.request.NoticeListRequest;
import com.trs.ai.moye.common.web.response.CustomerResponseBodyConvert;
import com.trs.ai.moye.common.web.response.GlobalExceptionProcessor;
import com.trs.moye.base.common.annotaion.validation.SafeSqlField;
import com.trs.moye.base.common.enums.SortOrder;
import com.trs.moye.base.common.request.SortParams;
import com.trs.moye.base.common.response.ResponseMessage;
import com.trs.moye.base.common.utils.JsonUtils;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

/**
 * NoticeCenterControllerTest
 *
 * <AUTHOR>
 * @since 2025/5/29 9:31
 */

@ExtendWith(MockitoExtension.class)
class NoticeCenterControllerTest {

    @InjectMocks
    private NoticeCenterController noticeCenterController;

    private MockMvc mockMvc;

    @BeforeEach
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(noticeCenterController)
            .setControllerAdvice(new CustomerResponseBodyConvert(), new GlobalExceptionProcessor())
            .setMessageConverters(new MappingJackson2HttpMessageConverter(JsonUtils.OBJECT_MAPPER))
            .build();
    }

    /**
     * {@link NoticeCenterController#userList(NoticeListRequest)}
     */
    @Test
    void userList_sqlInjection() throws Exception {
        // 构造请求体，测试sql注入
        NoticeListRequest request = new NoticeListRequest();
        request.setSortParams(new SortParams("publishTime%27%3B", SortOrder.ASC));
        String requestBody = JsonUtils.toJsonString(request);

        // 执行 POST 请求
        String responseContent = mockMvc.perform(
                post("/notice-center/user/list")
                    .contentType("application/json")
                    .content(requestBody))
            .andExpect(status().isInternalServerError())
            .andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);

        // 解析并验证响应
        ResponseMessage responseMessage = JsonUtils.parseObject(responseContent, ResponseMessage.class);
        assertNotNull(responseMessage);
        // 获取注解的默认值
        Method messageMethod = SafeSqlField.class.getMethod("message");
        String defaultValue = (String) messageMethod.getDefaultValue();
        assertEquals(defaultValue, responseMessage.getMessage());
    }
}