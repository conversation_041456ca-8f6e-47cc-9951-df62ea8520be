package com.trs.ai.moye.data.model.dto.arrangement.batch;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.InputBind;
import com.trs.moye.ability.entity.OperatorStyleInfo;
import com.trs.moye.ability.entity.OutputBind;
import com.trs.moye.ability.entity.operator.BatchOperator;
import com.trs.moye.ability.entity.operator.OperatorRowType;
import com.trs.moye.ability.enums.ArrangeNodeType;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.service.entity.Condition;
import com.trs.moye.base.data.service.enums.DataServiceConditionType;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.MethodSource;

class BatchOperatorDTOTest {

    @ParameterizedTest
    @MethodSource("provideBatchOperator")
    void testFromBatchOperator(BatchOperator operator) {
        BatchOperatorDTO dto = BatchOperatorDTO.fromBatchOperator(operator);

        assertNotNull(dto);
        assertEquals(operator.getCanvas(), dto.getCanvas());
        assertEquals(operator.getDisplayId(), dto.getDisplayId());
        assertEquals(operator.getTargetDisplayIds(), dto.getTargetDisplayIds());
        assertEquals(operator.getType(), dto.getType());
        assertEquals(operator.getName(), dto.getName());
        assertEquals(operator.getDesc(), dto.getDesc());
        assertEquals(operator.getEnabled(), dto.getEnabled());
        assertEquals(operator.getConditions(), dto.getConditions());
        assertEquals(operator.getInputFields(), dto.getInputFields());
        assertEquals(operator.getOutputFields(), dto.getOutputFields());
        assertEquals(operator.getAbilityId(), dto.getAbilityId());
        assertEquals(operator.getAbility(), dto.getAbility());
        assertEquals(operator.getInputBind(), dto.getInputBind());
        assertEquals(operator.getOutputBind(), dto.getOutputBind());
        assertEquals(operator.getTableType(), dto.getTableType());
        assertEquals(operator.getTableDataModelId(), dto.getDataModelId());
        assertEquals(operator.getTableStorageId(), dto.getStorageId());
        assertEquals(operator.getTableIsIncrement(), dto.getIsIncrement());
    }

    private static Stream<Arguments> provideBatchOperator() {
        return Stream.of(
            Arguments.of(BatchOperator.builder()
                .canvas(new OperatorStyleInfo(0, 0, 1200, 1000, "blue"))
                .displayId(1L)
                .targetDisplayIds(List.of(2L, 3L))
                .type(ArrangeNodeType.OPERATOR)
                .name("Test Operator")
                .desc("Test Description")
                .enabled(true)
                .conditions(List.of(new Condition(DataServiceConditionType.LOGIC, null, null, null)))
                .inputFields(new OperatorRowType())
                .outputFields(new OperatorRowType())
                .abilityId(100)
                .ability(new Ability())
                .inputBind(new InputBind())
                .outputBind(new OutputBind())
                .tableType(ModelLayer.ODS)
                .tableDataModelId(200)
                .tableStorageId(300)
                .tableIsIncrement(true)
                .build())
        );
    }

    @ParameterizedTest
    @MethodSource("provideBatchOperatorDTO")
    void testToBatchOperator(BatchOperatorDTO dto, Integer arrangementId, Integer dataModelId) {
        BatchOperator operator = dto.toBatchOperator(arrangementId, dataModelId);

        assertNotNull(operator);
        assertEquals(arrangementId, operator.getArrangementId());
        assertEquals(dataModelId, operator.getDataModelId());
        assertEquals(dto.getCanvas(), operator.getCanvas());
        assertEquals(dto.getDisplayId(), operator.getDisplayId());
        assertEquals(dto.getTargetDisplayIds(), operator.getTargetDisplayIds());
        assertEquals(dto.getType(), operator.getType());
        assertEquals(dto.getName(), operator.getName());
        assertEquals(dto.getDesc(), operator.getDesc());
        assertEquals(dto.getEnabled(), operator.getEnabled());
        assertEquals(dto.getConditions(), operator.getConditions());
        assertEquals(dto.getInputFields(), operator.getInputFields());
        assertEquals(dto.getOutputFields(), operator.getOutputFields());
        assertEquals(dto.getAbilityId(), operator.getAbilityId());
        assertEquals(dto.getInputBind(), operator.getInputBind());
        assertEquals(dto.getOutputBind(), operator.getOutputBind());
        assertEquals(dto.getTableType(), operator.getTableType());
        assertEquals(dto.getDataModelId(), operator.getTableDataModelId());
        assertEquals(dto.getStorageId(), operator.getTableStorageId());
        assertEquals(dto.getIsIncrement(), operator.getTableIsIncrement());
        assertEquals(BatchOperator.generateOutputTableName(dto.getType(), dto.getDisplayId(), dto.getName()), operator.getOutputTableName());
    }

    @ParameterizedTest
    @CsvSource({
        // Valid cases
        "TABLE, 1, validName, TABLE_1_validName",
        "OPERATOR, 123456789, operatorName, OPERATOR_123456789_operatorName",

        // Invalid characters
        "TABLE, 2, name_with_special_chars!@#$%^&*()`, TABLE_2_name_with_special_chars_________",
        "OPERATOR, 3, name_with_spaces and tabs, OPERATOR_3_name_with_spaces_and_tabs",
        "TABLE, 4, 中文字符和特殊符号!@#, TABLE_4_CCCCCCCCC___",

        // Long name exceeding 40 characters
        "OPERATOR, 6, veryLongNameExceedingFortyCharacters1234567890, OPERATOR_6_veryLongNameExceedingFortyCha",

        // Edge cases
        "TABLE, 7, _, TABLE_7__",
        "OPERATOR, 8, ., OPERATOR_8__",
        "TABLE, 9, 中文, TABLE_9_CC",
        "OPERATOR, 10, 中文和English123, OPERATOR_10_CCCEnglish123"
    })
    void testGenerateOutputTableName(ArrangeNodeType type, Long displayId, String name, String expected) {
        String result = BatchOperator.generateOutputTableName(type, displayId, name);
        assertEquals(expected, result);
    }

    private static Stream<Arguments> provideBatchOperatorDTO() {
        return Stream.of(
            Arguments.of(BatchOperatorDTO.builder()
                .canvas(new OperatorStyleInfo())
                .displayId(1L)
                .targetDisplayIds(List.of(2L, 3L))
                .type(ArrangeNodeType.OPERATOR)
                .name("Test Operator")
                .desc("Test Description")
                .enabled(true)
                .conditions(List.of(new Condition()))
                .inputFields(new OperatorRowType())
                .outputFields(new OperatorRowType())
                .abilityId(100)
                .inputBind(new InputBind())
                .outputBind(new OutputBind())
                .tableType(ModelLayer.ODS)
                .dataModelId(200)
                .storageId(300)
                .isIncrement(true)
                .build(), 1, 1)
        );
    }
}