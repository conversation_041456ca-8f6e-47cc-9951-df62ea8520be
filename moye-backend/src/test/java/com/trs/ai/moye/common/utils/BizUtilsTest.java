package com.trs.ai.moye.common.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.standard.entity.DataStandardField;
import org.junit.jupiter.api.Test;

class BizUtilsTest {

    @Test
    void toEmptyIfNull() {
        assertEquals("", BizUtils.toEmptyIfNull(null));
        assertEquals("", BizUtils.toEmptyIfNull(""));
        assertEquals("test", BizUtils.toEmptyIfNull("test"));
    }

    @Test
    void getStandardFieldIdentity() {
        DataStandardField field = new DataStandardField();
        field.setId(1);
        field.setZhName("测试字段");
        field.setEnName("test_field");

        String expected = "id：1，中文名称：测试字段，英文名称：test_field";
        assertEquals(expected, BizUtils.getStandardFieldIdentity(field));
    }

    @Test
    void getDataSourceIdentity() {
        DataSourceConfig dataSource = new DataSourceConfig();
        dataSource.setId(1);
        dataSource.setEnName("测试数据源");

        String expected = "主键：1，名称：测试数据源";
        assertEquals(expected, BizUtils.getDataSourceIdentity(dataSource));
    }

    @Test
    void getDataStorageIdentity() {
        DataStorage dataStorage = new DataStorage();
        dataStorage.setId(1);
        dataStorage.setEnName("测试数据存储");

        String expected = "主键：1，名称：测试数据存储";
        assertEquals(expected, BizUtils.getDataStorageIdentity(dataStorage));
    }

    @Test
    void getDataModelIdentity() {
        DataModel dataModel = new DataModel();
        dataModel.setId(1);
        dataModel.setZhName("测试数据模型");

        String expected = "主键：1，名称：测试数据模型";
        assertEquals(expected, BizUtils.getDataModelIdentity(dataModel));
    }
}