package com.trs.ai.moye.batchengine.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.trs.ai.moye.batchengine.entity.BatchEngineTaskParam;
import com.trs.ai.moye.batchengine.feign.BatchEngineFeignService;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.exception.BizException;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


@ExtendWith(MockitoExtension.class)
class BatchEngineServiceImplTest {

    @InjectMocks
    private BatchEngineServiceImpl batchEngineService;

    @Mock
    private BatchEngineFeignService batchEngineFeignService;

    private List<BatchEngineTaskParam> tasks;

    @BeforeEach
    public void setUp() {
        tasks = Collections.singletonList(new BatchEngineTaskParam("1597", ModelLayer.DWD, "测试"));
    }

    @Test
    void testExecuteCodeException() {
        doThrow(new RuntimeException("Test Exception")).when(batchEngineFeignService)
            .execute(tasks);
        Exception exception = assertThrows(BizException.class, () -> batchEngineService.executeCode(tasks));

        assertEquals("执行批处理引擎任务失败!", exception.getMessage());
        verify(batchEngineFeignService, times(1)).execute(tasks);
    }

    @Test
    void testExecuteCodeSuccess() {
        Boolean result = batchEngineService.executeCode(tasks);
        assertTrue(result);
        verify(batchEngineFeignService, times(1)).execute(tasks);
    }


}