package com.trs.ai.moye.monitor.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.trs.ai.moye.common.dao.GroupCount;
import com.trs.moye.base.common.response.ResponseMessage;
import com.trs.ai.moye.common.web.response.CustomerResponseBodyConvert;
import com.trs.ai.moye.common.web.response.GlobalExceptionProcessor;
import com.trs.ai.moye.data.model.dao.DataModelDisplayMapper;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.ai.moye.monitor.response.DashboardResponse;
import com.trs.moye.base.common.utils.JsonUtils;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

/**
 * DashboardControllerTest
 *
 * <AUTHOR>
 * @since 2024/12/18 14:05
 */
@ExtendWith(MockitoExtension.class)
class DashboardControllerTest {

    @InjectMocks
    private DashboardController dashboardController;

    private MockMvc mockMvc;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(dashboardController)
            .setControllerAdvice(new CustomerResponseBodyConvert(), new GlobalExceptionProcessor())
            .setMessageConverters(new MappingJackson2HttpMessageConverter(JsonUtils.OBJECT_MAPPER))
            .build();
    }

    @Mock
    private DataModelDisplayMapper dataModelDisplayMapper;

    @Test
    void getExecuteStatusDashboard() throws Exception {
        Long startNum = 100L;
        Long stopNum = 281L;
        List<GroupCount<ModelExecuteStatus>> mapperResult = new ArrayList<>();
        mapperResult.add(new GroupCount<ModelExecuteStatus>(ModelExecuteStatus.START, startNum));
        mapperResult.add(new GroupCount<ModelExecuteStatus>(ModelExecuteStatus.STOP, stopNum));
        when(dataModelDisplayMapper.selectExecuteStatusCounts(any(ModelLayer.class))).thenReturn(mapperResult);

        String responseContent = mockMvc.perform(
                get(String.format("/monitor/%s/dashboard/execute-status", ModelLayer.DWD)))
            .andExpect(status().is5xxServerError())
            .andReturn().getResponse().getContentAsString();

        ResponseMessage responseMessage = JsonUtils.parseObject(responseContent, ResponseMessage.class);
        assertNotNull(responseMessage);
    }
}