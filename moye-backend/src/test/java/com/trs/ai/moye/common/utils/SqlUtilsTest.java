package com.trs.ai.moye.common.utils;

import com.trs.ai.moye.common.entity.SqlSplitStatement;
import com.trs.moye.base.common.utils.FileUtils;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class SqlUtilsTest {

    @Test
    void splitSqlContent() {
        List<SqlSplitStatement> splitStatementList = SqlUtils.splitSqlContent(
            FileUtils.readClasspathText("sql/split/分割sql测试1.sql"));
        for (SqlSplitStatement splitStatement : splitStatementList) {
            System.out.println(splitStatement.sqlMessage());
        }
        Assertions.assertEquals(5, splitStatementList.size());
    }
}