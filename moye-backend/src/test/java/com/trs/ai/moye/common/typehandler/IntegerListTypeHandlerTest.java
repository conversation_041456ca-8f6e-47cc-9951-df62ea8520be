package com.trs.ai.moye.common.typehandler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class IntegerListTypeHandlerTest {

    private static final BaseTypeHandler<List<Integer>> TYPE_HANDLER = new IntegerListTypeHandler();

    private static final List<Integer> TEST_EXAMPLE = List.of(1, 3, 2);
    private static final String expect = "1,3,2";

    @Mock
    protected ResultSet rs;
    @Mock
    protected PreparedStatement ps;
    @Mock
    protected CallableStatement cs;

    @Test
    void shouldSetNonNullParameter() throws Exception {
        TYPE_HANDLER.setNonNullParameter(ps, 1, TEST_EXAMPLE, JdbcType.VARCHAR);
        verify(ps).setString(1, expect);
    }

    @Test
    void shouldGetResultFromResultSetByName() throws Exception {
        when(rs.getString("column")).thenReturn(expect);
        assertEquals(TEST_EXAMPLE, TYPE_HANDLER.getNullableResult(rs, "column"));
    }

    @Test
    void shouldGetResultNullFromResultSetByName() throws Exception {
        when(rs.getString("column")).thenReturn(null);
        assertTrue(TYPE_HANDLER.getNullableResult(rs, "column").isEmpty());
    }

    @Test
    void shouldGetResultFromResultSetByPosition() throws Exception {
        when(rs.getString(1)).thenReturn(expect);
        assertEquals(TEST_EXAMPLE, TYPE_HANDLER.getNullableResult(rs, 1));
    }

    @Test
    void shouldGetResultNullFromResultSetByPosition() throws Exception {
        when(rs.getString(1)).thenReturn(null);
        assertTrue(TYPE_HANDLER.getNullableResult(rs, 1).isEmpty());
    }

    @Test
    void shouldGetResultFromCallableStatement() throws Exception {
        shouldGetResultFromResultSetByPosition();
    }

    @Test
    void shouldGetResultNullFromCallableStatement() throws Exception {
        when(cs.getString(1)).thenReturn(null);
        assertTrue(TYPE_HANDLER.getNullableResult(cs, 1).isEmpty());
    }
}