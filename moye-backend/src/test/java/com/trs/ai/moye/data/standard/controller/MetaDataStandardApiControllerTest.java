package com.trs.ai.moye.data.standard.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.trs.ai.moye.backstage.service.UserService;
import com.trs.ai.moye.common.web.WebConfiguration;
import com.trs.ai.moye.common.web.interceptor.AuthenticationInterceptor;
import com.trs.ai.moye.common.web.response.CustomerResponseBodyConvert;
import com.trs.ai.moye.common.web.response.GlobalExceptionProcessor;
import com.trs.ai.moye.data.standard.request.MetaDataStandardCreateApiRequest;
import com.trs.ai.moye.data.standard.service.MetaDataStandardApiService;
import com.trs.ai.moye.data.standard.service.MetaDataStandardCatalogService;
import com.trs.ai.moye.data.standard.service.MetaDataStandardService;
import com.trs.ai.moye.permission.properties.SecurityProperties;
import com.trs.ai.moye.permission.service.JwtService;
import com.trs.ai.moye.permission.service.TokenStore;
import com.trs.moye.base.common.utils.JsonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(MockitoExtension.class)
class MetaDataStandardApiControllerTest {

    @InjectMocks
    private MetaDataStandardApiController metaDataStandardApiController;

    @Mock
    private MetaDataStandardApiService metaDataStandardApiService;


    private MockMvc mockMvc;

    @BeforeEach
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(metaDataStandardApiController)
            .setControllerAdvice(new CustomerResponseBodyConvert(), new GlobalExceptionProcessor())
            .setMessageConverters(new MappingJackson2HttpMessageConverter(JsonUtils.OBJECT_MAPPER))
            .build();
    }

    @Test
    void create() throws Exception {
        String requestStr = "{ \"zhName\": \"由外部api创建的元数据标准2\", \"enName\": \"wen\", \"description\": \"hahahaha\", \"catalogId\": 8, \"vidType\": \"int\", \"tag\": [ { \"zhName\": \"人物\", \"enName\": \"person\", \"description\": \"description_cb0fa7dbd48d\", \"fields\": [ { \"zhName\": \"名称\", \"enName\": \"nmae\", \"type\": \"string\", \"notNull\": false, \"defaultValue\": \"qiukeyu\", \"description\": \"\" }, { \"zhName\": \"性别\", \"enName\": \"gender\", \"type\": \"string\", \"notNull\": false, \"defaultValue\": \"man!\", \"description\": \"\" } ] } ], \"edge\": [ { \"zhName\": \"贵物\", \"enName\": \"monster\", \"description\": \"description_cb0fa7dbd48d\", \"fields\": [ { \"zhName\": \"名称\", \"enName\": \"nmae\", \"type\": \"string\", \"notNull\": false, \"defaultValue\": \"qiukeyu\", \"description\": \"\" }, { \"zhName\": \"性别\", \"enName\": \"gender\", \"type\": \"string\", \"notNull\": false, \"defaultValue\": \"man!\", \"description\": \"\" } ] } ] }";

        MetaDataStandardCreateApiRequest request = JsonUtils.parseObject(requestStr,
            MetaDataStandardCreateApiRequest.class);
        when(metaDataStandardApiService.creatGraphicsMetaDataStandard(request)).thenReturn(1);

        mockMvc.perform(post("/out/metadata-standard/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestStr))
            .andExpect(status().isOk())
            .andExpect(result -> {
                String responseBody = result.getResponse().getContentAsString();
                // 断言返回的状态码和内容
                String expected = """
                    {"code":200,"success":true,"data":{"metaDataStandardId":1}}""";
                assertThat(responseBody).isEqualTo(expected);
            });
    }
}