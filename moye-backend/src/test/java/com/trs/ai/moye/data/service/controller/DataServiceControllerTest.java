package com.trs.ai.moye.data.service.controller;

import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.ai.moye.common.web.response.CustomerResponseBodyConvert;
import com.trs.ai.moye.common.web.response.GlobalExceptionProcessor;
import com.trs.ai.moye.data.service.dao.DataServiceCategoryMapper;
import com.trs.ai.moye.data.service.dao.DataServiceMapper;
import com.trs.ai.moye.data.service.dto.DataServiceDto;
import com.trs.ai.moye.data.service.entity.DataService;
import com.trs.ai.moye.data.service.entity.DataServiceCategory;
import com.trs.ai.moye.data.service.entity.DataServiceConfig;
import com.trs.ai.moye.data.service.entity.params.CodeModeParams;
import com.trs.ai.moye.data.service.enums.ServiceConfigType;
import com.trs.ai.moye.data.service.enums.ServiceCreateMode;
import com.trs.ai.moye.data.service.enums.ServicePublishStatus;
import com.trs.ai.moye.data.service.request.CheckNameDataServiceRequest;
import com.trs.ai.moye.data.service.request.CodeBlockRequest;
import com.trs.ai.moye.data.service.request.DataServiceCategoryRequest;
import com.trs.ai.moye.data.service.request.DataServicePreviewRequest;
import com.trs.ai.moye.data.service.request.DataServiceRequest;
import com.trs.ai.moye.data.service.response.DataServiceCategoryResponse;
import com.trs.ai.moye.data.service.response.DataServiceCategoryTreeResponse;
import com.trs.ai.moye.data.service.response.DataServiceResponse;
import com.trs.ai.moye.data.service.response.ability.AbilityCheckUnpublishResponse;
import com.trs.ai.moye.data.service.service.DataServiceService;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import com.trs.moye.base.common.response.IdNameResponse;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.response.ResponseMessage;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockedStatic.Verification;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 数据服务控制器测试
 *
 * <AUTHOR>
 * @since 2024/10/21 17:48:08
 */
@ExtendWith(MockitoExtension.class)
class DataServiceControllerTest {

    @InjectMocks
    private DataServiceController dataServiceController;

    private MockMvc mockMvc;

    @Mock
    private DataServiceCategoryMapper dataServiceCategoryMapper;

    @Mock
    private DataServiceService dataServiceService;

    @Mock
    private DataStorageMapper dataStorageMapper;

    @Mock
    private DataServiceMapper dataServiceMapper;

    @Mock
    private DataConnectionMapper dataConnectionMapper;

    @Mock
    private DynamicUserNameService dynamicUserNameService;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(dataServiceController)
            .setControllerAdvice(new CustomerResponseBodyConvert(), new GlobalExceptionProcessor())
            .setMessageConverters(new MappingJackson2HttpMessageConverter(JsonUtils.OBJECT_MAPPER))
            .build();
    }

    @Test
    @DisplayName("当提供搜索参数时，应返回过滤后的分类树")
    void getCategoryTree_shouldReturnFilteredTreeWhenSearchParamProvided() {
        // Arrange
        String searchParam = "test";
        List<DataServiceCategoryTreeResponse> mockTree = Arrays.asList(
                new DataServiceCategoryTreeResponse(),
                new DataServiceCategoryTreeResponse()
        );

        try (MockedStatic<BeanUtil> beanUtilMock = Mockito.mockStatic(BeanUtil.class)) {
            beanUtilMock.when(() -> BeanUtil.getBean(DynamicUserNameService.class))
                    .thenReturn(dynamicUserNameService);

            when(dataServiceService.getCategoryTree()).thenReturn(Collections.emptyList());
            when(dataServiceService.searchFilterData(any(), anyString())).thenReturn(mockTree);

            // Act
            List<DataServiceCategoryTreeResponse> result = dataServiceController.getCategoryTree(searchParam);

            // Assert
            assertEquals(2, result.size());
            assertEquals(mockTree, result);
        }
    }

    @Test
    @DisplayName("当不提供搜索参数时，应返回完整的分类树")
    void getCategoryTree_shouldReturnFullTreeWhenNoSearchParam() {
        // Arrange
        List<DataServiceCategoryTreeResponse> mockTree = Arrays.asList(
                new DataServiceCategoryTreeResponse(),
                new DataServiceCategoryTreeResponse(),
                new DataServiceCategoryTreeResponse()
        );

        try (MockedStatic<BeanUtil> beanUtilMock = Mockito.mockStatic(BeanUtil.class)) {
            beanUtilMock.when(() -> BeanUtil.getBean(DynamicUserNameService.class))
                    .thenReturn(dynamicUserNameService);

            when(dataServiceService.getCategoryTree()).thenReturn(Collections.emptyList());
            when(dataServiceService.searchFilterData(any(), any())).thenReturn(mockTree);

            // Act
            List<DataServiceCategoryTreeResponse> result = dataServiceController.getCategoryTree(null);

            // Assert
            assertEquals(3, result.size());
            assertEquals(mockTree, result);
        }
    }

    @Test
    @DisplayName("当未找到匹配数据时，应返回空列表")
    void getCategoryTree_shouldReturnEmptyListWhenNoDataFound() {
        // Arrange
        try (MockedStatic<BeanUtil> beanUtilMock = Mockito.mockStatic(BeanUtil.class)) {
            beanUtilMock.when(() -> BeanUtil.getBean(DynamicUserNameService.class))
                    .thenReturn(dynamicUserNameService);

            when(dataServiceService.getCategoryTree()).thenReturn(Collections.emptyList());
            when(dataServiceService.searchFilterData(any(), any())).thenReturn(Collections.emptyList());

            // Act
            List<DataServiceCategoryTreeResponse> result = dataServiceController.getCategoryTree("nonexistent");

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    @DisplayName("当搜索参数为空字符串时，应正确处理并返回相应结果")
    void getCategoryTree_shouldHandleEmptySearchString() {
        // Arrange
        List<DataServiceCategoryTreeResponse> mockTree = Collections.singletonList(
                new DataServiceCategoryTreeResponse()
        );

        try (MockedStatic<BeanUtil> beanUtilMock = Mockito.mockStatic(BeanUtil.class)) {
            beanUtilMock.when(() -> BeanUtil.getBean(DynamicUserNameService.class))
                    .thenReturn(dynamicUserNameService);

            when(dataServiceService.getCategoryTree()).thenReturn(Collections.emptyList());
            when(dataServiceService.searchFilterData(any(), anyString())).thenReturn(mockTree);

            // Act
            List<DataServiceCategoryTreeResponse> result = dataServiceController.getCategoryTree("");

            // Assert
            assertEquals(1, result.size());
            assertEquals(mockTree, result);
        }
    }

    @Test
    void checkCategoryName() throws Exception {
        DataServiceCategoryRequest request = new DataServiceCategoryRequest();
        request.setName("Test Category");
        when(dataServiceCategoryMapper.checkCategoryName(any())).thenReturn(false);

        String expectedResponse = "{\"code\":200,\"success\":true,\"data\":false}";
        mockMvc.perform(post("/data-service/category/check-name")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.OBJECT_MAPPER.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(content().json(expectedResponse, false));
    }

    @Test
    void addCategory() throws Exception {
        // 测试参数为空的情况
        DataServiceCategoryRequest request = new DataServiceCategoryRequest();
        when(dataServiceCategoryMapper.insert(any(DataServiceCategory.class))).thenReturn(1);

        mockMvc.perform(post("/data-service/category")
                .contentType(MediaType.APPLICATION_JSON)
                .accept("application/json;charset=UTF-8")
                .content(JsonUtils.OBJECT_MAPPER.writeValueAsString(request)))
            .andExpect(status().isInternalServerError());

        // 测试参数不为空的情况
        request = new DataServiceCategoryRequest();
        request.setName("testName");

        mockMvc.perform(post("/data-service/category")
                .contentType(MediaType.APPLICATION_JSON)
                .accept("application/json;charset=UTF-8")
                .content(JsonUtils.OBJECT_MAPPER.writeValueAsString(request)))
            .andExpect(status().isOk());
    }

    @Test
    void getCategory() throws Exception {
        DataServiceCategory serviceCategory = new DataServiceCategory();
        serviceCategory.setId(1);
        serviceCategory.setName("testName");
        serviceCategory.setDescription("testDescription");
        MockedStatic<BeanUtil> mockedStatic = mockStatic(BeanUtil.class);
        mockedStatic.when((Verification) BeanUtil.getBean(DynamicUserNameService.class))
            .thenReturn(dynamicUserNameService);
        DataServiceCategoryResponse response = new DataServiceCategoryResponse(serviceCategory, dynamicUserNameService);
        when(dataServiceCategoryMapper.selectById(any(Integer.class))).thenReturn(serviceCategory);

        ResponseMessage responseEntity = ResponseMessage.ok(response);
        String expectedResponse = JsonUtils.OBJECT_MAPPER.writeValueAsString(responseEntity);

        mockMvc.perform(get("/data-service/category/1")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().string(expectedResponse));
        mockedStatic.close();
    }

    @Test
    void updateCategory() throws Exception {
        // Arrange
        DataServiceCategoryRequest request = new DataServiceCategoryRequest();
        request.setName("Updated Category");
        request.setId(1);

        when(dataServiceCategoryMapper.selectById(any(Integer.class))).thenReturn(new DataServiceCategory());
        when(dataServiceCategoryMapper.updateById(any(DataServiceCategory.class))).thenReturn(1);

        // Act & Assert
        mockMvc.perform(put("/data-service/category/1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.OBJECT_MAPPER.writeValueAsString(request)))
            .andExpect(status().isOk());

    }

    @Test
    void deleteCategory() throws Exception {
        // Arrange
        DataServiceCategory mockCategory = new DataServiceCategory();
        mockCategory.setId(1);
        mockCategory.setName("testName");

        when(dataServiceCategoryMapper.selectById(any(Integer.class))).thenReturn(mockCategory);
        when(dataServiceCategoryMapper.selectCategoryTreeByPid(any(Integer.class))).thenReturn(new ArrayList<>());

        // Act & Assert
        mockMvc.perform(delete("/data-service/category/1")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk());
    }


    @Test
    void addDataService() throws Exception {
        DataServiceRequest request = new DataServiceRequest();
        request.setName("Test Service");
        request.setDescription("Test Description");
        request.setConnectionId(1);
        request.setCategoryId(1);

        lenient().when(dataConnectionMapper.selectById(any(Integer.class))).thenReturn(new DataConnection());
        lenient().when(dataStorageMapper.selectById(any(Integer.class))).thenReturn(new DataStorage());
        lenient().when(dataServiceMapper.checkName(any())).thenReturn(false);
        lenient().when(dataServiceCategoryMapper.selectById(any(Integer.class))).thenReturn(new DataServiceCategory());

        mockMvc.perform(post("/data-service")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.OBJECT_MAPPER.writeValueAsString(request)))
            .andExpect(status().isOk());

        request.setName("");
        request.setDescription("Test Description");
        request.setConnectionId(null);
        request.setCategoryId(null);

        mockMvc.perform(post("/data-service")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.OBJECT_MAPPER.writeValueAsString(request)))
            .andExpect(status().isInternalServerError());

    }

    @Test
    void updateDataService() throws Exception {
        DataServiceRequest request = new DataServiceRequest();
        request.setName("Updated Service");
        request.setCategoryId(1);
        request.setDescription("Test Description");
        request.setConnectionId(1);
        lenient().when(dataConnectionMapper.selectById(any(Integer.class))).thenReturn(new DataConnection());
        lenient().when(dataStorageMapper.selectById(any(Integer.class))).thenReturn(new DataStorage());
        lenient().when(dataServiceMapper.checkName(any())).thenReturn(false);
        lenient().when(dataServiceCategoryMapper.selectById(1)).thenReturn(new DataServiceCategory());
        mockMvc.perform(put("/data-service")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.OBJECT_MAPPER.writeValueAsString(request)))
            .andExpect(status().isOk());

        request.setName("123");
        request.setDescription("");
        request.setConnectionId(null);
        request.setCategoryId(1);

        mockMvc.perform(put("/data-service")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.OBJECT_MAPPER.writeValueAsString(request)))
            .andExpect(status().isInternalServerError());
    }

    @Test
    void getDetail() throws Exception {

        DataServiceDto dataServiceDto = new DataServiceDto();
        dataServiceDto.setId(1);
        dataServiceDto.setName("Test Service");

        MockedStatic<BeanUtil> mockedStatic = mockStatic(BeanUtil.class);
        mockedStatic.when(() -> BeanUtil.getBean(DynamicUserNameService.class))
            .thenReturn(dynamicUserNameService);

        DataServiceResponse response = new DataServiceResponse(dataServiceDto, dynamicUserNameService);
        when(dataServiceService.getDetail(any(Integer.class))).thenReturn(response);

        ResponseMessage responseEntity = ResponseMessage.ok(response);
        String expectedResponse = JsonUtils.OBJECT_MAPPER.writeValueAsString(responseEntity);

        mockMvc.perform(get("/data-service/1")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().string(expectedResponse));
        mockedStatic.close();

    }

    @Test
    void testDeleteDataService() throws Exception {
        // Mock the data service list
        DataService dataService1 = new DataService();
        dataService1.setId(1);
        dataService1.setPublishStatus(ServicePublishStatus.UNRELEASED);
        dataService1.setName("Service1");

        DataService dataService2 = new DataService();
        dataService2.setId(2);
        dataService2.setPublishStatus(ServicePublishStatus.UNRELEASED);
        dataService2.setName("Service2");

        List<DataService> dataServiceList = Arrays.asList(dataService1, dataService2);

        // Mock the behavior of dataServiceMapper
        when(dataServiceMapper.selectByIds(List.of(1, 2))).thenReturn(dataServiceList);

        // Mock the behavior of dataServiceService
        doNothing().when(dataServiceService).deleteDataService(List.of(1,2));

        // Perform the DELETE request
        mockMvc.perform(delete("/data-service")
                .contentType(MediaType.APPLICATION_JSON)
                .content("[1, 2]")) // Assuming the request body is a JSON array of IDs
            .andExpect(status().isOk());
    }

    @Test
    void publishDataService() throws Exception {
        when(dataServiceMapper.selectById(any(Integer.class))).thenReturn(new DataService());
        when(dataServiceService.publish(any(Integer.class))).thenReturn(true);
        String expectedResponse = "{\"code\":200,\"success\":true,\"data\":true}";
        mockMvc.perform(post("/data-service/1/publish")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().json(expectedResponse, false));
    }

    @Test
    void checkServiceName() throws Exception {
        CheckNameDataServiceRequest request = new CheckNameDataServiceRequest();
        request.setName("Test Service");
        request.setCategoryId(1);
        String expectedResponse = "{\"code\":200,\"success\":true,\"data\":true}";
        when(dataServiceService.checkServiceName(any(CheckNameDataServiceRequest.class))).thenReturn(true);

        mockMvc.perform(post("/data-service/check-name")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.OBJECT_MAPPER.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(content().json(expectedResponse, false));
    }

    @Test
    void checkUnpublish() throws Exception {
        AbilityCheckUnpublishResponse response = new AbilityCheckUnpublishResponse();
        response.setCheckDelete(true);
        when(dataServiceMapper.selectById(any(Integer.class))).thenReturn(new DataService());
        when(dataServiceService.checkUnpublish(any(Integer.class))).thenReturn(response);
        ResponseMessage responseEntity = ResponseMessage.ok(response);
        String expectedResponse = JsonUtils.OBJECT_MAPPER.writeValueAsString(responseEntity);
        mockMvc.perform(post("/data-service/1/check-unpublish")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().string(expectedResponse));
    }

    @Test
    void unpublish() throws Exception {
        when(dataServiceMapper.selectById(any(Integer.class))).thenReturn(new DataService());
        when(dataServiceService.unpublish(any(Integer.class))).thenReturn(true);
        String expectedResponse = "{\"code\":200,\"success\":true,\"data\":true}";
        mockMvc.perform(post("/data-service/1/unpublish")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().json(expectedResponse, false));
    }

    @Test
    void republish() throws Exception {
        when(dataServiceMapper.selectById(any(Integer.class))).thenReturn(new DataService());
        when(dataServiceService.republish(any(Integer.class))).thenReturn(true);
        String expectedResponse = "{\"code\":200,\"success\":true,\"data\":true}";
        mockMvc.perform(post("/data-service/1/republish")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().json(expectedResponse, false));
    }

    @Test
    void getPublishStatus() throws Exception {
        when(dataServiceService.getPublishStatus(any(Integer.class))).thenReturn(ServicePublishStatus.RELEASED);
        String expectedResponse = "{\"code\":200,\"success\":true,\"data\":\"RELEASED\"}";
        mockMvc.perform(get("/data-service/1/publish-status")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().json(expectedResponse, false));
    }

    @Test
    void getRefConditionList() throws Exception {
        List<IdNameResponse> response = new ArrayList<>();
        response.add(new IdNameResponse(1, "Condition 1"));

        when(dataServiceService.getRefConditionList(any(Integer.class), any())).thenReturn(response);
        ResponseMessage responseEntity = ResponseMessage.ok(response);
        String expectedResponse = JsonUtils.OBJECT_MAPPER.writeValueAsString(responseEntity);

        mockMvc.perform(get("/data-service/ref-condition/list")
                .param("storageId", "1")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().string(expectedResponse));
    }

    @Test
    void getTips() throws Exception {
        when(dataServiceService.getTips(any(ConnectionType.class))).thenReturn("Sample Tips");
        String expectedResponse = "{\"code\":200,\"success\":true,\"data\":\"Sample Tips\"}";
        mockMvc.perform(get("/data-service/MYSQL/tips")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().json(expectedResponse, false));
    }

    @Test
    void analyseCode() throws Exception {
        CodeBlockRequest request = new CodeBlockRequest();
        request.setCodeBlock("SELECT * FROM table");
        request.setDbType(ConnectionType.MYSQL);

        List<String> response = List.of("Parsed Code");

        when(dataServiceService.analyseCode(any(CodeBlockRequest.class))).thenReturn(response);
        ResponseMessage responseEntity = ResponseMessage.ok(response);
        String expectedResponse = JsonUtils.OBJECT_MAPPER.writeValueAsString(responseEntity);
        mockMvc.perform(post("/data-service/code-analysis")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.OBJECT_MAPPER.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(content().string(expectedResponse));

        request.setCodeBlock("");
        mockMvc.perform(post("/data-service/code-analysis")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.OBJECT_MAPPER.writeValueAsString(request)))
            .andExpect(status().isInternalServerError());
    }

    @Test
    void preview() throws Exception {
        DataServicePreviewRequest request = new DataServicePreviewRequest();
        request.setConnectionId(1);
        request.setCreateMode(ServiceCreateMode.CODE_MODE);
        DataServiceConfig config = new DataServiceConfig();
        config.setType(ServiceConfigType.CODE);
        CodeModeParams codeModeParams = new CodeModeParams();
        codeModeParams.setType(ServiceConfigType.CODE);
        codeModeParams.setCodeBlock("SELECT * FROM table");
        config.setParams(codeModeParams);
        request.setDataServiceConfig(config);

        PageResponse<Map<String, Object>> response = new PageResponse<>();
        response.setTotal(1);
        response.setItems(List.of(Map.of("key", "value")));

        when(dataServiceService.preview(any(DataServicePreviewRequest.class))).thenReturn(response);
        ResponseMessage responseEntity = ResponseMessage.ok(response);
        String expectedResponse = JsonUtils.OBJECT_MAPPER.writeValueAsString(responseEntity);
        mockMvc.perform(post("/data-service/preview")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.OBJECT_MAPPER.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(content().string(expectedResponse));

        request.setConnectionId(null);

        mockMvc.perform(post("/data-service/preview")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.OBJECT_MAPPER.writeValueAsString(request)))
            .andExpect(status().isInternalServerError());
    }

}