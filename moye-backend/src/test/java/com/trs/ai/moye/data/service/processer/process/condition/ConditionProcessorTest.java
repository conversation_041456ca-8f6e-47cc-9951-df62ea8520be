package com.trs.ai.moye.data.service.processer.process.condition;

import static com.trs.ai.moye.data.service.processer.process.condition.ConditionProcessor.optimizeConditions;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.trs.ai.moye.data.service.entity.DataServiceField;
import com.trs.ai.moye.data.service.entity.query.Condition;
import com.trs.ai.moye.data.service.entity.query.ValueObject;
import com.trs.ai.moye.data.service.enums.DataServiceConditionType;
import com.trs.ai.moye.data.service.enums.LogicLinkOperator;
import com.trs.moye.base.common.enums.FieldType;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;

class ConditionProcessorTest {


    @Test
    void optimizeConditions_SingleExpression_Unchanged() {
        ArrayList<ValueObject> valueObjects = new ArrayList<>();
        Condition expression = new Condition();
        expression.setKey(
            new DataServiceField("1", "field", "field", "String", FieldType.STRING, false, false, null));
        expression.setType(DataServiceConditionType.EXPRESSION);
        expression.setValues(valueObjects);
        expression.setOperator("=");

        List<Condition> result = optimizeConditions(List.of(expression));

        assertEquals(1, result.size());
        assertEquals(DataServiceConditionType.EXPRESSION, result.get(0).getType());
    }

    @Test
    void optimizeConditions_ConsecutiveLogicOperators_MergesIdentical() {
        List<Condition> conditions = List.of(
            new Condition(LogicLinkOperator.AND),
            new Condition(LogicLinkOperator.AND),
            new Condition(DataServiceConditionType.EXPRESSION, "=")
        );

        List<Condition> result = optimizeConditions(conditions);

        assertEquals(1, result.size());
        assertEquals(DataServiceConditionType.EXPRESSION, result.get(0).getType());
    }

    @Test
    void optimizeConditions_InvalidCondition_RemovesEmptyValues() {
        Condition invalidCondition = new Condition(
            DataServiceConditionType.EXPRESSION,
            new DataServiceField("1", "field", "field", "String", FieldType.STRING, false, false, null),
            List.of(new ValueObject()),
            "="
        );

        List<Condition> result = optimizeConditions(List.of(invalidCondition));

        assertTrue(!result.isEmpty());
    }

    @Test
    void optimizeConditions_ComplexNesting_PreservesLogic() {
        List<Condition> conditions = List.of(
            new Condition(LogicLinkOperator.LEFT_PARENTHESES),
            new Condition(DataServiceConditionType.EXPRESSION, "="),
            new Condition(LogicLinkOperator.OR),
            new Condition(DataServiceConditionType.EXPRESSION, "ref"),
            new Condition(LogicLinkOperator.RIGHT_PARENTHESES),
            new Condition(LogicLinkOperator.AND),
            new Condition(DataServiceConditionType.EXPRESSION, ">")
        );

        List<Condition> result = optimizeConditions(conditions);

        assertEquals(7, result.size());
        assertEquals("(", result.get(0).getOperator());
        assertEquals("or", result.get(2).getOperator());
        assertEquals(")", result.get(4).getOperator());
        assertEquals("and", result.get(5).getOperator());
    }

    @Test
    void optimizeConditions_MultipleConsecutiveMixedOperators_KeepLastValid() {
        // 表达式1 AND OR AND 表达式2 -> 表达式1 AND 表达式2
        List<Condition> conditions = List.of(
            new Condition(DataServiceConditionType.EXPRESSION, "A"), // 有效表达式
            new Condition(LogicLinkOperator.AND),
            new Condition(LogicLinkOperator.OR),
            new Condition(LogicLinkOperator.AND),
            new Condition(DataServiceConditionType.EXPRESSION, "B")
        );

        List<Condition> result = optimizeConditions(conditions);

        assertThat(result).extracting(Condition::getOperator)
            .containsExactly("A", "and", "B");
    }

    @Test
    void optimizeConditions_LeadingOperators_RemoveAllLeading() {
        // OR AND OR 表达式 -> 表达式
        List<Condition> conditions = List.of(
            new Condition(LogicLinkOperator.OR),
            new Condition(LogicLinkOperator.AND),
            new Condition(LogicLinkOperator.OR),
            new Condition(DataServiceConditionType.EXPRESSION, "field")
        );

        List<Condition> result = optimizeConditions(conditions);

        assertThat(result).extracting(Condition::getType)
            .containsOnly(DataServiceConditionType.EXPRESSION);
    }

    @Test
    void optimizeConditions_TrailingOperators_RemoveAllTrailing() {
        // 表达式 AND OR -> 表达式
        List<Condition> conditions = List.of(
            new Condition(DataServiceConditionType.EXPRESSION, "field"),
            new Condition(LogicLinkOperator.AND),
            new Condition(LogicLinkOperator.OR)
        );

        List<Condition> result = optimizeConditions(conditions);

        assertThat(result).extracting(Condition::getType)
            .containsOnly(DataServiceConditionType.EXPRESSION);
    }

    @Test
    void optimizeConditions_OperatorsSurroundingBrackets_CleanEdges() {
        // AND ( 表达式 OR 表达式 ) OR -> ( 表达式 OR 表达式 )
        List<Condition> conditions = List.of(
            new Condition(LogicLinkOperator.AND),
            new Condition(LogicLinkOperator.LEFT_PARENTHESES),
            new Condition(DataServiceConditionType.EXPRESSION, "A"),
            new Condition(LogicLinkOperator.OR),
            new Condition(DataServiceConditionType.EXPRESSION, "B"),
            new Condition(LogicLinkOperator.RIGHT_PARENTHESES),
            new Condition(LogicLinkOperator.OR)
        );

        List<Condition> result = optimizeConditions(conditions);

        assertThat(result).extracting(Condition::getOperator)
            .containsExactly("(", "A", "or", "B", ")");
    }

    @Test
    void optimizeConditions_ComplexMixedOperators_HandleAllRedundant() {
        // OR AND ( OR 表达式 AND ) OR 表达式 AND -> ( 表达式 ) 表达式
        List<Condition> conditions = List.of(
            new Condition(LogicLinkOperator.OR),
            new Condition(LogicLinkOperator.AND),
            new Condition(LogicLinkOperator.LEFT_PARENTHESES),
            new Condition(LogicLinkOperator.OR),
            new Condition(DataServiceConditionType.EXPRESSION, "C"),
            new Condition(LogicLinkOperator.AND),
            new Condition(LogicLinkOperator.RIGHT_PARENTHESES),
            new Condition(LogicLinkOperator.OR),
            new Condition(LogicLinkOperator.NOT),
            new Condition(DataServiceConditionType.EXPRESSION, "D"),
            new Condition(LogicLinkOperator.AND)
        );

        List<Condition> result = optimizeConditions(conditions);

        assertThat(result).extracting(Condition::getOperator)
            .containsExactly("(", "C", ")", "or", "not", "D");
    }


}