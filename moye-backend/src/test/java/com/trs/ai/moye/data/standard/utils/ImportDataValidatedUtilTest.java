package com.trs.ai.moye.data.standard.utils;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.trs.ai.moye.data.standard.entity.ImportFieldDetail;
import com.trs.ai.moye.data.standard.entity.ImportFieldDetail.ValidationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

@DisplayName("ImportDataValidatedUtil Tests")
class ImportDataValidatedUtilTest {

    private ImportFieldDetail importFieldDetail;

    @BeforeEach
    void setUp() {
        importFieldDetail = mock(ImportFieldDetail.class);
    }

    @Test
    void testCheckPassFieldBlankValidationFails() {
        // Arrange
        ValidationResult validationResult = new ValidationResult(false, "中文名(zhName)");
        when(importFieldDetail.validateFieldBlank()).thenReturn(validationResult);

        // Act
        boolean result = ImportDataValidatedUtil.checkPass(importFieldDetail);

        // Assert
        assertFalse(result);
        verify(importFieldDetail, times(1)).setFailReason("中文名(zhName)字段不能为空！");
    }

    @Test
    void testCheckPassIsStatisticValidationFails() {
        // Arrange
        when(importFieldDetail.validateFieldBlank()).thenReturn(new ValidationResult(true, ""));
        when(importFieldDetail.getStatistic()).thenReturn("invalid");

        // Act
        boolean result = ImportDataValidatedUtil.checkPass(importFieldDetail);

        // Assert
        assertFalse(result);
        verify(importFieldDetail, times(1)).setFailReason("分类统计字段应该填写是/否！");
    }

    @Test
    void testCheckPassIsMultiValueValidationFails() {
        // Arrange
        when(importFieldDetail.validateFieldBlank()).thenReturn(new ValidationResult(true, ""));
        when(importFieldDetail.getStatistic()).thenReturn("是");
        when(importFieldDetail.getMultiValue()).thenReturn("invalid");

        // Act
        boolean result = ImportDataValidatedUtil.checkPass(importFieldDetail);

        // Assert
        assertFalse(result);
        verify(importFieldDetail, times(1)).setFailReason("允许多值字段应该填写是/否！");
    }

    @Test
    void testCheckPassIsNullableValidationFails() {
        // Arrange
        when(importFieldDetail.validateFieldBlank()).thenReturn(new ValidationResult(true, ""));
        when(importFieldDetail.getStatistic()).thenReturn("是");
        when(importFieldDetail.getMultiValue()).thenReturn("是");
        when(importFieldDetail.getNullable()).thenReturn("invalid");

        // Act
        boolean result = ImportDataValidatedUtil.checkPass(importFieldDetail);

        // Assert
        assertFalse(result);
        verify(importFieldDetail, times(1)).setFailReason("允许空值字段应该填写是/否！");
    }

    @Test
    void testCheckPassAllValidationsPass() {
        // Arrange
        when(importFieldDetail.validateFieldBlank()).thenReturn(new ValidationResult(true, ""));
        when(importFieldDetail.getStatistic()).thenReturn("是");
        when(importFieldDetail.getMultiValue()).thenReturn("是");
        when(importFieldDetail.getNullable()).thenReturn("是");

        // Act
        boolean result = ImportDataValidatedUtil.checkPass(importFieldDetail);

        // Assert
        assertTrue(result);
        verify(importFieldDetail, never()).setFailReason(anyString());
    }


}