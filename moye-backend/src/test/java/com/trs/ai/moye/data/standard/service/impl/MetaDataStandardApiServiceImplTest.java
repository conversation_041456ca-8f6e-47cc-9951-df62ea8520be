package com.trs.ai.moye.data.standard.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.trs.ai.moye.data.standard.request.MetaDataStandardCreateApiRequest;
import com.trs.ai.moye.data.standard.service.MetaDataStandardService;
import com.trs.moye.base.common.utils.JsonUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MetaDataStandardApiServiceImplTest {

    @InjectMocks
    private MetaDataStandardApiServiceImpl metaDataStandardApiService;

    @Mock
    private MetaDataStandardService metaDataStandardService;


    @BeforeEach
    void setUp() {
    }

    @Test
    void creatGraphicsMetaDataStandard() {
        // 断言没有异常抛出
        String requestStr = "{ \"zhName\": \"由外部api创建的元数据标准2\", \"enName\": \"wen\", \"description\": \"hahahaha\", \"catalogId\": 8, \"vidType\": \"int\", \"tag\": [ { \"zhName\": \"人物\", \"enName\": \"person\", \"description\": \"description_cb0fa7dbd48d\", \"fields\": [ { \"zhName\": \"名称\", \"enName\": \"nmae\", \"type\": \"string\", \"notNull\": false, \"defaultValue\": \"qiukeyu\", \"description\": \"\" }, { \"zhName\": \"性别\", \"enName\": \"gender\", \"type\": \"string\", \"notNull\": false, \"defaultValue\": \"man!\", \"description\": \"\" } ] } ], \"edge\": [ { \"zhName\": \"贵物\", \"enName\": \"monster\", \"description\": \"description_cb0fa7dbd48d\", \"fields\": [ { \"zhName\": \"名称\", \"enName\": \"nmae\", \"type\": \"string\", \"notNull\": false, \"defaultValue\": \"qiukeyu\", \"description\": \"\" }, { \"zhName\": \"性别\", \"enName\": \"gender\", \"type\": \"string\", \"notNull\": false, \"defaultValue\": \"man!\", \"description\": \"\" } ] } ] }";
        MetaDataStandardCreateApiRequest metaDataStandardCreateApiRequest = JsonUtils.parseObject(requestStr,
            new TypeReference<MetaDataStandardCreateApiRequest>() {
            });
        Assertions.assertDoesNotThrow(
            () -> metaDataStandardApiService.creatGraphicsMetaDataStandard(metaDataStandardCreateApiRequest));
    }
}