package com.trs.ai.moye.data.model.controller;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.ai.moye.data.model.dao.DataModelDisplayMapper;
import com.trs.ai.moye.data.model.entity.MetadataRelation;
import com.trs.ai.moye.data.model.request.CategoryTreeRequest;
import com.trs.ai.moye.data.model.response.CategoryTreeResponse;
import com.trs.ai.moye.data.model.response.DataSourceConnectionResponse;
import com.trs.ai.moye.data.model.service.DataModelCategoryService;
import com.trs.ai.moye.data.model.service.DataModelExecuteStatusService;
import com.trs.ai.moye.data.model.service.impl.DataModelCategoryServiceImpl;
import com.trs.ai.moye.data.standard.dao.DataStandardFieldMapper;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.data.model.dao.BusinessCategoryMapper;
import com.trs.moye.base.data.model.dao.CategoryOrderConfigMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.BusinessCategory;
import com.trs.moye.base.data.model.enums.CreateTableStatus;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

class DataModelCategoryControllerTest {

    @Mock
    private BusinessCategoryMapper businessCategoryMapper;

    @Mock
    private DataModelCategoryService dataModelCategoryService;

    @Mock
    private DataModelMapper dataModelMapper;

    @Mock
    private DataStandardFieldMapper dataStandardFieldMapper;

    @Mock
    private DynamicUserNameService dynamicUserNameService;
    @Mock
    private DataModelDisplayMapper dataModelDisplayMapper;
    @Mock
    private CategoryOrderConfigMapper categoryOrderConfigMapper;
    @Mock
    private DataConnectionMapper dataConnectionMapper;
    @Mock
    private DataModelExecuteStatusService dataModelExecuteStatusService;
    @InjectMocks
    private DataModelCategoryController controller;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        // 创建真实 service 实例
        DataModelCategoryService realService = new DataModelCategoryServiceImpl();

        // 使用反射注入 private 属性
        injectPrivateField(realService, "businessCategoryMapper", businessCategoryMapper);
        injectPrivateField(realService, "dataModelDisplayMapper", dataModelDisplayMapper);
        injectPrivateField(realService, "categoryOrderConfigMapper", categoryOrderConfigMapper);
        injectPrivateField(realService, "dataConnectionMapper", dataConnectionMapper);
        injectPrivateField(realService, "dataModelExecuteStatusService", dataModelExecuteStatusService);
        // 将真实 service 注入 controller
        injectPrivateField(controller, "dataModelCategoryService", realService);

        // 构建 MockMvc
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();

    }

    /**
     * 通过反射注入 private 字段
     */
    private <T> void injectPrivateField(Object target, String fieldName, T value) throws Exception {
        Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    // ------------------------ 测试辅助方法 ------------------------
    private String asJsonString(Object obj) throws Exception {
        return new ObjectMapper().writeValueAsString(obj);
    }

    /**
     * 测试获取业务分类树接口
     */
    @Test
    @DisplayName("无参数-返回全量树")
    void testCategoryTree_noParams() throws Exception {
        // 构造业务分类
        BusinessCategory catA = new BusinessCategory();
        catA.setId(1);
        catA.setZhName("业务A");
        catA.setEnName("businessA");

        BusinessCategory catB = new BusinessCategory();
        catB.setId(2);
        catB.setZhName("业务B");
        catB.setEnName("businessB");

        // 构造数据模型关联
        MetadataRelation relA = new MetadataRelation();
        relA.setCategoryId(1);
        relA.setModelLayer(ModelLayer.DWD);
        relA.setDataModelName("模型A");
        relA.setDataModelEnName("modelA");

        MetadataRelation relB = new MetadataRelation();
        relB.setCategoryId(2);
        relB.setModelLayer(ModelLayer.ODS);
        relB.setDataModelName("模型B");
        relB.setDataModelEnName("modelB");

        // mock mapper
        when(businessCategoryMapper.getAllCategory()).thenReturn(new ArrayList<>(List.of(catA, catB)));
        when(dataModelDisplayMapper.selectAllDataModelRelation(any())).thenReturn(new ArrayList<>(List.of(relA, relB)));

        CategoryTreeRequest request = new CategoryTreeRequest();
        mockMvc.perform(post("/data-model/category/tree")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$", hasSize(2)))
            .andExpect(jsonPath("$[0].name", is("业务A")))
            .andExpect(jsonPath("$[1].name", is("业务B")));
    }

    @Test
    @DisplayName("enableStatus筛选")
    void testCategoryTree_enableStatus() throws Exception {
        BusinessCategory catA = new BusinessCategory("1", "业务A", "businessA");
        BusinessCategory catB = new BusinessCategory("2", "业务B", "businessB");
        catA.setId(1);
        catB.setId(2);
        MetadataRelation relA = new MetadataRelation();
        relA.setCategoryId(1);
        relA.setModelLayer(ModelLayer.DWD);
        relA.setDataModelName("模型A");
        relA.setDataModelEnName("modelA");
        relA.setStatus(ModelExecuteStatus.START);

        MetadataRelation relC = new MetadataRelation();
        relC.setCategoryId(2);
        relC.setModelLayer(ModelLayer.DWD);
        relC.setDataModelName("模型C");
        relC.setDataModelEnName("modelC");
        relC.setStatus(ModelExecuteStatus.START);

        when(businessCategoryMapper.getAllCategory()).thenReturn(new ArrayList<>(List.of(catA, catB)));
        when(dataModelDisplayMapper.selectAllDataModelRelation(any())).thenReturn(new ArrayList<>(List.of(relA, relC)));

        CategoryTreeRequest request = new CategoryTreeRequest();
        request.setEnableStatus(ModelExecuteStatus.START);
        mockMvc.perform(post("/data-model/category/tree")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$[0].children[0].children[0].name", is("模型A")))
            .andExpect(jsonPath("$[1].children[0].children[0].name", is("模型C")));
    }

    @Test
    @DisplayName("createTableStatus筛选-SUCCESS")
    void testCategoryTree_createTableStatus_Success() throws Exception {
        BusinessCategory catA = new BusinessCategory("1", "业务A", "businessA");
        when(businessCategoryMapper.getAllCategory()).thenReturn(new ArrayList<>(List.of(catA)));
        when(dataModelDisplayMapper.selectAllDataModelRelation(any())).thenReturn(new ArrayList<>());

        CategoryTreeRequest request = new CategoryTreeRequest();
        request.setCreateTableStatus(CreateTableStatus.SUCCESS);
        mockMvc.perform(post("/data-model/category/tree")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk());
    }

    @Test
    @DisplayName("createTableStatus筛选-FAIL")
    void testCategoryTree_createTableStatus_FAIL() throws Exception {
        BusinessCategory catA = new BusinessCategory("1", "业务A", "businessA");
        when(businessCategoryMapper.getAllCategory()).thenReturn(new ArrayList<>(List.of(catA)));
        when(dataModelDisplayMapper.selectAllDataModelRelation(any())).thenReturn(new ArrayList<>());

        CategoryTreeRequest request = new CategoryTreeRequest();
        request.setCreateTableStatus(CreateTableStatus.FAIL);
        mockMvc.perform(post("/data-model/category/tree")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk());
    }

    @Test
    @DisplayName("createTableStatus筛选-NOT")
    void testCategoryTree_createTableStatus_NOT() throws Exception {
        BusinessCategory catA = new BusinessCategory("1", "业务A", "businessA");
        when(businessCategoryMapper.getAllCategory()).thenReturn(new ArrayList<>(List.of(catA)));
        when(dataModelDisplayMapper.selectAllDataModelRelation(any())).thenReturn(new ArrayList<>());

        CategoryTreeRequest request = new CategoryTreeRequest();
        request.setCreateTableStatus(CreateTableStatus.NOT);
        mockMvc.perform(post("/data-model/category/tree")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk());
    }

    @Test
    @DisplayName("arrangeStatus筛选")
    void testCategoryTree_arrangeStatus() throws Exception {
        BusinessCategory catA = new BusinessCategory("1", "业务A", "businessA");
        when(businessCategoryMapper.getAllCategory()).thenReturn(new ArrayList<>(List.of(catA)));
        when(dataModelDisplayMapper.selectAllDataModelRelation(any())).thenReturn(new ArrayList<>());

        CategoryTreeRequest request = new CategoryTreeRequest();
        request.setArrangeStatus(true);
        mockMvc.perform(post("/data-model/category/tree")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk());
    }

    @Test
    @DisplayName("connectionType筛选")
    void testCategoryTree_connectionType() throws Exception {
        BusinessCategory catA = new BusinessCategory("1", "业务A", "businessA");
        when(businessCategoryMapper.getAllCategory()).thenReturn(new ArrayList<>(List.of(catA)));
        when(dataModelDisplayMapper.selectAllDataModelRelation(any())).thenReturn(new ArrayList<>());

        CategoryTreeRequest request = new CategoryTreeRequest();
        request.setConnectionType(ConnectionType.MYSQL);
        mockMvc.perform(post("/data-model/category/tree")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk());
    }

    @Test
    @DisplayName("modelLayerType筛选")
    void testCategoryTree_modelLayerType() throws Exception {
        BusinessCategory catA = new BusinessCategory("1", "业务A", "businessA");
        when(businessCategoryMapper.getAllCategory()).thenReturn(new ArrayList<>(List.of(catA)));
        when(dataModelDisplayMapper.selectAllDataModelRelation(any())).thenReturn(new ArrayList<>());

        CategoryTreeRequest request = new CategoryTreeRequest();
        request.setModelLayerType(ModelLayer.DWD);
        mockMvc.perform(post("/data-model/category/tree")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk());
    }

    @Test
    @DisplayName("hasUnreadError筛选")
    void testCategoryTree_hasUnreadError() throws Exception {
        BusinessCategory catA = new BusinessCategory("1", "业务A", "businessA");
        when(businessCategoryMapper.getAllCategory()).thenReturn(new ArrayList<>(List.of(catA)));
        when(dataModelDisplayMapper.selectAllDataModelRelation(any())).thenReturn(new ArrayList<>());

        CategoryTreeRequest request = new CategoryTreeRequest();
        request.setHasUnreadError(true);
        mockMvc.perform(post("/data-model/category/tree")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk());
    }

    @Test
    @DisplayName("search模糊匹配-业务分类")
    void testCategoryTree_searchBusinessCategory() throws Exception {
        BusinessCategory catA = new BusinessCategory("1", "业务A", "businessA");
        BusinessCategory catB = new BusinessCategory("2", "业务B", "businessB");
        catA.setId(1);

        MetadataRelation relA = new MetadataRelation();
        relA.setCategoryId(1);
        relA.setModelLayer(ModelLayer.DWD);
        relA.setDataModelName("模型A");
        relA.setDataModelEnName("modelA");
        relA.setStatus(ModelExecuteStatus.START);
        when(businessCategoryMapper.getAllCategory()).thenReturn(new ArrayList<>(List.of(catA, catB)));
        when(dataModelDisplayMapper.selectAllDataModelRelation(any())).thenReturn(new ArrayList<>(List.of(relA)));

        CategoryTreeRequest request = new CategoryTreeRequest();
        request.setSearch("业务A");
        mockMvc.perform(post("/data-model/category/tree")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$[0].enName", is("业务A")));
    }

    @Test
    @DisplayName("search模糊匹配-数据模型")
    void testCategoryTree_searchDataModel() throws Exception {
        BusinessCategory catA = new BusinessCategory("1", "业务A", "businessA");
        BusinessCategory catB = new BusinessCategory("2", "业务B", "businessB");
        catA.setId(1);

        MetadataRelation relA = new MetadataRelation();
        relA.setCategoryId(1);
        relA.setModelLayer(ModelLayer.DWD);
        relA.setDataModelName("模型A");
        relA.setDataModelEnName("modelA");
        relA.setStatus(ModelExecuteStatus.START);
        when(businessCategoryMapper.getAllCategory()).thenReturn(new ArrayList<>(List.of(catA, catB)));
        when(dataModelDisplayMapper.selectAllDataModelRelation(any())).thenReturn(new ArrayList<>(List.of(relA)));

        CategoryTreeRequest request = new CategoryTreeRequest();
        request.setSearch("模型A");
        mockMvc.perform(post("/data-model/category/tree")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$[0].children[0].children[0].name", is("模型A")));
    }

    @Test
    @DisplayName("多参数组合筛选")
    void testCategoryTree_multiFilter() throws Exception {
        BusinessCategory catA = new BusinessCategory("1", "业务A", "businessA");
        when(businessCategoryMapper.getAllCategory()).thenReturn(new ArrayList<>(List.of(catA)));
        MetadataRelation relA = new MetadataRelation();
        relA.setCategoryId(1);
        relA.setModelLayer(ModelLayer.DWD);
        relA.setDataModelName("模型A");
        relA.setDataModelEnName("modelA");
        relA.setStatus(ModelExecuteStatus.START);
        when(dataModelDisplayMapper.selectAllDataModelRelation(any())).thenReturn(new ArrayList<>(List.of(relA)));

        CategoryTreeRequest request = new CategoryTreeRequest();
        request.setEnableStatus(ModelExecuteStatus.START);
        request.setCreateTableStatus(CreateTableStatus.SUCCESS);
        request.setArrangeStatus(true);
        request.setConnectionType(ConnectionType.MYSQL);
        request.setDataSourceCategory(DataSourceCategory.DATA_BASE);
        request.setModelLayerType(ModelLayer.DWD);
        request.setHasUnreadError(true);
        request.setSearch("模型A");
        mockMvc.perform(post("/data-model/category/tree")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk());
    }

    @Test
    void getCategoryDetails_Success() throws Exception {
        try (MockedStatic<BeanUtil> utilities = mockStatic(BeanUtil.class)) {
            utilities.when(() -> BeanUtil.getBean(DynamicUserNameService.class)).thenReturn(dynamicUserNameService);

            BusinessCategory category = new BusinessCategory();
            category.setId(1);
            when(businessCategoryMapper.selectById(1)).thenReturn(category);

            mockMvc.perform(get("/data-model/category/1"))
                .andExpect(status().isOk());
        }
    }

    @Test
    void deleteCategory_Success() throws Exception {
        // 模拟无关联数据
        when(dataModelMapper.selectByCategoryId(1)).thenReturn(Collections.emptyList());
        when(businessCategoryMapper.deleteById(1)).thenReturn(1);

        mockMvc.perform(delete("/data-model/category/1"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$").value(true));

        verify(dataStandardFieldMapper).deleteByCategoryId(1);
    }


    @Test
    void checkCategoryZhName_Exists() throws Exception {
        when(businessCategoryMapper.existByZhName("name")).thenReturn(true);

        mockMvc.perform(get("/data-model/category/check-zh-name")
                .param("zhName", "name"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$").value(true));
    }

    @Test
    void checkCategoryEnName_Exists() throws Exception {
        when(businessCategoryMapper.existByEnName("name")).thenReturn(true);

        mockMvc.perform(get("/data-model/category/check-en-name")
                .param("enName", "name"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$").value(true));
    }

    @Test
    void getCategoryList_Success() throws Exception {
        BusinessCategory category = new BusinessCategory();
        category.setId(1);
        category.setZhName("Test");
        when(businessCategoryMapper.selectList(null)).thenReturn(List.of(category));

        mockMvc.perform(get("/data-model/category/list"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$[0].id").value(1))
            .andExpect(jsonPath("$[0].name").value("Test"));
    }

    @Test
    void getCategoryTree_Success() throws Exception {
        CategoryTreeRequest request = new CategoryTreeRequest();
        when(dataModelCategoryService.getCategoryTree(request))
            .thenReturn(Collections.singletonList(new CategoryTreeResponse()));

        mockMvc.perform(post("/data-model/category/tree")
                .contentType(MediaType.APPLICATION_JSON)
                .content(asJsonString(request)))
            .andExpect(status().isOk());
    }

    @Test
    void getDataSourceConnectionTree_Success() throws Exception {
        when(dataModelCategoryService.getDataSourceConnectionTree())
            .thenReturn(Collections.singletonList(new DataSourceConnectionResponse()));

        mockMvc.perform(get("/data-model/category/data-source-connection/tree"))
            .andExpect(status().isOk());
    }

    @Test
    @DisplayName("dataSourceCategory筛选")
    void testCategoryTree_dataSourceCategory() throws Exception {
        BusinessCategory catA = new BusinessCategory("1", "业务A", "businessA");
        when(businessCategoryMapper.getAllCategory()).thenReturn(new ArrayList<>(List.of(catA)));
        when(dataModelDisplayMapper.selectAllDataModelRelation(any())).thenReturn(new ArrayList<>());

        CategoryTreeRequest request = new CategoryTreeRequest();
        request.setDataSourceCategory(DataSourceCategory.DATA_BASE);
        mockMvc.perform(post("/data-model/category/tree")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk());
    }

    @Test
    @DisplayName("dataSourceCategory与connectionType同时筛选")
    void testCategoryTree_dataSourceCategoryAndConnectionType() throws Exception {
        BusinessCategory catA = new BusinessCategory("1", "业务A", "businessA");
        when(businessCategoryMapper.getAllCategory()).thenReturn(new ArrayList<>(List.of(catA)));
        when(dataModelDisplayMapper.selectAllDataModelRelation(any())).thenReturn(new ArrayList<>());

        CategoryTreeRequest request = new CategoryTreeRequest();
        request.setDataSourceCategory(DataSourceCategory.DATA_BASE);
        request.setConnectionType(ConnectionType.MYSQL);
        mockMvc.perform(post("/data-model/category/tree")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk());
    }

    @Test
    @DisplayName("测试CategoryTreeRequest动态获取ConnectionType列表")
    void testCategoryTreeRequest_getConnectionTypesByCategory() {
        CategoryTreeRequest request = new CategoryTreeRequest();

        // 测试DATA_BASE类型
        request.setDataSourceCategory(DataSourceCategory.DATA_BASE);
        List<ConnectionType> dataBaseTypes = request.getConnectionTypesByCategory();
        assertNotNull(dataBaseTypes);
        assertTrue(dataBaseTypes.contains(ConnectionType.MYSQL));
        assertTrue(dataBaseTypes.contains(ConnectionType.ORACLE));
        assertTrue(dataBaseTypes.contains(ConnectionType.POSTGRESQL));

        // 测试MQ类型
        request.setDataSourceCategory(DataSourceCategory.MQ);
        List<ConnectionType> mqTypes = request.getConnectionTypesByCategory();
        assertNotNull(mqTypes);
        assertTrue(mqTypes.contains(ConnectionType.KAFKA));
        assertTrue(mqTypes.contains(ConnectionType.ROCKETMQ));

        // 测试null情况
        request.setDataSourceCategory(null);
        List<ConnectionType> nullTypes = request.getConnectionTypesByCategory();
        assertTrue(nullTypes.isEmpty());
    }
}