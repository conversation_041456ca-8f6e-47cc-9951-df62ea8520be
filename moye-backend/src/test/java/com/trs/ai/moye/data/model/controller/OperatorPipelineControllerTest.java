package com.trs.ai.moye.data.model.controller;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.trs.ai.moye.data.model.dto.arrangement.batch.BatchOperatorDTO;
import com.trs.ai.moye.data.model.dto.arrangement.batch.BatchOperatorPipelineDTO;
import com.trs.ai.moye.data.model.dto.arrangement.stream.OperatorDto;
import com.trs.ai.moye.data.model.dto.arrangement.stream.OperatorPipelineDTO;
import com.trs.ai.moye.data.model.service.BatchOperatorPipelineService;
import com.trs.ai.moye.data.model.service.OperatorPipelineService;
import com.trs.moye.ability.entity.Schema;
import com.trs.moye.ability.entity.Schema.BaseTypeSchema;
import com.trs.moye.ability.entity.operator.OperatorCanvas;
import com.trs.moye.ability.entity.operator.OperatorRowType;
import com.trs.moye.ability.enums.AbilityFieldType;
import com.trs.moye.ability.enums.ArrangeNodeType;
import com.trs.moye.base.common.utils.JsonUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(MockitoExtension.class)
class OperatorPipelineControllerTest {

    @InjectMocks
    private OperatorPipelineController operatorPipelineController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(operatorPipelineController).build();
    }

    @Mock
    private OperatorPipelineService operatorPipelineService;
    @Mock
    private BatchOperatorPipelineService batchOperatorPipelineService;

    @Test
    void saveOperatorPipeline_WithValidRequest_ShouldReturnOk() throws Exception {
        // 准备测试数据
        OperatorPipelineDTO dto = new OperatorPipelineDTO();
        dto.setDataModelId(1);
        dto.setCanvas(new OperatorCanvas());

        // 添加输入字段

        Schema.BaseTypeSchema nameSchema = new Schema.BaseTypeSchema();
        nameSchema.setEnName("name");
        nameSchema.setZhName("姓名");
        nameSchema.setType(AbilityFieldType.STRING);
        OperatorRowType inputFields = new OperatorRowType();
        inputFields.put("name", nameSchema);
        dto.setInputFields(inputFields);

        // 添加字段映射
        Map<String, List<String>> fieldMapping = new HashMap<>();
        fieldMapping.put("userName", List.of("name"));
        dto.setFieldMapping(fieldMapping);

        List<OperatorDto> operators = new ArrayList<>();
        dto.setOperators(operators);

        doNothing().when(operatorPipelineService).saveOperatorPipeline(1, dto);

        // 执行请求并验证结果
        mockMvc.perform(post("/data-model/arrangement/operator-pipeline")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(dto)))
            .andExpect(status().isOk());
    }

    @Test
    void getOperatorPipeline_WhenExists_ShouldReturnOk() throws Exception {
        // 准备测试数据
        OperatorPipelineDTO mockDto = new OperatorPipelineDTO();
        mockDto.setDataModelId(1);
        mockDto.setId(1L);
        mockDto.setCanvas(new OperatorCanvas());

        // 添加输入字段
        Schema.BaseTypeSchema nameSchema = new Schema.BaseTypeSchema();
        nameSchema.setEnName("name");
        nameSchema.setZhName("姓名");
        nameSchema.setType(AbilityFieldType.STRING);
        OperatorRowType inputFields = new OperatorRowType();
        inputFields.put("name", nameSchema);
        mockDto.setInputFields(inputFields);

        // 添加字段映射
        Map<String, List<String>> fieldMapping = new HashMap<>();
        fieldMapping.put("userName", List.of("name"));
        mockDto.setFieldMapping(fieldMapping);

        when(operatorPipelineService.getOperatorPipeline(1)).thenReturn(mockDto);

        // 执行请求并验证结果
        mockMvc.perform(get("/data-model/arrangement/operator-pipeline/1"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.dataModelId").value(1))
            .andExpect(jsonPath("$.id").value(1))
            .andExpect(jsonPath("$.inputFields.name.zhName").value("姓名"))
            .andExpect(jsonPath("$.fieldMapping.userName[0]").value("name"));
    }

    @Test
    void saveBatchOperatorPipeline_WithValidRequest_ShouldReturnOk() throws Exception {
        // Prepare test data
        BatchOperatorDTO dto1 = new BatchOperatorDTO();
        dto1.setDisplayId(1L);
        dto1.setType(ArrangeNodeType.OPERATOR);
        dto1.setName("name");
        OperatorRowType operatorRowType = new OperatorRowType();
        BaseTypeSchema schema = new BaseTypeSchema();
        schema.setEnName("age");
        operatorRowType.put("age", schema);
        dto1.setInputFields(operatorRowType);

        BatchOperatorDTO dto2 = new BatchOperatorDTO();
        dto2.setDisplayId(2L);
        dto2.setType(ArrangeNodeType.OPERATOR);
        dto2.setName("name2");

        BatchOperatorPipelineDTO dto = new BatchOperatorPipelineDTO();
        dto.setOperators(List.of(dto1, dto2));

        // Perform request and verify result
        mockMvc.perform(post("/data-model/arrangement/batch/1").contentType(MediaType.APPLICATION_JSON)
            .content(JsonUtils.toJsonString(dto))).andExpect(status().isOk());
    }

    @Test
    void getBatchOperatorPipeline_WhenExists_ShouldReturnOk() throws Exception {
        // Prepare test data
        BatchOperatorDTO dto1 = new BatchOperatorDTO();
        dto1.setDisplayId(1L);
        dto1.setType(ArrangeNodeType.OPERATOR);
        dto1.setName("name");
        BatchOperatorPipelineDTO mockDto = new BatchOperatorPipelineDTO();
        mockDto.setOperators(List.of(dto1));

        when(batchOperatorPipelineService.getBatchOperatorPipeline(1)).thenReturn(mockDto);

        // Perform request and verify result
        mockMvc.perform(get("/data-model/arrangement/batch/1")).andExpect(status().isOk())
            .andExpect(jsonPath("$.operators[0].name").value("name"));
    }
}