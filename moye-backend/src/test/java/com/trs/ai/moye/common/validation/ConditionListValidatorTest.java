package com.trs.ai.moye.common.validation;

import com.trs.ai.moye.data.service.entity.DataServiceField;
import com.trs.ai.moye.data.service.entity.query.Condition;
import com.trs.ai.moye.data.service.entity.query.ValueObject;
import com.trs.ai.moye.data.service.enums.DataServiceConditionType;
import com.trs.moye.base.common.utils.JsonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.validation.ConstraintValidatorContext;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * ConditionListValidator 单元测试
 * 测试条件表达式数组校验器的有效性，覆盖所有业务场景
 *
 * <AUTHOR>
 * @since 2025/01/27
 */
class ConditionListValidatorTest {

    private ConditionListValidator validator;
    
    @Mock
    private ConstraintValidatorContext context;
    
    @Mock
    private ConstraintValidatorContext.ConstraintViolationBuilder violationBuilder;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        validator = new ConditionListValidator();
        
        // 设置Mock行为
        when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(violationBuilder);
    }

    @Test
    @DisplayName("空条件列表应校验通过")
    void testIsValid_EmptyList() {
        List<Condition> conditions = new ArrayList<>();
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("null条件列表应校验通过")
    void testIsValid_NullList() {
        boolean result = validator.isValid(null, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("简单有效条件应校验通过")
    void testIsValid_SimpleValidCondition() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("id", "=", "1")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("括号不匹配应校验失败")
    void testIsValid_BracketMismatch() {
        List<Condition> conditions = Arrays.asList(
            createLogicCondition("("),
            createExpressionCondition("id", "=", "1")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("右括号多于左括号应校验失败")
    void testIsValid_RightBracketMoreThanLeft() {
        List<Condition> conditions = Arrays.asList(
            createLogicCondition(")"),
            createExpressionCondition("id", "=", "1")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("非法操作符应校验失败")
    void testIsValid_IllegalOperator() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("id", "illegal_operator", "1")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("null操作符应校验失败")
    void testIsValid_NullOperator() {
        Condition condition = createExpressionCondition("id", "=", "1");
        condition.setOperator(null);
        List<Condition> conditions = List.of(condition);
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("以and开头应校验失败")
    void testIsValid_StartWithAnd() {
        List<Condition> conditions = Arrays.asList(
            createLogicCondition("and"),
            createExpressionCondition("id", "=", "1")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("以or开头应校验失败")
    void testIsValid_StartWithOr() {
        List<Condition> conditions = Arrays.asList(
            createLogicCondition("or"),
            createExpressionCondition("id", "=", "1")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("以右括号开头应校验失败")
    void testIsValid_StartWithRightBracket() {
        List<Condition> conditions = Arrays.asList(
            createLogicCondition(")"),
            createExpressionCondition("id", "=", "1")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("以and结尾应校验失败")
    void testIsValid_EndWithAnd() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("id", "=", "1"),
            createLogicCondition("and")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("以左括号结尾应校验失败")
    void testIsValid_EndWithLeftBracket() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("id", "=", "1"),
            createLogicCondition("(")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("连续and逻辑符号应校验失败")
    void testIsValid_ConsecutiveAnd() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("id", "=", "1"),
            createLogicCondition("and"),
            createLogicCondition("and"),
            createExpressionCondition("age", ">=", "18")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("and后紧跟not应校验通过")
    void testIsValid_AndFollowedByNot() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("id", "=", "1"),
            createLogicCondition("and"),
            createLogicCondition("not"),
            createExpressionCondition("age", ">=", "18")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("and前不是表达式或右括号应校验失败")
    void testIsValid_AndPrecededByInvalid() {
        List<Condition> conditions = Arrays.asList(
            createLogicCondition("("),
            createLogicCondition("and"),
            createExpressionCondition("id", "=", "1")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("and后不是表达式或左括号或not应校验失败")
    void testIsValid_AndFollowedByInvalid() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("id", "=", "1"),
            createLogicCondition("and"),
            createLogicCondition("or"),
            createExpressionCondition("age", ">=", "18")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("not后不是表达式或左括号应校验失败")
    void testIsValid_NotFollowedByInvalid() {
        List<Condition> conditions = Arrays.asList(
            createLogicCondition("not"),
            createLogicCondition("and"),
            createExpressionCondition("id", "=", "1")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("not在最后应校验失败")
    void testIsValid_NotAtEnd() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("id", "=", "1"),
            createLogicCondition("not")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("空括号应校验失败")
    void testIsValid_EmptyBrackets() {
        List<Condition> conditions = Arrays.asList(
            createLogicCondition("("),
            createLogicCondition(")")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("括号内全为逻辑符号应校验失败")
    void testIsValid_BracketsWithOnlyLogic() {
        List<Condition> conditions = Arrays.asList(
            createLogicCondition("("),
            createLogicCondition("and"),
            createLogicCondition("or"),
            createLogicCondition(")")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("表达式缺少字段应校验失败")
    void testIsValid_ExpressionMissingField() {
        Condition condition = createExpressionCondition("id", "=", "1");
        condition.setKey(null);
        List<Condition> conditions = Arrays.asList(condition);
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("表达式缺少值应校验失败")
    void testIsValid_ExpressionMissingValues() {
        Condition condition = createExpressionCondition("id", "=", "1");
        condition.setValues(null);
        List<Condition> conditions = Arrays.asList(condition);
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("表达式值为空列表应校验失败")
    void testIsValid_ExpressionEmptyValues() {
        Condition condition = createExpressionCondition("id", "=", "1");
        condition.setValues(new ArrayList<>());
        List<Condition> conditions = Arrays.asList(condition);
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("复杂嵌套有效条件应校验通过")
    void testIsValid_ComplexValidCondition() {
        List<Condition> conditions = Arrays.asList(
            createLogicCondition("("),
            createExpressionCondition("id", "=", "1"),
            createLogicCondition("or"),
            createExpressionCondition("age", ">=", "18"),
            createLogicCondition(")"),
            createLogicCondition("and"),
            createLogicCondition("not"),
            createExpressionCondition("status", "!=", "deleted")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("包含比较操作符的有效条件应校验通过")
    void testIsValid_ComparisonOperators() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("age", ">", "18"),
            createLogicCondition("and"),
            createExpressionCondition("age", "<", "65"),
            createLogicCondition("and"),
            createExpressionCondition("salary", ">=", "5000"),
            createLogicCondition("and"),
            createExpressionCondition("salary", "<=", "100000")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("包含不等于操作符的有效条件应校验通过")
    void testIsValid_NotEqualOperator() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("status", "!=", "deleted")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("包含等于操作符的有效条件应校验通过")
    void testIsValid_EqualOperator() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("id", "=", "123")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("基于用户提供的参数模板的有效条件应校验通过")
    void testIsValid_UserProvidedTemplate() {
        List<Condition> conditions = Arrays.asList(
            createLogicCondition("not"),
            createExpressionCondition("id_number", "contain", "2")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("简单not操作符应校验通过")
    void testIsValid_SimpleNotOperator() {
        List<Condition> conditions = Arrays.asList(
            createLogicCondition("not"),
            createExpressionCondition("status", "=", "deleted")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("复杂嵌套条件组合应校验通过")
    void testIsValid_ComplexNestedCombination() {
        List<Condition> conditions = Arrays.asList(
            createLogicCondition("("),
            createExpressionCondition("department", "=", "IT"),
            createLogicCondition("and"),
            createLogicCondition("("),
            createExpressionCondition("age", ">=", "25"),
            createLogicCondition("or"),
            createExpressionCondition("experience", ">=", "3"),
            createLogicCondition(")"),
            createLogicCondition(")"),
            createLogicCondition("or"),
            createLogicCondition("not"),
            createExpressionCondition("status", "=", "inactive")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("表达式之间缺少逻辑符号应校验失败")
    void testIsValid_ExprWithoutLogicBetween() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("id", "=", "1"),
            createExpressionCondition("age", ">=", "18")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("仅有逻辑符号应校验失败")
    void testIsValid_OnlyLogic() {
        List<Condition> conditions = Arrays.asList(
            createLogicCondition("and"),
            createLogicCondition("or")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("仅有括号应校验失败")
    void testIsValid_OnlyParentheses() {
        List<Condition> conditions = Arrays.asList(
            createLogicCondition("("),
            createLogicCondition(")")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = false;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("简单括号嵌套应校验通过")
    void testIsValid_SimpleBracketNesting() {
        List<Condition> conditions = Arrays.asList(
            createLogicCondition("("),
            createExpressionCondition("id", "=", "1"),
            createLogicCondition("or"),
            createExpressionCondition("id", "=", "2"),
            createLogicCondition(")")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    // 辅助方法：创建表达式条件
    private Condition createExpressionCondition(String fieldName, String operator, String... values) {
        Condition condition = new Condition();
        condition.setType(DataServiceConditionType.EXPRESSION);
        
        DataServiceField key = new DataServiceField();
        key.setEnName(fieldName);
        key.setZhName(fieldName);
        key.setTypeName("字符串");
        condition.setKey(key);
        
        condition.setOperator(operator);
        
        List<ValueObject> valueObjects = new ArrayList<>();
        for (String value : values) {
            valueObjects.add(new ValueObject(value));
        }
        condition.setValues(valueObjects);
        
        return condition;
    }

    // 辅助方法：创建逻辑条件
    private Condition createLogicCondition(String operator) {
        Condition condition = new Condition();
        condition.setType(DataServiceConditionType.LOGIC);
        condition.setOperator(operator);
        condition.setValues(new ArrayList<>());
        condition.setKey(null);
        return condition;
    }

    @Test
    @DisplayName("包含contain操作符的有效条件应校验通过")
    void testIsValid_ContainOperator() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("name", "contain", "test")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("包含in操作符的有效条件应校验通过")
    void testIsValid_InOperator() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("status", "in", "active", "pending")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("包含like操作符的有效条件应校验通过")
    void testIsValid_LikeOperator() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("description", "like", "%test%")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("包含isNull操作符的有效条件应校验通过")
    void testIsValid_IsNullOperator() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("optional_field", "isNull")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("包含isNotNull操作符的有效条件应校验通过")
    void testIsValid_IsNotNullOperator() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("optional_field", "isNotNull")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("包含notIn操作符的有效条件应校验通过")
    void testIsValid_NotInOperator() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("status", "notIn", "deleted", "archived")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("包含notcontain操作符的有效条件应校验通过")
    void testIsValid_NotContainOperator() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("content", "notcontain", "spam")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("包含notLike操作符的有效条件应校验通过")
    void testIsValid_NotLikeOperator() {
        List<Condition> conditions = Arrays.asList(
            createExpressionCondition("description", "notLike", "%error%")
        );
        boolean result = validator.isValid(conditions, context);
        boolean expected = true;
        assertEquals(expected, result);
    }
} 