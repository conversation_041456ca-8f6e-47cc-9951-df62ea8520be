package com.trs.ai.moye.data.standard.service.impl;

import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.ai.moye.data.standard.dao.MetaDataStandardFieldMapper;
import com.trs.ai.moye.data.standard.dao.MetaDataStandardMapper;
import com.trs.ai.moye.data.standard.entity.GraphicsMetaDataStandard;
import com.trs.moye.base.data.standard.enums.MetaDataStandardTypeEnum;
import com.trs.ai.moye.data.standard.response.MetaDataStandardDetailResponse;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import com.trs.moye.base.common.exception.BizException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockedStatic.Verification;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MetaDataStandardServiceImplTest {

    @InjectMocks
    private MetaDataStandardServiceImpl metaDataStandardService;

    @Mock
    private MetaDataStandardMapper metaDataStandardMapper;
    @Mock
    private MetaDataStandardFieldMapper metaDataStandardFieldMapper;
    @Mock
    private DataModelMapper dataModelMapper;
    @Mock
    private DynamicUserNameService dynamicUserNameService;


    @Test
    void addGraphicsMetaDataStandard() {
        GraphicsMetaDataStandard graphicsMetaDataStandard = buildGraphicsMetaDataStandard();
        graphicsMetaDataStandard.setMetaDataStandardCatalogId(1);
        when(metaDataStandardMapper.countByEnNameAndExcludeId(graphicsMetaDataStandard.getEnName(),
            graphicsMetaDataStandard.getId())).thenReturn(0);
        Assertions.assertDoesNotThrow(
            () -> metaDataStandardService.addGraphicsMetaDataStandard(graphicsMetaDataStandard));
    }

    @Test
    void deleteMetaDataStandard() {
        Integer inUserId = 1;
        when(dataModelMapper.countByMetaDataStandardId(inUserId)).thenReturn(1);
        Assertions.assertThrowsExactly(BizException.class,
            () -> metaDataStandardService.deleteMetaDataStandard(inUserId));
        Integer unUsedId = 2;
        when(dataModelMapper.countByMetaDataStandardId(unUsedId)).thenReturn(0);
        Assertions.assertDoesNotThrow(() -> metaDataStandardService.deleteMetaDataStandard(unUsedId));
    }

    @Test
    void updateGraphicsMetaDataStandard() {
        GraphicsMetaDataStandard graphicsMetaDataStandard = buildGraphicsMetaDataStandard();
        graphicsMetaDataStandard.setId(1);
        graphicsMetaDataStandard.setMetaDataStandardCatalogId(1);
        when(metaDataStandardMapper.countByEnNameAndExcludeId(graphicsMetaDataStandard.getEnName(),
            graphicsMetaDataStandard.getId())).thenReturn(0);
        Assertions.assertDoesNotThrow(
            () -> metaDataStandardService.updateGraphicsMetaDataStandard(graphicsMetaDataStandard));
    }

    private static GraphicsMetaDataStandard buildGraphicsMetaDataStandard() {
        GraphicsMetaDataStandard graphicsMetaDataStandard = new GraphicsMetaDataStandard();
        graphicsMetaDataStandard.setType(MetaDataStandardTypeEnum.GRAPHICS);
        graphicsMetaDataStandard.setEnName("graphics");
        graphicsMetaDataStandard.setZhName("图形化");
        graphicsMetaDataStandard.setCreateBy(1);
        graphicsMetaDataStandard.setUpdateBy(1);
        return graphicsMetaDataStandard;
    }

    @Test
    void queryMetaDataStandardDetailById() {
        GraphicsMetaDataStandard graphicsMetaDataStandard = buildGraphicsMetaDataStandard();
        // mock
        when(metaDataStandardMapper.selectById(1)).thenReturn(graphicsMetaDataStandard);
        MockedStatic<BeanUtil> mockedStatic = mockStatic(BeanUtil.class);
        mockedStatic.when((Verification) BeanUtil.getBean(DynamicUserNameService.class))
            .thenReturn(dynamicUserNameService);
        String admin = "admin";
        when(dynamicUserNameService.getUserName(graphicsMetaDataStandard.getCreateBy())).thenReturn(admin);
        when(dynamicUserNameService.getUserName(graphicsMetaDataStandard.getUpdateBy())).thenReturn(admin);
        // act
        MetaDataStandardDetailResponse metaDataStandardDetailResponse = metaDataStandardService.queryMetaDataStandardDetailById(
            1);
        // assert
        Assertions.assertEquals(metaDataStandardDetailResponse.getZhName(), graphicsMetaDataStandard.getZhName());
        Assertions.assertEquals(metaDataStandardDetailResponse.getUpdateBy(), admin);
        mockedStatic.close();
    }
}