package com.trs.ai.moye.data.model.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.ai.moye.data.connection.response.DataConnectionStatisticsResponse;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.dao.DataConnectionMonitorConfigMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.DataConnectionMonitorConfig;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import javax.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

/**
 * DataConnectionDisplayMapperTest
 *
 * <AUTHOR>
 * @since 2025/7/14 9:44
 */
@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class DataConnectionDisplayMapperTest {

    @Resource
    private DataConnectionDisplayMapper dataConnectionDisplayMapper;
    @Resource
    private DataConnectionMapper dataConnectionMapper;
    @Resource
    private DataConnectionMonitorConfigMapper dataConnectionMonitorConfigMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        jdbcTemplate.execute("TRUNCATE TABLE data_connection");
        jdbcTemplate.execute("TRUNCATE TABLE data_connection_monitor_config");
    }

    @Test
    void countStatistics() {
        DataConnection dataConnection1 = new DataConnection();
        dataConnection1.setName("dataConnection1");
        dataConnection1.setConnectionType(ConnectionType.MYSQL);
        dataConnection1.setSource(true);
        dataConnection1.setTestSuccess(true);
        dataConnectionMapper.insert(dataConnection1);

        DataConnection dataConnection2 = new DataConnection();
        dataConnection2.setName("dataConnection2");
        dataConnection2.setConnectionType(ConnectionType.MYSQL);
        dataConnection2.setSource(false);
        dataConnection2.setTestSuccess(true);
        dataConnectionMapper.insert(dataConnection2);

        DataConnectionMonitorConfig config = new DataConnectionMonitorConfig(dataConnection1.getId(), true, 100);
        dataConnectionMonitorConfigMapper.insert(config);

        DataConnectionStatisticsResponse response = dataConnectionDisplayMapper.countStatistics(
            null);
        assertEquals(2, response.getTotalCount());

        DataConnectionStatisticsResponse response1 = dataConnectionDisplayMapper.countStatistics(
            true);
        assertEquals(1, response1.getTotalCount());
    }
}