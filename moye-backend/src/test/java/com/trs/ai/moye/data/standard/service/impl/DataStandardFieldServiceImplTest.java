package com.trs.ai.moye.data.standard.service.impl;

import com.trs.ai.moye.data.standard.dao.DataStandardFieldMapper;
import com.trs.ai.moye.data.standard.entity.ImportFieldDetail;
import com.trs.ai.moye.data.standard.response.DataStandardImportResponse;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.data.standard.entity.DataStandardField;
import java.util.ArrayList;
import java.util.List;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.multipart.MultipartFile;

@ExtendWith(MockitoExtension.class)
class DataStandardFieldServiceImplTest {


    @Mock
    private DataStandardFieldMapper dataStandardFieldMapper;

    @InjectMocks
    private DataStandardFieldServiceImpl dataStandardFieldService;

    @Mock
    private MultipartFile file;


    @Test
    void testDoImport_Success() {
        // Arrange
        List<ImportFieldDetail> successData = new ArrayList<>();

        List<ImportFieldDetail> failData = new ArrayList<>();
        ImportFieldDetail importFieldDetail = new ImportFieldDetail();
        importFieldDetail.setZhName("字段1");
        importFieldDetail.setEnName("field1");
        importFieldDetail.setType("STRING");
        importFieldDetail.setTypeName("字符串");
        importFieldDetail.setMultiValue("是");
        importFieldDetail.setNullable("是");
        importFieldDetail.setStatistic("是");
        importFieldDetail.setDescription("描述");
        importFieldDetail.setMinValue("最小值");
        importFieldDetail.setMaxValue("最大值");
        importFieldDetail.setDefaultValue("默认值");
        importFieldDetail.setMinLength(0);
        importFieldDetail.setMaxLength(0);
        importFieldDetail.setAccuracy(0);
        importFieldDetail.setScale(0);
        importFieldDetail.setEnumValues("枚举1,枚举2");

        successData.add(importFieldDetail);

        DataStandardField dataStandardField = importFieldDetail.toDataStandardField();
        when(dataStandardFieldMapper.getByZhNameOrEnName(dataStandardField.getZhName(),
            dataStandardField.getEnName(), 1)).thenReturn(null);

        // Act
        DataStandardImportResponse response = dataStandardFieldService.doImport(successData, 1, failData);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getSuccessData().size());
        assertEquals(0, response.getFailData().size());
    }

    @Test
    void testDoImport_Failure() {
        List<ImportFieldDetail> successData = new ArrayList<>();
        List<ImportFieldDetail> failData = new ArrayList<>();
        // Arrange
        ImportFieldDetail importFieldDetail = new ImportFieldDetail();
        importFieldDetail.setZhName("蒙利幸测试标题1");
        importFieldDetail.setEnName("mlx_test_title1");
        importFieldDetail.setType("STRING");
        importFieldDetail.setTypeName("字符串");
        importFieldDetail.setMultiValue("是");
        importFieldDetail.setNullable("是");
        importFieldDetail.setStatistic("是");
        importFieldDetail.setDescription("描述");
        importFieldDetail.setMinValue("最小值");
        importFieldDetail.setMaxValue("最大值");
        importFieldDetail.setDefaultValue("默认值");
        importFieldDetail.setMinLength(0);
        importFieldDetail.setMaxLength(0);
        importFieldDetail.setAccuracy(0);
        importFieldDetail.setScale(0);
        importFieldDetail.setEnumValues("枚举1,枚举2");

        successData.add(importFieldDetail);

        DataStandardField dataStandardField = importFieldDetail.toDataStandardField();
        when(dataStandardFieldMapper.getByZhNameOrEnName(dataStandardField.getZhName(),
            dataStandardField.getEnName(), 1)).thenThrow(new BizException("中文名或英文名重复"));

        // Act
        DataStandardImportResponse response = dataStandardFieldService.doImport(successData, 1, failData);

        // Assert
        assertNotNull(response);
        assertEquals(0, response.getSuccessData().size());
        assertEquals(1, response.getFailData().size());
    }

}