package com.trs.ai.moye.monitor.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.ai.moye.monitor.entity.BasicComponentDetectionStatistics;
import java.time.LocalDateTime;
import java.util.Map;
import javax.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

/**
 * BasicComponentDetectionStatisticsMapperTest
 *
 * <AUTHOR>
 * @since 2025/7/15 14:27
 */
@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class BasicComponentDetectionStatisticsMapperTest {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Resource
    private BasicComponentDetectionStatisticsMapper basicComponentDetectionStatisticsMapper;

    @BeforeEach
    void setUp() {
        jdbcTemplate.execute("TRUNCATE TABLE `basic_component_detection_statistics`");
    }

    @Test
    void selectAsMapOrderByStatusAndTime() {
        basicComponentDetectionStatisticsMapper.insert(
            new BasicComponentDetectionStatistics(1L, "test", null, 1, 1, 1, LocalDateTime.now(), LocalDateTime.now(),
                ""));
        Map<String, BasicComponentDetectionStatistics> name2EntityMap = basicComponentDetectionStatisticsMapper.selectAsMap();
        assertNotNull(name2EntityMap);
        assertEquals(1, name2EntityMap.size());
    }
}