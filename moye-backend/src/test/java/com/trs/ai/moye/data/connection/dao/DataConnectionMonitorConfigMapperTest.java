package com.trs.ai.moye.data.connection.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.dao.DataConnectionMonitorConfigMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.DataConnectionMonitorConfig;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import java.util.List;
import javax.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

/**
 * DataConnectionMonitorConfigMapperTest
 *
 * <AUTHOR>
 * @since 2025/7/9 15:36
 */
@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class DataConnectionMonitorConfigMapperTest {

    @Resource
    private DataConnectionMonitorConfigMapper dataConnectionMonitorConfigMapper;
    @Resource
    private DataConnectionMapper dataConnectionMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        jdbcTemplate.execute("TRUNCATE TABLE `data_connection_monitor_config`");
        jdbcTemplate.execute("TRUNCATE TABLE `data_connection`");
    }

    @Test
    void selectByConnectionId() {
        DataConnectionMonitorConfig config = new DataConnectionMonitorConfig(1, true, 100);
        dataConnectionMonitorConfigMapper.insert(config);

        DataConnectionMonitorConfig result = dataConnectionMonitorConfigMapper.selectByConnectionId(1);
        assertNotNull(result);
        assertEquals(config.getXxlJobId(), result.getXxlJobId());

        DataConnectionMonitorConfig result2 = dataConnectionMonitorConfigMapper.selectByConnectionId(2);
        assertNull(result2);
    }

    @Test
    void selectAllWithConnectionParams() {
        DataConnection dataConnection1 = new DataConnection();
        dataConnection1.setName("dataConnection1");
        dataConnection1.setConnectionType(ConnectionType.MYSQL);
        dataConnection1.setSource(true);
        dataConnection1.setTestSuccess(true);
        dataConnectionMapper.insert(dataConnection1);

        DataConnection dataConnection2 = new DataConnection();
        dataConnection2.setName("dataConnection2");
        dataConnection2.setConnectionType(ConnectionType.MYSQL);
        dataConnection2.setSource(true);
        dataConnection2.setTestSuccess(true);
        dataConnectionMapper.insert(dataConnection2);

        DataConnectionMonitorConfig config = new DataConnectionMonitorConfig(dataConnection1.getId(), true, 100);
        dataConnectionMonitorConfigMapper.insert(config);

        List<DataConnection> result = dataConnectionMonitorConfigMapper.selectAllWithConnectionParams();
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("dataConnection1", result.get(0).getName());
    }
}