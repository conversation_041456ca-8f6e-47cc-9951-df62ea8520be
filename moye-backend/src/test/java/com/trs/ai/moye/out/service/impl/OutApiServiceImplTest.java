package com.trs.ai.moye.out.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.trs.ai.moye.data.model.dao.DataConnectionDisplayMapper;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.ai.moye.data.model.service.DataModelService;
import com.trs.ai.moye.data.service.dto.TableStructDto;
import com.trs.ai.moye.data.service.request.SqlRequest;
import com.trs.ai.moye.data.service.response.NativeStatementExecuteResult;
import com.trs.ai.moye.data.service.response.StorageInfoListResponse;
import com.trs.ai.moye.out.request.NebulaDataTransferRequest;
import com.trs.ai.moye.out.request.NebulaSearchRequest;
import com.trs.ai.moye.out.util.nebulaparser.NebulaParser;
import com.trs.ai.moye.storageengine.response.StorageSearchResponse;
import com.trs.ai.moye.storageengine.service.StorageEngineService;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.exception.BizException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class OutApiServiceImplTest {

    @Mock
    private DataConnectionMapper dataConnectionMapper;
    @Mock
    private DataConnectionDisplayMapper dataConnectionDisplayMapper;
    @Mock
    private DataStorageMapper dataStorageMapper;
    @Mock
    private DataModelFieldMapper dataModelFieldMapper;
    @Mock
    private DataModelMapper dataModelMapper;
    @Mock
    private StorageEngineService storageEngineService;
    @Mock
    private NebulaParser nebulaParser;
    @Mock
    private DataModelService dataModelService;

    @InjectMocks
    private OutApiServiceImpl outApiService;

    private DataConnection nebulaConnection;
    private DataConnection mysqlConnection;

    @BeforeEach
    void setUp() {
        nebulaConnection = new DataConnection();
        nebulaConnection.setConnectionType(ConnectionType.NEBULA);

        mysqlConnection = new DataConnection();
        mysqlConnection.setConnectionType(ConnectionType.MYSQL);
        mysqlConnection.setName("mysql测试");
    }

    @Test
    void getDbInfoByType_ValidDbTypes_ReturnsCorrectResponse() {
        // Arrange
        List<Integer> dbTypes = List.of(1, 2);
        when(dataConnectionDisplayMapper.selectAllConnections(any()))
            .thenReturn(Collections.singletonList(nebulaConnection))
            .thenReturn(Collections.singletonList(mysqlConnection));

        // Act
        List<StorageInfoListResponse> result = outApiService.getDbInfoByType(dbTypes);

        // Assert
        assertEquals(2, result.size());
        assertEquals(1, result.get(0).getDbType());
        assertEquals(2, result.get(1).getDbType());
    }

    @Test
    void getDbInfoByType_InvalidDbType_ThrowsBizException() {
        // Arrange
        List<Integer> dbTypes = List.of(99);

        // Act & Assert
        assertThrows(BizException.class, () -> outApiService.getDbInfoByType(dbTypes));
    }

    @Test
    void executeSQL_ValidRequest_ReturnsQueryResult() {
        // Arrange
        SqlRequest request = new SqlRequest();
        request.setDbId(1);
        StorageSearchResponse mockResponse = new StorageSearchResponse();
        mockResponse.setItems(Collections.singletonList(Map.of("key", "value")));
        when(storageEngineService.codeQuery(anyInt(), anyString(), any()))
            .thenReturn(mockResponse);

        when(dataConnectionMapper.selectById((Integer) any()))
            .thenReturn(mysqlConnection);
        // Act
        NativeStatementExecuteResult<Map<String, Object>> result = outApiService.executeSQL(request);

        // Assert
        assertEquals(1, result.getItems().size());
        assertEquals("value", result.getItems().get(0).get("key"));
    }

    @Test
    void getTableStructInfos_NebulaConnection_ReturnsGraphStructure() {
        // Arrange
        DataStorage storage = new DataStorage();
        storage.setDataModelId(1);

        DataModel dataModel = new DataModel();
        dataModel.setId(1); // 显式设置DataModel的ID

        when(dataConnectionMapper.selectById(1)).thenReturn(nebulaConnection);
        when(dataStorageMapper.selectByConnectionId(1)).thenReturn(Collections.singletonList(storage));
        when(dataModelMapper.selectById(1)).thenReturn(dataModel);
        DataModelField dataModelField = new DataModelField();
        dataModelField.setType(FieldType.GRAPHICS_EDGE);
        when(dataModelFieldMapper.selectByDataModelId(anyInt()))
            .thenReturn(Collections.singletonList(dataModelField));

        // Act
        List<TableStructDto> result = outApiService.getTableStructInfos(1);

        // Assert
        assertEquals(1, result.size());
        assertNotNull(result.get(0).getEdges());
    }

    @Test
    void getTableStructInfos_NonNebulaConnection_ReturnsTableStructure() {
        // Arrange
        DataStorage storage = new DataStorage();
        storage.setFieldIds(List.of(1));
        when(dataConnectionMapper.selectById(1)).thenReturn(mysqlConnection);
        when(dataStorageMapper.selectByConnectionId(1)).thenReturn(Collections.singletonList(storage));
        DataModelField dataModelField = new DataModelField();
        dataModelField.setType(FieldType.INT);
        when(dataModelFieldMapper.selectByIds(any())).thenReturn(Collections.singletonList(dataModelField));

        // Act
        List<TableStructDto> result = outApiService.getTableStructInfos(1);

        // Assert
        assertEquals(1, result.size());
        assertNotNull(result.get(0).getFields());
    }

    @Test
    void nebulaExecute_WithConditionParsing_ReturnsParsedResult() {
        // Arrange
        NebulaSearchRequest request = new NebulaSearchRequest();
        request.setNeedParseCondition(true);
        request.setStatement("MATCH (v) RETURN v");
        request.setDataModelId(1);

        DataModel model = new DataModel();
        model.setId(1);
        DataSourceConfig dataSourceConfig = new DataSourceConfig();
        dataSourceConfig.setConnectionId(2);
        model.setDataSource(List.of(dataSourceConfig));
        when(dataModelMapper.selectById(1)).thenReturn(model);
        when(nebulaParser.parseMatchQuery(anyString())).thenReturn("PARSED_QUERY");
        when(storageEngineService.codeQueryNebulaRawData(anyInt(), any()))
            .thenReturn(new StorageSearchResponse(new ArrayList<>(), 0L));

        // Act
        List<Map<String, Object>> result = outApiService.nebulaExecute(request);

        // Assert
        verify(nebulaParser).parseMatchQuery("MATCH (v) RETURN v");
        assertTrue(result.isEmpty());
    }

    @Test
    void outDeleteDataModel_ValidModelId_DeletesSuccessfully() {
        // Arrange
        DataModel model = new DataModel();
        when(dataModelMapper.selectById(1)).thenReturn(model);

        // Act
        boolean result = outApiService.outDeleteDataModel(1);

        // Assert
        assertTrue(result);
        verify(dataModelMapper).selectById(1);
    }


    @Test
    void nebulaDataTransfer_InvalidModelIds_ThrowsBizException() {
        // Arrange
        when(dataModelMapper.selectById(1)).thenReturn(null);
        when(dataModelMapper.selectById(2)).thenReturn(null);

        // Act & Assert
        NebulaDataTransferRequest request = new NebulaDataTransferRequest(1, 2);
        assertThrows(BizException.class, () -> outApiService.nebulaDataTransfer(request));
    }
}