package com.trs.ai.moye.data.ability.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.ai.moye.data.ability.request.SearchTypeArrayAbilityTreeRequest;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.Schema.BaseTypeSchema;
import com.trs.moye.ability.enums.AbilityType;
import java.util.List;
import javax.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

/**
 * AbilityMapperTest
 *
 * <AUTHOR>
 * @since 2025/6/9 21:16
 */
@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class AbilityMapperTest {

    @Resource
    private AbilityMapper abilityMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        jdbcTemplate.execute("TRUNCATE TABLE ability");

        Ability ability1 = new Ability();
        ability1.setEnName("ability1");
        ability1.setZhName("ability1");
        ability1.setType(AbilityType.BATCH);
        ability1.setPath("");
        ability1.setInputSchema(new BaseTypeSchema());
        ability1.setOutputSchema(new BaseTypeSchema());
        ability1.setIsBatchSupported(Boolean.TRUE);
        abilityMapper.insert(ability1);

        Ability ability2 = new Ability();
        ability2.setEnName("ability2");
        ability2.setZhName("ability2");
        ability2.setIsBatchSupported(Boolean.FALSE);
        ability2.setType(AbilityType.NLP);
        ability2.setPath("");
        ability2.setInputSchema(new BaseTypeSchema());
        ability2.setOutputSchema(new BaseTypeSchema());
        abilityMapper.insert(ability2);
    }

    @Test
    void selectAllByRequest_selectBatchSupportedAbility() {
        SearchTypeArrayAbilityTreeRequest batchRequest = new SearchTypeArrayAbilityTreeRequest();
        batchRequest.setIsBatchSupported(Boolean.TRUE);
        List<Ability> batchSupportedAbility = abilityMapper.selectAllByRequest(batchRequest);
        assertEquals(1, batchSupportedAbility.size());
        assertEquals(Boolean.TRUE, batchSupportedAbility.get(0).getIsBatchSupported());
    }
}