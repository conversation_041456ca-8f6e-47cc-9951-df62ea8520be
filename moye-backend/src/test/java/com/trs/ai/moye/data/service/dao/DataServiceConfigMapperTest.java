package com.trs.ai.moye.data.service.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.ai.moye.data.service.entity.DataServiceConfig;
import com.trs.ai.moye.data.service.entity.params.CodeModeParams;
import com.trs.ai.moye.data.service.entity.params.ServiceQueryParams;
import com.trs.ai.moye.data.service.enums.ServiceConfigType;
import javax.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

/**
 * 数据服务配置 Mapper 测试
 *
 * <AUTHOR>
 * @since 2024/10/15 18:31:33
 */
@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class DataServiceConfigMapperTest {

    @Resource
    private DataServiceConfigMapper dataServiceConfigMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @BeforeEach
    void setUp() {
        jdbcTemplate.execute("TRUNCATE TABLE data_service_config");
    }

    @Test
    void selectOneByDataServiceId() {
        // Arrange
        DataServiceConfig dataServiceConfig = new DataServiceConfig();
        dataServiceConfig.setDataServiceId(1);
        dataServiceConfig.setType(ServiceConfigType.CODE);
        CodeModeParams params = new CodeModeParams();
        params.setType(ServiceConfigType.CODE);
        params.setCodeBlock("CODE_BLOCK");
        dataServiceConfig.setParams(params);
        dataServiceConfigMapper.insert(dataServiceConfig);
        // Act
        DataServiceConfig result = dataServiceConfigMapper.selectOneByDataServiceId(dataServiceConfig.getDataServiceId());

        assertNotNull(result);
        assertEquals(1, result.getDataServiceId());
        assertEquals(ServiceConfigType.CODE, result.getType());
        assertEquals(params.getType(), result.getParams().getType());
    }

    @Test
    void updateByDataServiceId() {
        // Arrange
        DataServiceConfig dataServiceConfig = new DataServiceConfig();
        dataServiceConfig.setDataServiceId(1);
        dataServiceConfig.setType(ServiceConfigType.CODE);
        ServiceQueryParams params = new ServiceQueryParams();
        params.setType(ServiceConfigType.QUERY);
        dataServiceConfig.setParams(params);
        dataServiceConfigMapper.insert(dataServiceConfig);
        // Act
        dataServiceConfig.setType(ServiceConfigType.QUERY);
        ServiceQueryParams queryParams = new ServiceQueryParams();
        queryParams.setType(ServiceConfigType.QUERY);
        dataServiceConfigMapper.updateByDataServiceId(dataServiceConfig);
        // Assert
        DataServiceConfig result = dataServiceConfigMapper.selectOneByDataServiceId(dataServiceConfig.getDataServiceId());
        assertNotNull(result);
        assertEquals(1, result.getDataServiceId());
        assertEquals(ServiceConfigType.QUERY, result.getType());
        assertEquals(queryParams.getType(), result.getParams().getType());
    }

    @Test
    void deleteByDataServiceId() {
        // Arrange
        DataServiceConfig dataServiceConfig = new DataServiceConfig();
        dataServiceConfig.setDataServiceId(1);
        dataServiceConfig.setType(ServiceConfigType.CODE);
        CodeModeParams params = new CodeModeParams();
        params.setType(ServiceConfigType.CODE);
        params.setCodeBlock("CODE_BLOCK");
        dataServiceConfig.setParams(params);
        dataServiceConfigMapper.insert(dataServiceConfig);
        // Act
        dataServiceConfigMapper.deleteByDataServiceId(dataServiceConfig.getDataServiceId());
        // Assert
        DataServiceConfig result = dataServiceConfigMapper.selectOneByDataServiceId(dataServiceConfig.getDataServiceId());
        assertNull(result);
    }
}