package com.trs.ai.moye.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * <AUTHOR>
 * @since 2024/11/8 16:05
 */
@Slf4j
public class BcryptTest {

    @Test
    void test() {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String pwd = encoder.encode("0192023a7bbd73250516f069df18b500");
        log.info(pwd);
    }
}
