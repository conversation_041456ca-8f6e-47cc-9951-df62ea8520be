package com.trs.ai.moye.data.model.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataModelFieldAttributes;
import com.trs.moye.base.data.standard.entity.TagEdgeField;
import java.util.List;
import javax.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class DataModelFieldMapperTest {

    @Resource
    private DataModelFieldMapper dataModelFieldMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        jdbcTemplate.execute("TRUNCATE TABLE data_model_field");
    }

    @Test
    void insert() {
        DataModelField dataModelField = new DataModelField();
        DataModelFieldAttributes<TagEdgeField> subfield = new DataModelFieldAttributes<>("vid",
            List.of(new TagEdgeField("vid", "vid", "string", false, null, null)));
        dataModelField.setFields(subfield);
        dataModelFieldMapper.insert(dataModelField);

        List<String> query = jdbcTemplate.query("select * from data_model_field",
            (rs, rowNum) -> rs.getString("fields"));

        assertEquals(1, query.size());
        String expected = "{\"primaryKey\":\"vid\",\"fields\":[{\"zhName\":\"vid\",\"enName\":\"vid\",\"type\":\"string\",\"notNull\":false}]}";
        assertEquals(expected, query.get(0));
    }

    @Test
    void listByDataModelId() {
        DataModelField expected = new DataModelField();
        expected.setDataModelId(1);
        DataModelFieldAttributes<TagEdgeField> subfield = new DataModelFieldAttributes<>("vid",
            List.of(new TagEdgeField("vid", "vid", "string", false, null, null)));
        expected.setFields(subfield);
        dataModelFieldMapper.insert(expected);

        List<DataModelField> result = dataModelFieldMapper.listByDataModelId(1);
        assertEquals(1, result.size());

        DataModelField actual = result.get(0);

        // 比较主键值
        assertEquals(expected.getFields().getPrimaryKey(), actual.getFields().getPrimaryKey());

        // 验证字段数量
        assertEquals(expected.getFields().getFields().size(), actual.getFields().getFields().size());

        // 转换为JSON字符串比较内容
        List<TagEdgeField> expectedFields = expected.getFields().getTagEdgeFields();
        List<TagEdgeField> actualFields = actual.getFields().getTagEdgeFields();

        assertEquals(expectedFields, actualFields);
    }
}