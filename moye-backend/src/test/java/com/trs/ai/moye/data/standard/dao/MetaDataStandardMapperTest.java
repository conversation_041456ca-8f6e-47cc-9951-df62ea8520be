package com.trs.ai.moye.data.standard.dao;

import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.ai.moye.data.standard.entity.GraphicsMetaDataStandard;
import com.trs.moye.base.data.standard.entity.MetaDataStandard;
import com.trs.moye.base.data.standard.enums.MetaDataStandardTypeEnum;
import java.util.List;
import javax.annotation.Resource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class MetaDataStandardMapperTest {

    @Autowired
    private MetaDataStandardMapper metaDataStandardMapper;
    @Resource
    private DataModelMapper dataModelMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;


    @BeforeEach
    void setUp() {
        jdbcTemplate.execute("TRUNCATE TABLE meta_data_standard");
        jdbcTemplate.execute("TRUNCATE TABLE data_model");
    }

    @Test
    void selectByCatalogId() {
        GraphicsMetaDataStandard graphicsMetaDataStandard = new GraphicsMetaDataStandard();
        graphicsMetaDataStandard.setMetaDataStandardCatalogId(1);
        graphicsMetaDataStandard.setType(MetaDataStandardTypeEnum.GRAPHICS);
        graphicsMetaDataStandard.setEnName("test");
        graphicsMetaDataStandard.setZhName("测试");
        graphicsMetaDataStandard.setVidType("string");

        metaDataStandardMapper.insert(graphicsMetaDataStandard);
        List<GraphicsMetaDataStandard> graphicsMetaDataStandards = metaDataStandardMapper.selectByCatalogId(1);
        Assertions.assertEquals(1, graphicsMetaDataStandards.size());
        assert graphicsMetaDataStandards.get(0).getEnName().equals("test");
        assert graphicsMetaDataStandards.get(0).getZhName().equals("测试");
        assert graphicsMetaDataStandards.get(0).getVidType().equals("string");
    }

    @Test
    void updateOne() {
        GraphicsMetaDataStandard graphicsMetaDataStandard = new GraphicsMetaDataStandard();
        graphicsMetaDataStandard.setMetaDataStandardCatalogId(1);
        graphicsMetaDataStandard.setType(MetaDataStandardTypeEnum.GRAPHICS);
        graphicsMetaDataStandard.setEnName("test");
        graphicsMetaDataStandard.setZhName("测试");
        graphicsMetaDataStandard.setVidType("string");

        metaDataStandardMapper.insert(graphicsMetaDataStandard);
        graphicsMetaDataStandard.setZhName("测试2");
        metaDataStandardMapper.updateOne(graphicsMetaDataStandard);
        GraphicsMetaDataStandard graphicsMetaDataStandard1 = metaDataStandardMapper.selectByEnName("test");
        Assertions.assertEquals("测试2", graphicsMetaDataStandard1.getZhName());
    }

    @Test
    void countByEnNameAndExcludeId() {
        GraphicsMetaDataStandard graphicsMetaDataStandard = new GraphicsMetaDataStandard();
        graphicsMetaDataStandard.setMetaDataStandardCatalogId(1);
        graphicsMetaDataStandard.setType(MetaDataStandardTypeEnum.GRAPHICS);
        graphicsMetaDataStandard.setEnName("test");
        graphicsMetaDataStandard.setZhName("测试");
        graphicsMetaDataStandard.setVidType("string");

        metaDataStandardMapper.insert(graphicsMetaDataStandard);
        Integer countExcludeId = metaDataStandardMapper.countByEnNameAndExcludeId("test",
            graphicsMetaDataStandard.getId());
        Assertions.assertEquals(0, countExcludeId);
        Integer count = metaDataStandardMapper.countByEnNameAndExcludeId("test", null);
        Assertions.assertEquals(1, count);
    }

    @Test
    void selectByStandardType() {
        GraphicsMetaDataStandard graphicsMetaDataStandard = new GraphicsMetaDataStandard();
        graphicsMetaDataStandard.setMetaDataStandardCatalogId(1);
        graphicsMetaDataStandard.setType(MetaDataStandardTypeEnum.GRAPHICS);
        graphicsMetaDataStandard.setEnName("test");
        graphicsMetaDataStandard.setZhName("测试");
        graphicsMetaDataStandard.setVidType("string");

        metaDataStandardMapper.insert(graphicsMetaDataStandard);
        List<MetaDataStandard> metaDataStandards = metaDataStandardMapper.selectByStandardType(
            MetaDataStandardTypeEnum.GRAPHICS.name());
        Assertions.assertEquals(1, metaDataStandards.size());
        Assertions.assertEquals("test", metaDataStandards.get(0).getEnName());

    }

    @Test
    void selectByDataModelId() {
        // 测试 有元数据标准 的 数据建模
        GraphicsMetaDataStandard expected = new GraphicsMetaDataStandard("string");
        expected.setMetaDataStandardCatalogId(1);
        expected.setZhName("测试");
        expected.setEnName("test");
        expected.setType(MetaDataStandardTypeEnum.GRAPHICS);
        metaDataStandardMapper.insert(expected);

        DataModel dataModel = new DataModel();
        dataModel.setMetaDataStandardId(expected.getId());
        dataModelMapper.insert(dataModel);

        GraphicsMetaDataStandard actual = metaDataStandardMapper.selectByDataModelId(dataModel.getId());
        Assertions.assertEquals(expected.getEnName(), actual.getEnName());
    }

    @Test
    void selectByDataModelIdWithoutStandard() {
        // 测试 无元数据标准 的 数据建模
        DataModel dataModelWithoutStandard = new DataModel();
        dataModelWithoutStandard.setEnName("无元数据标准的数据建模");
        dataModelMapper.insert(dataModelWithoutStandard);

        GraphicsMetaDataStandard nullMetaDataStandard = metaDataStandardMapper.selectByDataModelId(
            dataModelWithoutStandard.getId());
        Assertions.assertNull(nullMetaDataStandard);
    }
}