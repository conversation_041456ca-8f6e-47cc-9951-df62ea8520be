package com.trs.ai.moye.storageengine.service.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.trs.ai.moye.common.http.HttpClient;
import com.trs.ai.moye.storageengine.feign.JobFeign;
import com.trs.ai.moye.storageengine.request.SubmitJobRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

 class StorageEngineServiceImplTest{

    @InjectMocks
    private StorageEngineServiceImpl storageEngineService;

    @Mock
    private HttpClient httpClient;


    @Mock
    private JobFeign jobFeign;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testImmediateExecute() {
        // Arrange
        SubmitJobRequest request = new SubmitJobRequest();
    
        // Act
        storageEngineService.submitJob(request);
    
        // Assert
        verify(jobFeign, times(1)).submitJob(any(SubmitJobRequest.class));
    }

}