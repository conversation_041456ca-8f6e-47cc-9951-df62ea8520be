package com.trs.ai.moye.backstage.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.ai.moye.backstage.entity.StreamExclusiveNodeConfig;
import com.trs.moye.base.common.response.IdNameResponse;
import java.util.List;
import java.util.Set;
import javax.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

/**
 * StreamExclusiveNodeConfigMapperTest
 *
 * <AUTHOR>
 * @since 2025/6/27 16:49
 */
@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class StreamExclusiveNodeConfigMapperTest {

    @Resource
    private StreamExclusiveNodeConfigMapper streamExclusiveNodeConfigMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        jdbcTemplate.execute("TRUNCATE TABLE stream_exclusive_node_config");
    }

    @Test
    void updateById_nullRateLimit() {
        StreamExclusiveNodeConfig config1 = new StreamExclusiveNodeConfig();
        config1.setDataModels(Set.of(new IdNameResponse(1, "dataModel1"), new IdNameResponse(2, "dataModel2")));
        config1.setExpectNodeCount(1);
        config1.setPriority(1);
        config1.setConcurrentThreads(2);
        config1.setRateLimit(5000);
        streamExclusiveNodeConfigMapper.insert(List.of(config1));

        config1.setRateLimit(null);
        streamExclusiveNodeConfigMapper.updateById(List.of(config1));

        List<StreamExclusiveNodeConfig> streamExclusiveNodeConfigs = streamExclusiveNodeConfigMapper.selectAll();
        assertEquals(1, streamExclusiveNodeConfigs.size());
        assertNull(streamExclusiveNodeConfigs.get(0).getRateLimit());
    }
}