package com.trs.ai.moye.data.model.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.trs.ai.moye.data.model.response.BatchDeleteResponse;
import com.trs.ai.moye.data.model.response.ModelBasicInfoResponse;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class DataModelServiceTest {

    @Mock
    private DataModelService dataModelService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    @Test
    void testModelBasicInfo() {
        // Arrange
        Integer modelId = 1;
        ModelBasicInfoResponse expectedResponse = new ModelBasicInfoResponse();
        expectedResponse.setId(modelId);
        expectedResponse.setZhName("TestModel");

        when(dataModelService.getModelBasicInfo(modelId)).thenReturn(expectedResponse);

        ModelBasicInfoResponse actualResponse = dataModelService.getModelBasicInfo(modelId);
        assertEquals(expectedResponse, actualResponse);
    }

    @Test
    void testMoveMetaDataToCategory() {
        // Arrange
        Set<Integer> ids = new HashSet<>();
        ids.add(1);
        ids.add(2);
        Integer businessId = 100;
        boolean expectedResult = true;

        when(dataModelService.moveMetaDataToCategory(ids, businessId)).thenReturn(expectedResult);

        boolean actualResult = dataModelService.moveMetaDataToCategory(ids, businessId);
        assertTrue(actualResult);
    }

    @Test
    void testDeleteDataModel() {
        // Arrange
        List<Integer> ids = Arrays.asList(1, 2, 3);
        BatchDeleteResponse response1 = new BatchDeleteResponse();
        BatchDeleteResponse response2 = new BatchDeleteResponse();
        List<BatchDeleteResponse> expectedResponses = Arrays.asList(response1, response2);

        when(dataModelService.deleteDataModel(ids)).thenReturn(expectedResponses);

        List<BatchDeleteResponse> actualResponses = dataModelService.deleteDataModel(ids);
        assertEquals(expectedResponses, actualResponses);
    }

    @Test
    void testDeleteModel() {
        // Arrange
        Integer modelId = 1;
        when(dataModelService.deleteModel(modelId)).thenReturn(true);

        boolean result = dataModelService.deleteModel(modelId);
        assertTrue(result);
    }

    @Test
    void testDeleteOds() {
        // Arrange
        Integer odsId = 1;
        doNothing().when(dataModelService).deleteOds(odsId);

        dataModelService.deleteOds(odsId);
        verify(dataModelService).deleteOds(odsId);
    }

}