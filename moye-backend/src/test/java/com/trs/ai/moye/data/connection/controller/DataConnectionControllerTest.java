package com.trs.ai.moye.data.connection.controller;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.trs.ai.moye.common.web.response.CustomerResponseBodyConvert;
import com.trs.ai.moye.common.web.response.GlobalExceptionProcessor;
import com.trs.ai.moye.data.connection.request.AutoTestRequest;
import com.trs.ai.moye.data.connection.service.DataConnectionMonitorConfigService;
import com.trs.moye.base.common.utils.JsonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

/**
 * DataConnectionControllerTest
 *
 * <AUTHOR>
 * @since 2025/7/9 10:29
 */
@ExtendWith(MockitoExtension.class)
class DataConnectionControllerTest {

    @InjectMocks
    private DataConnectionController dataConnectionController;

    @Mock
    private DataConnectionMonitorConfigService dataConnectionMonitorConfigService;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(dataConnectionController)
            .setControllerAdvice(new CustomerResponseBodyConvert(), new GlobalExceptionProcessor())
            .setMessageConverters(new MappingJackson2HttpMessageConverter(JsonUtils.OBJECT_MAPPER))
            .build();
    }

    /**
     * {@link DataConnectionController#switchAutoTestEnabled(AutoTestRequest)}
     */
    @Test
    void switchAutoTestEnabled() throws Exception {

        String url = "/data-connection/auto-test";

        // case1: success
        AutoTestRequest request = new AutoTestRequest();
        request.setConnectionId(1);
        request.setAutoTestEnabled(Boolean.TRUE);

        mockMvc.perform(
                post(url).contentType(MediaType.APPLICATION_JSON)
                    .content(JsonUtils.toJsonString(request)))
            .andExpect(status().isOk());

        // case2: autoTestEnabled is null
        request.setAutoTestEnabled(null);

        mockMvc.perform(
                post(url).contentType(MediaType.APPLICATION_JSON)
                   .content(JsonUtils.toJsonString(request)))
           .andExpect(status().isInternalServerError());

        // case3: connectionId is null
        request.setConnectionId(null);

        mockMvc.perform(
                post(url).contentType(MediaType.APPLICATION_JSON)
                   .content(JsonUtils.toJsonString(request)))
           .andExpect(status().isInternalServerError());
    }
}