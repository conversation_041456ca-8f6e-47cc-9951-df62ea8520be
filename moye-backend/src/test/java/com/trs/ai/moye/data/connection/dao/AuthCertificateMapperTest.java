package com.trs.ai.moye.data.connection.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.ai.moye.nacos.config.starter.NacosIncrementConfigMerger;
import com.trs.moye.base.data.connection.dao.AuthCertificateMapper;
import com.trs.moye.base.data.connection.entity.KerberosCertificate;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

@MybatisPlusTest(excludeAutoConfiguration = NacosIncrementConfigMerger.class)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class AuthCertificateMapperTest {

    @Autowired
    private AuthCertificateMapper authCertificateMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        jdbcTemplate.execute("TRUNCATE TABLE auth_certificate_kerberos");
    }

    @Test
    void selectById() {
        // Arrange
        KerberosCertificate certificate = new KerberosCertificate();
        certificate.setKrb5Path("/etc/krb5.conf");
        certificate.setPrincipal("<EMAIL>");
        certificate.setKeytabPath("/etc/security/keytabs/user.keytab");
        authCertificateMapper.insert(certificate);

        // Act
        KerberosCertificate result = authCertificateMapper.selectById(certificate.getId());

        // Assert
        assertNotNull(result);
        assertEquals("/etc/krb5.conf", result.getKrb5Path());
        assertEquals("<EMAIL>", result.getPrincipal());
        assertEquals("/etc/security/keytabs/user.keytab", result.getKeytabPath());
    }

    @Test
    void selectAll() {
        // Arrange
        KerberosCertificate certificate1 = new KerberosCertificate();
        certificate1.setKrb5Path("/etc/krb5_1.conf");
        certificate1.setPrincipal("<EMAIL>");
        certificate1.setKeytabPath("/etc/security/keytabs/user1.keytab");

        KerberosCertificate certificate2 = new KerberosCertificate();
        certificate2.setKrb5Path("/etc/krb5_2.conf");
        certificate2.setPrincipal("<EMAIL>");
        certificate2.setKeytabPath("/etc/security/keytabs/user2.keytab");

        authCertificateMapper.insert(certificate1);
        authCertificateMapper.insert(certificate2);

        // Act
        List<KerberosCertificate> results = authCertificateMapper.selectAll();

        // Assert
        assertNotNull(results);
        assertEquals(2, results.size());
        assertTrue(results.stream().anyMatch(cert -> "<EMAIL>".equals(cert.getPrincipal())));
        assertTrue(results.stream().anyMatch(cert -> "<EMAIL>".equals(cert.getPrincipal())));
    }

    @Test
    void selectByUserId() {
        // Arrange
        KerberosCertificate certificate1 = new KerberosCertificate();
        certificate1.setKrb5Path("/etc/krb5_1.conf");
        certificate1.setPrincipal("<EMAIL>");
        certificate1.setKeytabPath("/etc/security/keytabs/user1.keytab");
        // Assuming there's a method to set user ID, if not, you might need to modify your entity
        certificate1.setCreateBy(1);

        KerberosCertificate certificate2 = new KerberosCertificate();
        certificate2.setKrb5Path("/etc/krb5_2.conf");
        certificate2.setPrincipal("<EMAIL>");
        certificate2.setKeytabPath("/etc/security/keytabs/user2.keytab");
        certificate2.setCreateBy(2);

        authCertificateMapper.insert(certificate1);
        authCertificateMapper.insert(certificate2);

        // Act
        // Assuming selectByUserId method exists, if not, you might need to implement it
        List<KerberosCertificate> results = authCertificateMapper.selectByUserId(1);

        // Assert
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals("<EMAIL>", results.get(0).getPrincipal());
    }
}