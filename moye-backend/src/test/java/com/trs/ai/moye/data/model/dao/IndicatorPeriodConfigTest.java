package com.trs.ai.moye.data.model.dao;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.fasterxml.jackson.core.type.TypeReference;
import com.trs.moye.base.common.exception.JsonParseException;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.indicator.entity.DailyStatRange;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfig;
import com.trs.moye.base.data.indicator.entity.IntervalStatRange;
import com.trs.moye.base.data.indicator.entity.MonthlyStatRange;
import com.trs.moye.base.data.indicator.entity.TriggerConfig;
import com.trs.moye.base.data.indicator.entity.WeeklyStatRange;
import com.trs.moye.base.data.indicator.enums.IndicatorConstants;
import java.time.DayOfWeek;
import java.time.LocalTime;
import org.junit.jupiter.api.Test;

class IndicatorPeriodConfigTest {

    @Test
    void dailyConfig_shouldSerializeAndDeserializeCorrectly() {
        String json = """
            {
              "statRange": {
                "startTime": "00:00:00",
                "endTime": "23:59:59"
              },
              "trigger": {
                "time": "00:00:00"
              }
            }
            """;

        // 反序列化验证
        IndicatorPeriodConfig<DailyStatRange> config = JsonUtils.parseObject(json, new TypeReference<>() {
        });

        assertThat(config.getStatRange())
            .usingRecursiveComparison()
            .isEqualTo(new DailyStatRange(LocalTime.MIN, LocalTime.MAX));

        assertThat(config.getTrigger())
            .usingRecursiveComparison()
            .isEqualTo(new TriggerConfig(null, null, null, LocalTime.MIN));

        // 序列化验证
        String serialized = JsonUtils.toJsonString(config);
        assertThat(serialized)
            .contains("\"startTime\":\"00:00:00\"")
            .contains("\"endTime\":\"23:59:59\"")
            .contains("\"time\":\"00:00:00\"");
    }

    @Test
    void weeklyConfig_shouldHandleDayOfWeekCorrectly() {
        String json = """
            {
              "statRange": {
                "startDayOfWeek": "1",
                "startTime": "00:00:00",
                "endDayOfWeek": "7",
                "endTime": "23:59:59"
              },
              "trigger": {
                "dayOfWeek": "1",
                "time": "00:00:00"
              }
            }
            """;

        IndicatorPeriodConfig<WeeklyStatRange> config = JsonUtils.parseObject(json, new TypeReference<>() {
        });

        assertThat(config.getStatRange().getStartDayOfWeek()).isEqualTo(DayOfWeek.MONDAY);
        assertThat(config.getStatRange().getEndDayOfWeek()).isEqualTo(DayOfWeek.SUNDAY);
        assertThat(config.getTrigger().getDayOfWeek()).isEqualTo(DayOfWeek.MONDAY);
    }

    @Test
    void monthlyConfig_shouldHandleLastDayCorrectly() {
        String json = """
            {
              "statRange": {
                "startDay": 1,
                "startTime": "00:00:00",
                "endDay": "last_day",
                "endTime": "23:59:59"
              },
              "trigger": {
                "day": "1",
                "time": "00:00:00"
              }
            }
            """;

        IndicatorPeriodConfig<MonthlyStatRange> config = JsonUtils.parseObject(json, new TypeReference<>() {
        });

        assertThat(config.getStatRange().getEndDay()).isEqualTo(IndicatorConstants.LAST_DAY);
        assertThat(config.getTrigger().getDay()).isEqualTo("1");
    }

    @Test
    void intervalConfig_shouldHandleDurationMonths() {
        String json = """
            {
              "statRange": {
                "startMonth": 1,
                "startDay": 1,
                "startTime": "00:00:00",
                "durationMonths": 6,
                "endDay": "last_day",
                "endTime": "23:59:59"
              },
              "trigger": {
                "offsetMonths": 1,
                "day": "1",
                "time": "00:00:00"
              }
            }
            """;

        IndicatorPeriodConfig<IntervalStatRange> config = JsonUtils.parseObject(json, new TypeReference<>() {
        });

        assertThat(config.getStatRange().getDurationMonths()).isEqualTo(6);
        assertThat(config.getTrigger().getOffsetMonths()).isEqualTo(1);
    }
    // endregion

    // region 异常测试
    @Test
    void invalidTimeFormat_shouldThrowException() {
        String invalidJson = """
            {
              "statRange": {
                "startTime": "25:00:00",
                "endTime": "23:59:59"
              }
            }
            """;

        assertThrows(JsonParseException.class,
            () -> JsonUtils.parseObject(invalidJson, new TypeReference<IndicatorPeriodConfig<DailyStatRange>>() {
            }));
    }

    @Test
    void fullQuarterlyConfig_shouldWork() {
        String json = """
            {
              "statRange": {
                "startMonth": 1,
                "startDay": 1,
                "startTime": "00:00:00",
                "durationMonths": 3,
                "endDay": "last_day",
                "endTime": "23:59:59"
              },
              "trigger": {
                "offsetMonths": 1,
                "day": "5",
                "time": "09:00:00"
              }
            }
            """;

        IndicatorPeriodConfig<IntervalStatRange> config = JsonUtils.parseObject(json, new TypeReference<>() {
        });

        assertThat(config.getStatRange())
            .usingRecursiveComparison()
            .isEqualTo(new IntervalStatRange(
                1, 1, LocalTime.MIN,
                3, IndicatorConstants.LAST_DAY, LocalTime.MAX));

        assertThat(config.getTrigger())
            .usingRecursiveComparison()
            .isEqualTo(new TriggerConfig(1, "5", null, LocalTime.parse("09:00:00")));
    }

    @Test
    void semiannuallyConfig_shouldHandle6Months() {
        String json = """
            {
              "statRange": {
                "startMonth": 1,
                "startDay": 1,
                "startTime": "00:00:00",
                "durationMonths": 6,
                "endDay": "last_day",
                "endTime": "23:59:59"
              },
              "trigger": {
                "offsetMonths": 1,
                "day": "1",
                "time": "00:00:00"
              }
            }
            """;

        IndicatorPeriodConfig<IntervalStatRange> config = JsonUtils.parseObject(json, new TypeReference<>() {
        });

        assertThat(config.getStatRange().getDurationMonths()).isEqualTo(6);
        assertThat(config.getTrigger().getOffsetMonths()).isEqualTo(1);
    }
}