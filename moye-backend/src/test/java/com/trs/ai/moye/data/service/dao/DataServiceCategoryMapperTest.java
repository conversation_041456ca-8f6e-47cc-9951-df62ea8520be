package com.trs.ai.moye.data.service.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.ai.moye.data.service.entity.DataServiceCategory;
import com.trs.ai.moye.data.service.entity.DataServiceCategoryTree;
import java.util.List;
import javax.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

/**
 * 数据服务分类映射测试
 *
 * <AUTHOR>
 * @since 2024/10/15 14:51:27
 */
@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class DataServiceCategoryMapperTest {
    @Resource
    private DataServiceCategoryMapper dataServiceCategoryMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        jdbcTemplate.execute("TRUNCATE TABLE data_service_category");
    }
    @Test
    void selectCategoryTree() {
        DataServiceCategory dataServiceCategory = new DataServiceCategory();
        dataServiceCategory.setName("test");
        dataServiceCategory.setDescription("test");
        dataServiceCategory.setPid(0);
        dataServiceCategoryMapper.insert(dataServiceCategory);
        DataServiceCategory dataServiceCategory1 = new DataServiceCategory();
        dataServiceCategory1.setName("test1");
        dataServiceCategory1.setDescription("test1");
        dataServiceCategory1.setPid(dataServiceCategory.getId());
        dataServiceCategoryMapper.insert(dataServiceCategory1);
        // Act
        List<DataServiceCategoryTree> results = dataServiceCategoryMapper.selectCategoryTree();

        // Assert
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals("test", results.get(0).getName());
        assertEquals("test1", results.get(0).getChildren().get(0).getName());
    }

    @Test
    void checkCategoryName() {
        // Arrange
        DataServiceCategory dataServiceCategory = new DataServiceCategory();
        dataServiceCategory.setName("Existing Category");
        dataServiceCategory.setDescription("Existing Category");
        dataServiceCategory.setPid(0);
        dataServiceCategoryMapper.insert(dataServiceCategory);

        // Act
        DataServiceCategory dataServiceCategory1 = new DataServiceCategory();
        dataServiceCategory1.setName("Existing Category");
        dataServiceCategory1.setPid(0);
        boolean exists = dataServiceCategoryMapper.checkCategoryName(dataServiceCategory1);
        dataServiceCategory1.setName("Non-existing Category");
        boolean notExists = dataServiceCategoryMapper.checkCategoryName(dataServiceCategory1);

        // Assert
        assertTrue(exists);
        assertFalse(notExists);
    }

    @Test
    void selectCategoryTreeByPid() {
        // Arrange
        DataServiceCategory dataServiceCategory = new DataServiceCategory();
        dataServiceCategory.setName("Parent Category");
        dataServiceCategory.setDescription("Parent Category");
        dataServiceCategory.setPid(0);
        dataServiceCategoryMapper.insert(dataServiceCategory);
        DataServiceCategory dataServiceCategory1 = new DataServiceCategory();
        dataServiceCategory1.setName("Child Category");
        dataServiceCategory1.setDescription("Child Category");
        dataServiceCategory1.setPid(dataServiceCategory.getId());
        dataServiceCategoryMapper.insert(dataServiceCategory1);

        // Act
        List<DataServiceCategoryTree> results = dataServiceCategoryMapper.selectCategoryTreeByPid(dataServiceCategory.getId());

        // Assert
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals("Child Category", results.get(0).getName());
    }
}