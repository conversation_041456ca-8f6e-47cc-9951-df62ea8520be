package com.trs.ai.moye.abilitycenter;

import static org.junit.jupiter.api.Assertions.assertInstanceOf;

import com.trs.ai.moye.data.ability.service.SchemaConvert;
import com.trs.moye.ability.entity.Schema;
import com.trs.moye.ability.entity.Schema.ObjectTypeSchema;
import org.junit.jupiter.api.Test;

/**
 * 能力中心算子参数转换
 *
 * <AUTHOR>
 * @since 2025/7/9 11:47
 */
public class SchemaConvertTest {

    @Test
    void responseTest() {
        String responseSchema = "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"city\":{\"type\":\"string\",\"parameterCnName\":\"城市名\"},\"adcode\":{\"type\":\"string\",\"parameterCnName\":\"地区代码\"},\"province\":{\"type\":\"string\",\"parameterCnName\":\"省份\"},\"reporttime\":{\"type\":\"string\",\"parameterCnName\":\"预报时间\"},\"casts\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"date\":{\"type\":\"string\",\"parameterCnName\":\"日期\"},\"week\":{\"type\":\"string\",\"parameterCnName\":\"星期\"},\"dayweather\":{\"type\":\"string\",\"parameterCnName\":\"白天天气\"},\"nightweather\":{\"type\":\"string\",\"parameterCnName\":\"晚上天气\"},\"daytemp\":{\"type\":\"string\",\"parameterCnName\":\"白天气温\"},\"nighttemp\":{\"type\":\"string\",\"parameterCnName\":\"夜晚气温\"},\"daywind\":{\"type\":\"string\",\"parameterCnName\":\"白天风向\"},\"nightwind\":{\"type\":\"string\",\"parameterCnName\":\"夜晚风向\"},\"daypower\":{\"type\":\"string\",\"parameterCnName\":\"白天风力\"},\"nightpower\":{\"type\":\"string\",\"parameterCnName\":\"夜晚风力\"},\"daytemp_float\":{\"type\":\"string\",\"parameterCnName\":\"白天气温小数\"},\"nighttemp_float\":{\"type\":\"string\",\"parameterCnName\":\"夜晚气温小数\"}}},\"parameterCnName\":\"预报\"}}}";
        String requestSchema = "[{\"parameterLocation\":\"Form\",\"parameterName\":\"city\",\"dataType\":\"String\",\"description\":\"城市名，或城市行政编码都可以；比如：成都、510100\",\"parameterCnName\":\"城市行政编码\",\"required\":1,\"default\":\"510100\"},{\"parameterLocation\":\"Json\",\"reqBody\":\"{\\\"root\\\":\\\"root\\\",\\\"properties\\\":{},\\\"dataType\\\":7}\",\"parameterName\":\"json\",\"dataType\":\"\",\"parameterCnName\":\"json\"},{\"parameterLocation\":\"\",\"reqBody\":\"\",\"parameterName\":\"plainText\",\"dataType\":\"\",\"parameterCnName\":\"\"}]";
        SchemaConvert schemaConvert = new SchemaConvert(requestSchema, responseSchema);
        Schema response = schemaConvert.getResponse();
        Schema request = schemaConvert.getBody();
        assertInstanceOf(ObjectTypeSchema.class, response);
    }

    @Test
    void responseArrayTest() {
        String responseSchema = "{\"root\":\"root\",\"type\":\"array\",\"properties\":{},\"required\":[],\"items\":{\"type\":\"string\"},\"parameterCnName\":\"字符串数组\"}";
        String requestSchema = "[{\"parameterLocation\":\"Json\",\"reqBody\":\"{\\\"name\\\":\\\"root\\\",\\\"root\\\":\\\"root\\\",\\\"dataType\\\":7,\\\"properties\\\":{\\\"chunk_size\\\":{\\\"dataType\\\":2,\\\"default\\\":200,\\\"description\\\":\\\"分段大小\\\",\\\"required\\\":2},\\\"text\\\":{\\\"dataType\\\":1,\\\"default\\\":\\\"\\\",\\\"description\\\":\\\"文本\\\",\\\"required\\\":2},\\\"seperators\\\":{\\\"dataType\\\":8,\\\"items\\\":{\\\"dataType\\\":1,\\\"default\\\":\\\"\\\",\\\"description\\\":\\\"分隔符\\\"},\\\"description\\\":\\\"分隔符\\\",\\\"required\\\":2}},\\\"description\\\":\\\"root\\\"}\",\"parameterName\":\"json\",\"dataType\":\"\",\"parameterCnName\":\"json\"},{\"parameterLocation\":\"\",\"reqBody\":\"\",\"parameterName\":\"plainText\",\"dataType\":\"\",\"parameterCnName\":\"\"}]";
        SchemaConvert schemaConvert = new SchemaConvert(requestSchema, responseSchema);
        Schema response = schemaConvert.getResponse();
        Schema request = schemaConvert.getBody();
        assertInstanceOf(Schema.ArrayTypeSchema.class, response);
    }

    @Test
    void responseTest2() {
        String responseSchema = "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"code\":{\"type\":\"integer\"},\"data\":{\"type\":\"object\",\"properties\":{\"file_status\":{\"type\":\"string\",\"description\":\"0 => '确认中',         1 => '非实时中',         2 => '非实时完成',         3 => '文件错误',         4 => '实时中',         5 => '实时完成',         6 => '文件上传中',         7 => '连接断开',         8 => '会议暂停中',         9 => '转写完成',         10 => '角色分离进行中',         11 => '角色分离完成'\"},\"merge_list\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"id\":{\"type\":\"integer\"},\"fid\":{\"type\":\"integer\"},\"type\":{\"type\":\"integer\"},\"is_active\":{\"type\":\"integer\"},\"is_display\":{\"type\":\"integer\"},\"txt\":{\"type\":\"string\"},\"start_time\":{\"type\":\"integer\"},\"end_time\":{\"type\":\"number\"},\"pcmtime\":{\"type\":\"string\"},\"timestamp\":{\"type\":\"string\"},\"pcm\":{\"type\":\"string\"},\"wav\":{\"type\":\"string\"},\"speaker\":{\"type\":\"integer\"},\"silence\":{\"type\":\"integer\"},\"extend_field\":{\"type\":\"string\"},\"create_time\":{\"type\":\"string\"},\"update_time\":{\"type\":\"string\"},\"speaker_vpr_message\":{\"type\":\"string\"},\"speaker_message\":{\"type\":\"string\"},\"mark\":{\"type\":\"string\"},\"time_mark\":{\"type\":\"string\"},\"rich_text\":{\"type\":\"string\"},\"edit_record\":{\"type\":\"array\"}}}}}},\"error\":{\"type\":\"string\"}}}";
        String requestSchema = "[{\"parameterLocation\":\"Json\",\"reqBody\":\"{\\\"name\\\":\\\"root\\\",\\\"root\\\":\\\"root\\\",\\\"dataType\\\":7,\\\"properties\\\":{\\\"chunk_size\\\":{\\\"dataType\\\":2,\\\"default\\\":200,\\\"description\\\":\\\"分段大小\\\",\\\"required\\\":2},\\\"text\\\":{\\\"dataType\\\":1,\\\"default\\\":\\\"\\\",\\\"description\\\":\\\"文本\\\",\\\"required\\\":2},\\\"seperators\\\":{\\\"dataType\\\":8,\\\"items\\\":{\\\"dataType\\\":1,\\\"default\\\":\\\"\\\",\\\"description\\\":\\\"分隔符\\\"},\\\"description\\\":\\\"分隔符\\\",\\\"required\\\":2}},\\\"description\\\":\\\"root\\\"}\",\"parameterName\":\"json\",\"dataType\":\"\",\"parameterCnName\":\"json\"},{\"parameterLocation\":\"\",\"reqBody\":\"\",\"parameterName\":\"plainText\",\"dataType\":\"\",\"parameterCnName\":\"\"}]";
        SchemaConvert schemaConvert = new SchemaConvert(requestSchema, responseSchema);
        Schema response = schemaConvert.getResponse();
        Schema request = schemaConvert.getBody();
        assertInstanceOf(Schema.ObjectTypeSchema.class, response);
    }
}
