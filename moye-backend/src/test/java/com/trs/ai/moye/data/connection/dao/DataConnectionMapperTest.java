package com.trs.ai.moye.data.connection.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.ai.moye.data.connection.request.ConnectionListRequest;
import com.trs.ai.moye.data.model.dao.DataConnectionDisplayMapper;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.dao.DataConnectionMonitorConfigMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.DataConnectionMonitorConfig;
import com.trs.moye.base.data.connection.entity.params.MysqlConnectionParams;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import java.util.List;
import javax.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class DataConnectionMapperTest {

    @Resource
    private DataConnectionMapper dataConnectionMapper;

    @Resource
    private DataConnectionMonitorConfigMapper dataConnectionMonitorConfigMapper;

    @Resource
    private DataConnectionDisplayMapper dataConnectionDisplayMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        jdbcTemplate.execute("TRUNCATE TABLE data_connection");
        jdbcTemplate.execute("TRUNCATE TABLE data_connection_monitor_config");
    }

    @Test
    void selectById() {
        // Arrange
        DataConnection connection = new DataConnection();
        connection.setName("Test Connection");
        connection.setConnectionType(ConnectionType.MYSQL);
        connection.setSource(true);
        connection.setTestSuccess(true);
        MysqlConnectionParams connectionParams = new MysqlConnectionParams();
        connectionParams.setConnectionType(ConnectionType.MYSQL);
        connectionParams.setHost("localhost");
        connectionParams.setPort(3306);
        connectionParams.setUsername("test_user");
        connectionParams.setPassword("test_password");
        connectionParams.setDatabase("test_db");
        connection.setConnectionParams(connectionParams);
        dataConnectionMapper.insert(connection);

        // Act
        DataConnection result = dataConnectionMapper.selectById(connection.getId());

        // Assert
        assertNotNull(result);
        assertEquals("Test Connection", result.getName());
        assertEquals(ConnectionType.MYSQL, result.getConnectionType());
        assertEquals(ConnectionType.MYSQL, result.getConnectionParams().getConnectionType());
        assertEquals("localhost", result.getConnectionParams().getHost());
        assertTrue(result.isSource());
        assertTrue(result.isTestSuccess());
    }

    @Test
    void selectConnections() {
        // Arrange
        DataConnection connection1 = new DataConnection();
        connection1.setName("Test Connection 1");
        connection1.setConnectionType(ConnectionType.MYSQL);
        connection1.setSource(true);
        connection1.setTestSuccess(true);
        dataConnectionMapper.insert(connection1);

        DataConnection connection2 = new DataConnection();
        connection2.setName("Test Connection 2");
        connection2.setConnectionType(ConnectionType.POSTGRESQL);
        connection2.setSource(true);
        connection2.setTestSuccess(false);
        dataConnectionMapper.insert(connection2);

        ConnectionListRequest request = new ConnectionListRequest();
        request.setConnectionType(ConnectionType.MYSQL);
        request.setTestSuccess(true);

        // Act
        List<DataConnection> results = dataConnectionDisplayMapper.selectConnections(request, true);

        // Assert
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals("Test Connection 1", results.get(0).getName());
        assertEquals(ConnectionType.MYSQL, results.get(0).getConnectionType());

        // case2
        dataConnectionMonitorConfigMapper.insert(new DataConnectionMonitorConfig(connection1.getId(), true, 100));

        request.setConnectionType(null);
        request.setAutoTestEnabled(true);

        List<DataConnection> autoTestEnabledDataConnections = dataConnectionDisplayMapper.selectConnections(request, true);
        assertNotNull(autoTestEnabledDataConnections);
        assertEquals(1, autoTestEnabledDataConnections.size());
        assertEquals(connection1.getId(), autoTestEnabledDataConnections.get(0).getId());
        assertTrue(autoTestEnabledDataConnections.get(0).getMonitorConfig().isEnabled());
    }

    @Test
    void selectByName() {
        // Arrange
        DataConnection connection = new DataConnection();
        connection.setName("Unique Connection");
        connection.setConnectionType(ConnectionType.MYSQL);
        connection.setSource(true);
        dataConnectionMapper.insert(connection);

        // Act
        DataConnection result = dataConnectionMapper.selectByName(true, "Unique Connection");

        // Assert
        assertNotNull(result);
        assertEquals("Unique Connection", result.getName());
        assertEquals(ConnectionType.MYSQL, result.getConnectionType());
        assertTrue(result.isSource());
    }

    @Test
    void existsByName() {
        // Arrange
        DataConnection connection = new DataConnection();
        connection.setName("Existing Connection");
        connection.setConnectionType(ConnectionType.MYSQL);
        connection.setSource(true);
        dataConnectionMapper.insert(connection);

        // Act
        boolean exists = dataConnectionMapper.existsByName("Existing Connection", true);
        boolean notExists = dataConnectionMapper.existsByName("Non-existing Connection", true);

        // Assert
        assertTrue(exists);
        assertFalse(notExists);
    }

    @Test
    void selectAllConnection() {
        // Arrange
        DataConnection connection1 = new DataConnection();
        connection1.setName("All Connection 1");
        connection1.setConnectionType(ConnectionType.MYSQL);
        connection1.setSource(true);
        dataConnectionMapper.insert(connection1);

        DataConnection connection2 = new DataConnection();
        connection2.setName("All Connection 2");
        connection2.setConnectionType(ConnectionType.POSTGRESQL);
        connection2.setSource(false);
        dataConnectionMapper.insert(connection2);

        // Act
        List<DataConnection> results = dataConnectionMapper.selectAllConnection();

        // Assert
        assertNotNull(results);
        assertTrue(results.size() >= 2);
        assertTrue(results.stream().anyMatch(conn -> "All Connection 1".equals(conn.getName())));
        assertTrue(results.stream().anyMatch(conn -> "All Connection 2".equals(conn.getName())));
    }
}