package com.trs.ai.moye.data.ability.controller;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.mockito.Mockito.*;

import com.trs.ai.moye.common.web.response.CustomerResponseBodyConvert;
import com.trs.ai.moye.common.web.response.GlobalExceptionProcessor;
import com.trs.ai.moye.data.ability.request.AbilityRequest;
import com.trs.ai.moye.data.ability.response.AbilityDetailResponse;
import com.trs.ai.moye.data.ability.service.AbilityService;
import com.trs.moye.ability.enums.AbilityType;
import com.trs.moye.base.common.utils.JsonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(MockitoExtension.class)
class AbilityControllerTest {

    @InjectMocks
    private AbilityController abilityController;

    private MockMvc mockMvc;

    @Mock
    private AbilityService abilityService;

    @BeforeEach
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(abilityController)
            .setControllerAdvice(new CustomerResponseBodyConvert(), new GlobalExceptionProcessor())
            .setMessageConverters(new MappingJackson2HttpMessageConverter(JsonUtils.OBJECT_MAPPER))
            .build();
    }

    @Test
    void addAbility_WithValidRequest_ShouldReturnOk() throws Exception {
        AbilityRequest request = new AbilityRequest();
        request.setEnName("remoteTest");
        request.setZhName("远程测试");
        request.setDescription("远程能力描述");
        request.setType(AbilityType.REMOTE);
        request.setOperatorCategoryId(1);
        request.setIconName("icon");

        mockMvc.perform(post("/ability")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(request)))
            .andExpect(status().isOk());
    }

    @Test
    void addAbility_WithInvalidRequest_ShouldReturnBadRequest() throws Exception {
        AbilityRequest request = new AbilityRequest();
        // 缺少必填字段
        request.setEnName("incompleteRemote");

        mockMvc.perform(post("/ability")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(request)))
            .andExpect(status().isInternalServerError());
    }

    @Test
    void delete_WhenAbilityExists_ShouldReturnOk() throws Exception {
        when(abilityService.deleteAbility(1)).thenReturn(true);
        mockMvc.perform(delete("/ability/1"))
            .andExpect(status().isOk());
    }


    /**
     * 测试当传入有效 ID 时，`getAbilityDetailInfo` 方法应能正确返回能力详情响应。
     * 此测试用例模拟传入一个有效的整数 ID，验证控制器方法是否能从服务层获取预期的能力详情响应，
     * 并确保返回结果不为空且与预期结果一致，同时验证服务层方法被正确调用一次。
     */
    @Test
    void getAbilityDetailInfo_shouldReturnResponseWhenIdIsValid() {
        // Arrange
        // 定义一个有效的 ID
        Integer validId = 1;
        // 创建预期的能力详情响应对象
        AbilityDetailResponse expectedResponse = new AbilityDetailResponse();
        // 模拟服务层方法调用，当传入有效 ID 时返回预期响应
        when(abilityService.getAbilityDetailInfo(validId)).thenReturn(expectedResponse);

        // Act
        // 调用控制器方法获取实际的能力详情响应
        AbilityDetailResponse actualResponse = abilityController.getAbilityDetailInfo(validId);

        // Assert
        // 验证实际响应不为空
        assertNotNull(actualResponse);
        // 验证实际响应与预期响应一致
        assertEquals(expectedResponse, actualResponse);
        // 验证服务层方法被调用了一次
        verify(abilityService, times(1)).getAbilityDetailInfo(validId);
    }

    /**
     * 测试当传入 null ID 时，`getAbilityDetailInfo` 方法应能正确处理并返回能力详情响应。
     * 此测试用例模拟传入 null 作为 ID，验证控制器方法是否能从服务层获取预期的能力详情响应，
     * 并确保返回结果不为空且与预期结果一致，同时验证服务层方法被正确调用一次。
     */
    @Test
    void getAbilityDetailInfo_shouldHandleNullId() {
        // Arrange
        // 定义一个 null ID
        Integer nullId = null;
        // 创建预期的能力详情响应对象
        AbilityDetailResponse expectedResponse = new AbilityDetailResponse();
        // 模拟服务层方法调用，当传入 null ID 时返回预期响应
        when(abilityService.getAbilityDetailInfo(nullId)).thenReturn(expectedResponse);

        // Act
        // 调用控制器方法获取实际的能力详情响应
        AbilityDetailResponse actualResponse = abilityController.getAbilityDetailInfo(nullId);

        // Assert
        // 验证实际响应不为空
        assertNotNull(actualResponse);
        // 验证实际响应与预期响应一致
        assertEquals(expectedResponse, actualResponse);
        // 验证服务层方法被调用了一次
        verify(abilityService, times(1)).getAbilityDetailInfo(nullId);
    }

    /**
     * 测试当传入负 ID 时，`getAbilityDetailInfo` 方法应能正确处理并返回能力详情响应。
     * 此测试用例模拟传入一个负整数作为 ID，验证控制器方法是否能从服务层获取预期的能力详情响应，
     * 并确保返回结果不为空且与预期结果一致，同时验证服务层方法被正确调用一次。
     */
    @Test
    void getAbilityDetailInfo_shouldHandleNegativeId() {
        // Arrange
        // 定义一个负 ID
        Integer negativeId = -1;
        // 创建预期的能力详情响应对象
        AbilityDetailResponse expectedResponse = new AbilityDetailResponse();
        // 模拟服务层方法调用，当传入负 ID 时返回预期响应
        when(abilityService.getAbilityDetailInfo(negativeId)).thenReturn(expectedResponse);

        // Act
        // 调用控制器方法获取实际的能力详情响应
        AbilityDetailResponse actualResponse = abilityController.getAbilityDetailInfo(negativeId);

        // Assert
        // 验证实际响应不为空
        assertNotNull(actualResponse);
        // 验证实际响应与预期响应一致
        assertEquals(expectedResponse, actualResponse);
        // 验证服务层方法被调用了一次
        verify(abilityService, times(1)).getAbilityDetailInfo(negativeId);
    }

    /**
     * 测试当传入 ID 为 0 时，`getAbilityDetailInfo` 方法应能正确处理并返回能力详情响应。
     * 此测试用例模拟传入 0 作为 ID，验证控制器方法是否能从服务层获取预期的能力详情响应，
     * 并确保返回结果不为空且与预期结果一致，同时验证服务层方法被正确调用一次。
     */
    @Test
    void getAbilityDetailInfo_shouldHandleZeroId() {
        // Arrange
        // 定义一个 ID 为 0
        Integer zeroId = 0;
        // 创建预期的能力详情响应对象
        AbilityDetailResponse expectedResponse = new AbilityDetailResponse();
        // 模拟服务层方法调用，当传入 ID 为 0 时返回预期响应
        when(abilityService.getAbilityDetailInfo(zeroId)).thenReturn(expectedResponse);

        // Act
        // 调用控制器方法获取实际的能力详情响应
        AbilityDetailResponse actualResponse = abilityController.getAbilityDetailInfo(zeroId);

        // Assert
        // 验证实际响应不为空
        assertNotNull(actualResponse);
        // 验证实际响应与预期响应一致
        assertEquals(expectedResponse, actualResponse);
        // 验证服务层方法被调用了一次
        verify(abilityService, times(1)).getAbilityDetailInfo(zeroId);
    }
}
