package com.trs.ai.moye.data.connection.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.ai.moye.common.web.response.GlobalExceptionProcessor;
import com.trs.ai.moye.data.connection.request.ConnectionListRequest;
import com.trs.ai.moye.data.connection.request.ConnectionRequest;
import com.trs.ai.moye.data.connection.response.DataConnectionResponse;
import com.trs.ai.moye.data.connection.response.DataConnectionCardResponse;
import com.trs.ai.moye.data.connection.service.DataConnectionService;
import com.trs.ai.moye.data.connection.service.DataSourceConnectionService;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.params.MysqlConnectionParams;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(MockitoExtension.class)
class DataSourceConnectionControllerTest {

    @InjectMocks
    private DataSourceConnectionController dataSourceConnectionController;

    private MockMvc mockMvc;

    @Mock
    private DataSourceConnectionService dataSourceService;

    @Mock
    private DataConnectionService dataConnectionService;

    @Mock
    private DataConnectionMapper dataConnectionMapper;

    @Mock
    private DynamicUserNameService dynamicUserNameService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(dataSourceConnectionController)
            .setControllerAdvice(new GlobalExceptionProcessor())
            .build();
    }

    @Test
    void getConnectionCardList_ValidRequest_ReturnsCardList() throws Exception {
        // Arrange
        DataConnection dataConnection = new DataConnection();
        dataConnection.setId(1);
        dataConnection.setName("testName");
        dataConnection.setConnectionType(ConnectionType.MYSQL);
        dataConnection.setSource(true);

        ConnectionListRequest request = new ConnectionListRequest();
        List<DataConnectionCardResponse> mockResponse = Arrays.asList(
            new DataConnectionCardResponse("testName",
                List.of(new DataConnectionResponse(dataConnection, dynamicUserNameService))),
            new DataConnectionCardResponse("testName2",
                List.of(new DataConnectionResponse(dataConnection, dynamicUserNameService)))
        );

        when(dataSourceService.getCardList(any(ConnectionListRequest.class))).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(post("/data-source/card-list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk());

        verify(dataSourceService, times(1)).getCardList(any(ConnectionListRequest.class));
    }

    @Test
    void getConnectionCardList_EmptyRequest_ReturnsCardList() throws Exception {
        // Arrange
        ConnectionListRequest request = new ConnectionListRequest();
        List<DataConnectionCardResponse> mockResponse = List.of(
            new DataConnectionCardResponse("testName", List.of())
        );

        when(dataSourceService.getCardList(any(ConnectionListRequest.class))).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(post("/data-source/card-list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk());
    }

    @Test
    void checkName_ExistingName_ReturnsTrue() throws Exception {
        // Arrange
        String name = "testName";
        when(dataConnectionMapper.existsByName(name, true)).thenReturn(true);

        // Act & Assert
        mockMvc.perform(get("/data-source/check-name")
                .param("name", name))
            .andExpect(status().isOk())
            .andExpect(content().string("true"));
    }

    @Test
    void checkName_NonExistingName_ReturnsFalse() throws Exception {
        // Arrange
        String name = "nonExistingName";
        when(dataConnectionMapper.existsByName(name, true)).thenReturn(false);

        // Act & Assert
        mockMvc.perform(get("/data-source/check-name")
                .param("name", name))
            .andExpect(status().isOk())
            .andExpect(content().string("false"));
    }

    @Test
    void getUsedCount_ValidId_ReturnsCount() throws Exception {
        // Arrange
        Integer id = 1;
        when(dataSourceService.getConnectionUsedCount(id)).thenReturn(5);

        // Act & Assert
        mockMvc.perform(get("/data-source/{id}/used-count", id))
            .andExpect(status().isOk())
            .andExpect(content().string("5"));
    }

    @Test
    void getUsedCount_InvalidId_ThrowsException() throws Exception {
        // Arrange
        Integer id = 999;
        when(dataSourceService.getConnectionUsedCount(id))
            .thenThrow(new BizException("数据源不存在"));

        // Act & Assert
        mockMvc.perform(get("/data-source/{id}/used-count", id))
            .andExpect(status().isInternalServerError())
            .andExpect(result -> assertThat(result.getResolvedException()).isInstanceOf(BizException.class));
    }

    @Test
    void addDataSource_ValidRequest_ReturnsId() throws Exception {
        // Arrange
        ConnectionRequest request = createValidConnectionRequest();
        when(dataConnectionService.addDataConnection(any(ConnectionRequest.class), eq(true))).thenReturn(1);

        // Act & Assert
        mockMvc.perform(post("/data-source")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(content().string("1"));

        verify(dataConnectionService, times(1)).addDataConnection(any(ConnectionRequest.class), eq(true));
    }

    @Test
    void addDataSource_EmptyRequest_ReturnsBadRequest() throws Exception {
        // Arrange
        ConnectionRequest request = new ConnectionRequest();

        // Act & Assert
        mockMvc.perform(post("/data-source")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isInternalServerError());
    }

    @Test
    void addDataSource_ServiceThrowsException_ReturnsBadRequest() throws Exception {
        // Arrange
        ConnectionRequest request = createValidConnectionRequest();
        when(dataConnectionService.addDataConnection(any(ConnectionRequest.class), eq(true)))
            .thenThrow(new BizException("添加失败"));

        // Act & Assert
        mockMvc.perform(post("/data-source")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isInternalServerError())
            .andExpect(result -> assertThat(result.getResolvedException()).isInstanceOf(BizException.class))
            .andExpect(result -> assertThat(Objects.requireNonNull(result.getResolvedException()).getMessage())
                .contains("添加失败"));
    }

    @Test
    void updateDataSource_ValidRequest_ReturnsSuccess() throws Exception {
        // Arrange
        ConnectionRequest request = createValidConnectionRequest();

        // Act & Assert
        mockMvc.perform(put("/data-source/{id}", 1)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk());

        verify(dataConnectionService, times(1)).updateConnection(eq(1), any(ConnectionRequest.class), eq(true));
    }

    @Test
    void updateDataSource_InvalidRequest_ReturnsBadRequest() throws Exception {
        // Arrange
        ConnectionRequest request = new ConnectionRequest();

        // Act & Assert
        mockMvc.perform(put("/data-source/{id}", 1)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isInternalServerError());
    }

    @Test
    void deleteDataSource_ValidId_ReturnsSuccess() throws Exception {
        // Arrange
        Integer id = 1;

        // Act & Assert
        mockMvc.perform(delete("/data-source/{id}", id))
            .andExpect(status().isOk());

        verify(dataConnectionService, times(1)).deleteConnection(id, true);
    }

    @Test
    void deleteDataSource_ServiceThrowsException_ReturnsBadRequest() throws Exception {
        // Arrange
        Integer id = 1;
        doThrow(new BizException("删除失败")).when(dataConnectionService).deleteConnection(id, true);

        // Act & Assert
        mockMvc.perform(delete("/data-source/{id}", id))
            .andExpect(status().isInternalServerError())
            .andExpect(result -> assertThat(result.getResolvedException()).isInstanceOf(BizException.class));
    }

    private ConnectionRequest createValidConnectionRequest() {
        ConnectionRequest request = new ConnectionRequest();
        request.setName("testName");
        request.setTestSuccess(true);

        MysqlConnectionParams connectionParams = new MysqlConnectionParams();
        connectionParams.setHost("localhost");
        connectionParams.setUsername("root");
        connectionParams.setPassword("password");
        connectionParams.setPort(3306);
        connectionParams.setConnectionType(ConnectionType.MYSQL);
        connectionParams.setProtocol("http");
        connectionParams.setDatabase("test_db");

        request.setConnectionParams(connectionParams);
        return request;
    }
}