package com.trs.ai.moye.data.standard.dao;

import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.ai.moye.data.standard.entity.MetaDataStandardCatalog;
import com.trs.ai.moye.data.standard.entity.MetaDataStandardCatalogTree;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class MetaDataStandardCatalogMapperTest {

    @Autowired
    private MetaDataStandardCatalogMapper metaDataStandardCatalogMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        jdbcTemplate.execute("TRUNCATE TABLE meta_data_standard_catalog");
    }

    @Test
    void selectCatalogTree() {
        MetaDataStandardCatalog rootCatalog = new MetaDataStandardCatalog();
        rootCatalog.setName("root");
        rootCatalog.setPid(0);
        metaDataStandardCatalogMapper.insert(rootCatalog);
        MetaDataStandardCatalog secondCatalog = new MetaDataStandardCatalog();
        secondCatalog.setName("second");
        secondCatalog.setPid(rootCatalog.getId());
        metaDataStandardCatalogMapper.insert(secondCatalog);

        List<MetaDataStandardCatalogTree> metaDataStandardCatalogTrees = metaDataStandardCatalogMapper.selectCatalogTree();
        Assertions.assertEquals(1, metaDataStandardCatalogTrees.size());
        Assertions.assertEquals(1, metaDataStandardCatalogTrees.get(0).getChildren().size());
    }

    @Test
    void selectByCatalogName() {
        MetaDataStandardCatalog metaDataStandardCatalog = new MetaDataStandardCatalog();
        metaDataStandardCatalog.setName("test");
        metaDataStandardCatalogMapper.insert(metaDataStandardCatalog);

        MetaDataStandardCatalog result = metaDataStandardCatalogMapper.selectByCatalogName("test");
        Assertions.assertNotNull(result);
    }

    @Test
    void countByNameAndExcludeId() {
        MetaDataStandardCatalog metaDataStandardCatalog = new MetaDataStandardCatalog();
        metaDataStandardCatalog.setName("test");
        metaDataStandardCatalogMapper.insert(metaDataStandardCatalog);

        Integer count = metaDataStandardCatalogMapper.countByNameAndExcludeId("test", metaDataStandardCatalog.getId());
        Assertions.assertEquals(0, count);
        Integer count1 = metaDataStandardCatalogMapper.countByNameAndExcludeId("test", null);
        Assertions.assertEquals(1, count1);
    }

    @Test
    void findAllByParentId() {
        MetaDataStandardCatalog rootCatalog = new MetaDataStandardCatalog();
        rootCatalog.setName("root");
        rootCatalog.setPid(0);
        metaDataStandardCatalogMapper.insert(rootCatalog);
        MetaDataStandardCatalog secondCatalog = new MetaDataStandardCatalog();
        secondCatalog.setName("second");
        secondCatalog.setPid(rootCatalog.getId());
        metaDataStandardCatalogMapper.insert(secondCatalog);

        List<MetaDataStandardCatalog> metaDataStandardCatalogs = metaDataStandardCatalogMapper.findAllByParentId(
            rootCatalog.getId());
        Assertions.assertEquals(1, metaDataStandardCatalogs.size());
    }
}