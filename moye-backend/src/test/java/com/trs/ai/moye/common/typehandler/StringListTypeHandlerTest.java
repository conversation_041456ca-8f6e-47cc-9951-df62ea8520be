package com.trs.ai.moye.common.typehandler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import org.apache.ibatis.type.JdbcType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class StringListTypeHandlerTest {

    private StringListTypeHandler typeHandler;
    private PreparedStatement mockPreparedStatement;
    private ResultSet mockResultSet;
    private CallableStatement mockCallableStatement;

    @BeforeEach
    public void setUp() {
        typeHandler = new StringListTypeHandler();
        mockPreparedStatement = mock(PreparedStatement.class);
        mockResultSet = mock(ResultSet.class);
        mockCallableStatement = mock(CallableStatement.class);
    }

    @Test
    void testSetNonNullParameter() throws SQLException {
        // Given
        List<String> list = Arrays.asList("apple", "banana", "cherry");

        // When
        typeHandler.setNonNullParameter(mockPreparedStatement, 1, list, JdbcType.VARCHAR);

        // Then
        verify(mockPreparedStatement).setString(1, "apple,banana,cherry");
    }

    @Test
    void testGetNullableResultByColumnName() throws SQLException {
        // Given
        when(mockResultSet.getString("tags")).thenReturn("apple,banana,cherry");

        // When
        List<String> result = typeHandler.getNullableResult(mockResultSet, "tags");

        // Then
        assertEquals(Arrays.asList("apple", "banana", "cherry"), result);
    }

    @Test
    void testGetNullableResultByColumnIndex() throws SQLException {
        // Given
        when(mockResultSet.getString(1)).thenReturn("apple,banana,cherry");

        // When
        List<String> result = typeHandler.getNullableResult(mockResultSet, 1);

        // Then
        assertEquals(Arrays.asList("apple", "banana", "cherry"), result);
    }

    @Test
    void testGetNullableResultFromCallableStatement() throws SQLException {
        // Given
        when(mockCallableStatement.getString(1)).thenReturn("apple,banana,cherry");

        // When
        List<String> result = typeHandler.getNullableResult(mockCallableStatement, 1);

        // Then
        assertEquals(Arrays.asList("apple", "banana", "cherry"), result);
    }

    @Test
    void testHandleNullValues() throws SQLException {
        // Given
        when(mockResultSet.getString("tags")).thenReturn(null);

        // When
        List<String> result = typeHandler.getNullableResult(mockResultSet, "tags");

        // Then
        assertNull(result);

        // When handling empty string
        when(mockResultSet.getString("tags")).thenReturn("");
        result = typeHandler.getNullableResult(mockResultSet, "tags");

        // Then
        assertEquals(List.of(""), result);
    }
}