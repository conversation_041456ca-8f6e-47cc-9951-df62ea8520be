package com.trs.ai.moye.data.standard.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.moye.base.data.standard.entity.MetaDataStandardField;
import com.trs.ai.moye.data.standard.entity.MetaDataStandardFieldQueryParams;
import com.trs.moye.base.data.standard.entity.TagEdgeField;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.request.PageParams;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class MetaDataStandardFieldMapperTest {

    @Autowired
    private MetaDataStandardFieldMapper metaDataStandardFieldMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        jdbcTemplate.execute("TRUNCATE TABLE meta_data_standard_field");
    }

    @Test
    void selectSpecifiedFieldsByStandardId() {

        MetaDataStandardField metaDataStandardField = getMetaDataStandardField();
        metaDataStandardFieldMapper.insert(metaDataStandardField);

        MetaDataStandardFieldQueryParams metaDataStandardFieldQueryParams = new MetaDataStandardFieldQueryParams();
        metaDataStandardFieldQueryParams.setMetaDataStandardId(1);
        metaDataStandardFieldQueryParams.setFieldType(FieldType.GRAPHICS_EDGE);
        metaDataStandardFieldQueryParams.setKeyWord("person");

        PageParams pageParams = new PageParams();
        pageParams.setPageNum(1);
        pageParams.setPageSize(10);

        Page<MetaDataStandardField> pageResult = metaDataStandardFieldMapper.selectSpecifiedFieldsByStandardId(
            metaDataStandardFieldQueryParams, pageParams.toPage());
        List<MetaDataStandardField> records = pageResult.getRecords();

        Assertions.assertTrue(records.stream().anyMatch(item -> item.getEnName().equals("person")));
        Assertions.assertEquals(1, records.size());
    }

    private static MetaDataStandardField getMetaDataStandardField() {
        MetaDataStandardField metaDataStandardField = new MetaDataStandardField();
        metaDataStandardField.setMetaDataStandardId(1);
        metaDataStandardField.setType(FieldType.GRAPHICS_EDGE);
        metaDataStandardField.setEnName("person");
        metaDataStandardField.setZhName("人员");
        TagEdgeField tagEdgeField = new TagEdgeField();
        tagEdgeField.setEnName("name");
        tagEdgeField.setZhName("姓名");
        tagEdgeField.setType("string");
        tagEdgeField.setNotNull(true);
        metaDataStandardField.setFields(List.of(tagEdgeField));
        return metaDataStandardField;
    }

    @Test
    void selectByMetaDataStandardId() {
        MetaDataStandardField expected = getMetaDataStandardField();
        metaDataStandardFieldMapper.insert(expected);

        List<MetaDataStandardField> selectResults = metaDataStandardFieldMapper.selectByMetaDataStandardId(
            expected.getMetaDataStandardId());

        Assertions.assertEquals(1, selectResults.size());
        Assertions.assertEquals(expected.getEnName(), selectResults.get(0).getEnName());
    }

    @Test
    void selectByEnNameAndStandardId() {
        MetaDataStandardField metaDataStandardField = getMetaDataStandardField();
        metaDataStandardFieldMapper.insert(metaDataStandardField);

        MetaDataStandardField result = metaDataStandardFieldMapper.selectByEnNameAndStandardId(1, "person");
        Assertions.assertNotNull(result);
        Assertions.assertEquals("人员", result.getZhName());
    }

    @Test
    void countByEnNameAndExcludeId() {
        MetaDataStandardField metaDataStandardField = getMetaDataStandardField();
        metaDataStandardFieldMapper.insert(metaDataStandardField);

        Integer count = metaDataStandardFieldMapper.countByEnNameAndExcludeId(1, "person",
            metaDataStandardField.getId());
        Assertions.assertEquals(0, count);

        Integer countExcludeId = metaDataStandardFieldMapper.countByEnNameAndExcludeId(1, "person", null);
        Assertions.assertEquals(1, countExcludeId);
    }

}