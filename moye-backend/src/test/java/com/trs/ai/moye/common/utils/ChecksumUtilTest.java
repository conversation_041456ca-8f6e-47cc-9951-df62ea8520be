package com.trs.ai.moye.common.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.http.HttpHeaders;

class ChecksumUtilTest {

    @Test
    void getTrsCheckHeaders_ShouldReturnHeadersWithChecksum() {
        String uri = "/api/test";
        HttpHeaders headers = ChecksumUtil.getTrsCheckHeaders(uri);
        
        assertNotNull(headers);
        assertTrue(headers.containsKey("TRS-CHECKSUM"));
        assertNotNull(headers.getFirst("TRS-CHECKSUM"));
        assertFalse(headers.getFirst("TRS-CHECKSUM").isEmpty());
    }

    @ParameterizedTest
    @ValueSource(strings = {"/api/test", "/user/1", "/data/source"})
    void getTrsCheckHeaders_ShouldReturnConsistentChecksumForSameUri(String uri) {
        HttpHeaders headers1 = ChecksumUtil.getTrsCheckHeaders(uri);
        HttpHeaders headers2 = ChecksumUtil.getTrsCheckHeaders(uri);
        
        assertEquals(headers1.getFirst("TRS-CHECKSUM"), headers2.getFirst("TRS-CHECKSUM"));
    }

    @Test
    void getTrsCheckHeaders_ShouldReturnDifferentChecksumForDifferentUris() {
        HttpHeaders headers1 = ChecksumUtil.getTrsCheckHeaders("/api/test1");
        HttpHeaders headers2 = ChecksumUtil.getTrsCheckHeaders("/api/test2");
        
        assertNotEquals(headers1.getFirst("TRS-CHECKSUM"), headers2.getFirst("TRS-CHECKSUM"));
    }

    @ParameterizedTest
    @ValueSource(strings = {"test", "hello world", "12345", "!@#$%^&*()"})
    void encrypt_ShouldReturnNonNullNonEmptyString(String input) {
        String result = ChecksumUtil.encrypt(input);
        
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @ParameterizedTest
    @ValueSource(strings = {"test", "hello world", "12345", "!@#$%^&*()"})
    void encrypt_ShouldReturnConsistentResultForSameInput(String input) {
        String result1 = ChecksumUtil.encrypt(input);
        String result2 = ChecksumUtil.encrypt(input);
        
        assertEquals(result1, result2);
    }

    @Test
    void encrypt_ShouldReturnDifferentResultsForDifferentInputs() {
        String result1 = ChecksumUtil.encrypt("input1");
        String result2 = ChecksumUtil.encrypt("input2");
        
        assertNotEquals(result1, result2);
    }

    @ParameterizedTest
    @NullAndEmptySource
    void encrypt_ShouldHandleNullAndEmptyInput(String input) {
        String result = ChecksumUtil.encrypt(input);
        
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    void encrypt_ShouldReturnFixedLengthString() {
        String shortInput = "a";
        String longInput = "a".repeat(1000);
        
        assertEquals(ChecksumUtil.encrypt(shortInput).length(), ChecksumUtil.encrypt(longInput).length());
    }
}