package com.trs.ai.moye.out.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.ai.moye.common.web.response.GlobalExceptionProcessor;
import com.trs.ai.moye.data.model.enums.CategoryTreeNodeType;
import com.trs.ai.moye.data.model.request.BusinessCategoryRequest;
import com.trs.ai.moye.data.model.request.CategoryTreeRequest;
import com.trs.ai.moye.data.model.response.CategoryTreeResponse;
import com.trs.ai.moye.data.model.service.DataModelCategoryService;
import com.trs.ai.moye.data.model.service.DataModelService;
import com.trs.ai.moye.data.standard.dao.MetaDataStandardMapper;
import com.trs.ai.moye.data.standard.entity.GraphicsMetaDataStandard;
import com.trs.ai.moye.out.request.OutBusinessCategoryUpdateRequest;
import com.trs.ai.moye.out.request.OutDataModelCreateRequest;
import com.trs.ai.moye.out.service.OutApiService;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(MockitoExtension.class)
class OutDataModelControllerTest {

    @InjectMocks
    private OutDataModelController outDataModelController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(outDataModelController)
            .setControllerAdvice(new GlobalExceptionProcessor())
            .build();
    }

    @Mock
    private DataModelService dataModelService;

    @Mock
    private DataModelCategoryService dataModelCategoryService;

    @Mock
    private DataModelMapper dataModelMapper;

    @Mock
    private DataModelFieldMapper dataModelFieldMapper;

    @Mock
    private MetaDataStandardMapper metaDataStandardMapper;

    @Mock
    private DataStorageMapper dataStorageMapper;

    @Mock
    private OutApiService outApiService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void updateCategory_SuccessfulUpdate_ReturnsTrue() throws Exception {
        OutBusinessCategoryUpdateRequest request = new OutBusinessCategoryUpdateRequest();
        request.setBusinessCategoryId(1);
        request.setBusinessName("Updated Name");

        when(dataModelCategoryService.updateCategory(new BusinessCategoryRequest(request), 1)).thenReturn(true);

        mockMvc.perform(put("/out/data-model/category").contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request))).andExpect(status().isOk());
    }

    @Test
    void updateCategory_FailedUpdate_ReturnsFalse() throws Exception {
        OutBusinessCategoryUpdateRequest request = new OutBusinessCategoryUpdateRequest();
        request.setBusinessCategoryId(1);
        request.setBusinessName("Updated Name");

        when(dataModelCategoryService.updateCategory(new BusinessCategoryRequest(request), 1)).thenReturn(false);

        mockMvc.perform(put("/out/data-model/category").contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request))).andExpect(status().isOk());
    }

    @Test
    void updateCategory_InvalidInput_ReturnsBadRequest() throws Exception {
        OutBusinessCategoryUpdateRequest request = new OutBusinessCategoryUpdateRequest();
        request.setBusinessName("Updated Name");
        mockMvc.perform(put("/out/data-model/category")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isInternalServerError());
    }

    @Test
    void getCategoryTree_ShouldReturnCategoryTree() throws Exception {
        // Arrange
        List<CategoryTreeResponse> categoryTreeResponses = Arrays.asList(
            new CategoryTreeResponse(CategoryTreeNodeType.BUSINESS, "Category1", "icon1"),
            new CategoryTreeResponse(CategoryTreeNodeType.LIBRARY, "Category2", "icon2"));
        when(dataModelCategoryService.getCategoryTree(new CategoryTreeRequest())).thenReturn(categoryTreeResponses);

        String expectedResponse = "[{\"enName\":\"Category1\",\"iconName\":\"icon1\",\"nodeType\":\"BUSINESS\"},{\"enName\":\"Category2\",\"iconName\":\"icon2\",\"nodeType\":\"LIBRARY\"}]";

        // Act & Assert
        mockMvc.perform(get("/out/data-model/category/tree").accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().json(expectedResponse, false));
    }


    @Test
    void getDataModel_DataModelExists_ReturnsResponse() throws Exception {
        Integer dataModelId = 1;
        DataModel dataModel = new DataModel();
        GraphicsMetaDataStandard metaDataStandard = new GraphicsMetaDataStandard();
        List<DataStorage> dataStorages = Collections.singletonList(new DataStorage());
        List<DataModelField> dataModelFields = Collections.singletonList(new DataModelField());

        when(dataModelMapper.getById(dataModelId)).thenReturn(dataModel);
        when(metaDataStandardMapper.selectByDataModelId(dataModelId)).thenReturn(metaDataStandard);
        when(dataStorageMapper.selectByDataModelId(dataModelId)).thenReturn(dataStorages);
        when(dataModelFieldMapper.selectByDataModelId(dataModelId)).thenReturn(dataModelFields);

        mockMvc.perform(get("/out/data-model/{dataModelId}", dataModelId)).andExpect(status().isOk());
    }

    @Test
    void getDataModel_DataModelDoesNotExist_ThrowsException() throws Exception {
        Integer dataModelId = 1;

        when(dataModelMapper.getById(dataModelId)).thenReturn(null);

        mockMvc.perform(get("/out/data-model/{dataModelId}", dataModelId))
            .andExpect(result -> assertThat(result.getResolvedException()).isInstanceOf(BizException.class))
            .andExpect(result -> assertThat(Objects.requireNonNull(result.getResolvedException()).getMessage())
                .contains("数据建模不存在, dataModelId: " + dataModelId))
            .andExpect(status().isInternalServerError());
    }

    @Test
    void createDataModelByMetaData_ValidRequest_ShouldReturnDataModelId() throws Exception {
        // Arrange
        OutDataModelCreateRequest request = new OutDataModelCreateRequest();
        request.setBusinessCategoryId(1);
        request.setConnectionId(2);
        request.setMetadataId(3);

        when(outApiService.outCreateDataModel(any(OutDataModelCreateRequest.class))).thenReturn(100);

        String expectedResponse = "{\"dataModelId\":100}";

        // Act & Assert
        mockMvc.perform(post("/out/data-model")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(content().json(expectedResponse));
    }

    @Test
    void createDataModelByMetaData_ServiceThrowsException_ShouldReturnError() throws Exception {
        // Arrange
        OutDataModelCreateRequest request = new OutDataModelCreateRequest();
        request.setBusinessCategoryId(1);
        request.setConnectionId(2);
        request.setMetadataId(3);

        doThrow(new BizException("Service error")).when(outApiService)
            .outCreateDataModel(any(OutDataModelCreateRequest.class));

        // Act & Assert
        mockMvc.perform(post("/out/data-model")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"businessCategoryId\":1,\"connectionId\":2,\"metadataId\":3}"))
            .andExpect(status().isInternalServerError());
    }

    @Test
    void deleteModel_ModelExists_ShouldReturnOk() throws Exception {
        Integer dataModelId = 1;
        lenient().when(dataModelService.deleteModel(dataModelId)).thenReturn(true);

        mockMvc.perform(delete("/out/data-model/{dataModelId}", dataModelId))
            .andExpect(status().isOk());
    }


    @Test
    void addCategory_ValidRequest_ReturnsBusinessCategoryId() throws Exception {
        // Arrange
        BusinessCategoryRequest request = new BusinessCategoryRequest("Business Name", "BusinessEnName", "Description");
        when(dataModelCategoryService.addCategoryReturnId(any(BusinessCategoryRequest.class))).thenReturn(1);

        String expectedResponse = "{\"businessCategoryId\":1}";

        // Act & Assert
        mockMvc.perform(post("/out/data-model/category").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))).andExpect(status().isOk())
            .andExpect(content().string(expectedResponse));

        verify(dataModelCategoryService, times(1)).addCategoryReturnId(any(BusinessCategoryRequest.class));
    }


    @Test
    void addCategory_ServiceThrowsBizException_ReturnsBadRequest() throws Exception {
        // Arrange
        BusinessCategoryRequest request = new BusinessCategoryRequest("Business Name", "BusinessEnName", "Description");
        when(dataModelCategoryService.addCategoryReturnId(any(BusinessCategoryRequest.class))).thenThrow(
            new BizException("Service error"));

        // Act & Assert
        mockMvc.perform(post("/out/data-model/category")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isInternalServerError());

        verify(dataModelCategoryService, times(1))
            .addCategoryReturnId(any(BusinessCategoryRequest.class));
    }
}
