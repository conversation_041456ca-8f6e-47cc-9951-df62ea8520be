package com.trs.ai.moye.monitor.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import com.trs.ai.moye.monitor.entity.CutoffPeriod;
import com.trs.moye.base.monitor.entity.MonitorOdsCutoff;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;

class CutoffPeriodDetectorTest {

    @Test
    void testSingleCutoffPeriod() {
        // 构造测试数据
        List<MonitorOdsCutoff> records = Arrays.asList(
            createCutoffRecord(true, "2025-03-15T09:00", "id=100"),
            createCutoffRecord(true, "2025-03-15T10:00", "id=101"),
            createCutoffRecord(false, "2025-03-15T11:00", null)
        );

        CutoffPeriodDetector detector = new CutoffPeriodDetector();
        List<CutoffPeriod> periods = detector.processRecords(records);

        // 验证结果
        assertEquals(1, periods.size());
        CutoffPeriod period = periods.get(0);
        assertEquals("2025-03-15T09:00", period.getStartTime().toString());
        assertEquals("2025-03-15T11:00", period.getEndTime().toString());
        assertEquals("id=101", period.getLatestIncrementValue());
    }

    @Test
    void testOngoingCutoff() {
        List<MonitorOdsCutoff> records = Arrays.asList(
            createCutoffRecord(true, "2025-03-15T12:00", "id=200"),
            createCutoffRecord(true, "2025-03-15T13:00", "id=201")
        );

        CutoffPeriodDetector detector = new CutoffPeriodDetector();
        List<CutoffPeriod> periods = detector.processRecords(records);

        assertEquals(1, periods.size());
        CutoffPeriod period = periods.get(0);
        assertTrue(period.isInCutoff());
        assertEquals("id=201", period.getLatestIncrementValue());
        assertEquals("2025-03-15T13:00", period.getEndTime().toString());
    }

    private MonitorOdsCutoff createCutoffRecord(boolean isCutoff, String time, String incrementValue) {
        MonitorOdsCutoff cutoff = new MonitorOdsCutoff();
        cutoff.setDataModelId(1001);
        cutoff.setCutoff(isCutoff);
        cutoff.setMonitorTime(LocalDateTime.parse(time));
        cutoff.setIncrementValue(incrementValue);
        return cutoff;
    }

}