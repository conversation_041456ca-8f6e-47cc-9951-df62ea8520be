package com.trs.ai.moye.knowledgebase.dao.dynamicrepository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.ai.moye.common.mybatis.MybatisConfiguration;
import com.trs.moye.base.common.enums.SortOrder;
import com.trs.moye.base.common.request.PageParams;
import com.trs.moye.base.common.request.SearchParams;
import com.trs.moye.base.common.request.SortParams;
import com.trs.moye.base.knowledgebase.entity.EntityColumn;
import com.trs.moye.base.knowledgebase.entity.TableInsertDataParam;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.context.annotation.Import;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

/**
 * KnowledgeBaseDataMapperTest
 *
 * <AUTHOR>
 * @since 2025/6/5 17:02
 */
@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@Import(MybatisConfiguration.class)
class KnowledgeBaseDataMapperTest {

    @Autowired
    private KnowledgeBaseDataMapper knowledgeBaseDataMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private static final String TABLE_NAME = "test_table";

    @BeforeEach
    void setUp() {
        // Drop the table if it exists
        jdbcTemplate.execute("DROP TABLE IF EXISTS " + TABLE_NAME);

        // Create the table
        jdbcTemplate.execute("CREATE TABLE " + TABLE_NAME + " (" +
                             "id INT AUTO_INCREMENT PRIMARY KEY, " +
                             "name VARCHAR(255) NOT NULL, " +
                             "amount INT," +
                             "situation VARCHAR(255)" +
                             ")");
    }

    private void insertTestData() {
        // Insert mock data into the table
        jdbcTemplate.update("INSERT INTO " + TABLE_NAME + " (name, amount) VALUES (?, ?)", "Test Name 1", 100);
        jdbcTemplate.update("INSERT INTO " + TABLE_NAME + " (name, amount) VALUES (?, ?)", "Test Name 2", 200);
    }

    @Test
    void selectTableDataList() {
        insertTestData();

        // Case 1: No search parameters, no sort parameters
        // This test verifies that all data is returned when no filters or sorting are applied.
        Page<Map<String, Object>> resultsNoParams = knowledgeBaseDataMapper.selectTableDataList(
            TABLE_NAME, null, null, new PageParams().toPage());

        // Assertions for Case 1
        assertNotNull(resultsNoParams, "Results should not be null");
        assertEquals(2, resultsNoParams.getRecords().size(), "Total records should match the inserted data count");

        // Case 2: Search parameters, no sort parameters
        // This test verifies that the search functionality works correctly when filtering by a specific keyword.
        SearchParams searchParams = new SearchParams();
        searchParams.setKeyword("Test Name 1");
        searchParams.setFields(List.of("name"));

        Page<Map<String, Object>> resultsWithSearch = knowledgeBaseDataMapper.selectTableDataList(
            TABLE_NAME, searchParams, null, new PageParams().toPage());

        // Assertions for Case 2
        assertNotNull(resultsWithSearch, "Results should not be null");
        assertEquals(1, resultsWithSearch.getTotal(), "Only one record should match the search criteria");
        assertEquals("Test Name 1", resultsWithSearch.getRecords().get(0).get("name"), "The returned record should match the search keyword");

        // Case 3: Search parameters with sort parameters
        // This test verifies that the search results are correctly sorted based on the specified field and order.
        SortParams sortParams = new SortParams();
        sortParams.setField("amount");
        sortParams.setOrder(SortOrder.DESC);

        Page<Map<String, Object>> resultsWithSearchAndSort = knowledgeBaseDataMapper.selectTableDataList(
            TABLE_NAME, searchParams, sortParams, new PageParams().toPage());

        // Assertions for Case 3
        assertNotNull(resultsWithSearchAndSort, "Results should not be null");
        assertEquals(1, resultsWithSearchAndSort.getTotal(), "Only one record should match the search criteria");
        assertEquals("Test Name 1", resultsWithSearchAndSort.getRecords().get(0).get("name"), "The returned record should match the search keyword");
        assertEquals(100, resultsWithSearchAndSort.getRecords().get(0).get("amount"), "The record's amount should match the expected amount");

        // Case 4: Both search parameters and sort parameters
        searchParams.setKeyword("Test");
        Page<Map<String, Object>> resultsWithBothParams = knowledgeBaseDataMapper.selectTableDataList(
            TABLE_NAME, searchParams, sortParams, new PageParams().toPage());
        assertNotNull(resultsWithBothParams, "Results should not be null");
        assertEquals(2, resultsWithBothParams.getTotal(), "Both records should match the search criteria");
        assertEquals("Test Name 2", resultsWithBothParams.getRecords().get(0).get("name"), "The first record should match the sorted order");
        assertEquals(200, resultsWithBothParams.getRecords().get(0).get("amount"), "The first record's amount should match the sorted order");
    }

    @Test
    void getConditionPageList() {
        insertTestData();

        // Case 1: No conditions
        Page<Map<String, Object>> resultNoConditions = knowledgeBaseDataMapper.getConditionPageList(
            TABLE_NAME, null, null, new PageParams().toPage());
        assertNotNull(resultNoConditions, "Results should not be null");
        assertEquals(2, resultNoConditions.getTotal(), "Total records should match the inserted data count");

        // Case 2: With conditions
        String conditions = "`name` LIKE '%Test%'";
        Page<Map<String, Object>> resultWithConditions = knowledgeBaseDataMapper.getConditionPageList(
            TABLE_NAME, conditions, null, new PageParams().toPage());
        assertNotNull(resultWithConditions, "Results should not be null");
        assertEquals(2, resultWithConditions.getTotal(), "Both records should match the condition");

        // Case 3: With conditions and sorting
        SortParams sortParams = new SortParams("amount", SortOrder.DESC);
        Page<Map<String, Object>> resultWithConditionsAndSort = knowledgeBaseDataMapper.getConditionPageList(
            TABLE_NAME, conditions, sortParams, new PageParams().toPage());
        assertNotNull(resultWithConditionsAndSort, "Results should not be null");
        assertEquals(2, resultWithConditionsAndSort.getTotal(), "Both records should match the condition");
        assertEquals("Test Name 2", resultWithConditionsAndSort.getRecords().get(0).get("name"), "First record should match the sorted order");
    }

    @Test
    void getMultiSearchPageList() {
        insertTestData();

        // Case 1: Single search condition
        SearchParams searchParam1 = new SearchParams();
        searchParam1.setKeyword("Test Name 1");
        searchParam1.setFields(List.of("name"));
        Page<Map<String, Object>> resultSingleSearch = knowledgeBaseDataMapper.getMultiSearchPageList(
            TABLE_NAME, List.of(searchParam1), null, new PageParams().toPage());
        assertNotNull(resultSingleSearch, "Results should not be null");
        assertEquals(1, resultSingleSearch.getTotal(), "Only one record should match the search condition");

        // Case 2: Multiple search conditions
        SearchParams searchParam2 = new SearchParams();
        searchParam2.setKeyword("200");
        searchParam2.setFields(List.of("amount"));
        Page<Map<String, Object>> resultMultiSearch = knowledgeBaseDataMapper.getMultiSearchPageList(
            TABLE_NAME, List.of(searchParam1, searchParam2), null, new PageParams().toPage());
        assertNotNull(resultMultiSearch, "Results should not be null");
        assertEquals(0, resultMultiSearch.getTotal(), "Both records should match the search conditions");

        // Case 3: Sorting
        SortParams sortParams = new SortParams("amount", SortOrder.ASC);
        Page<Map<String, Object>> resultMultiSearchWithSort = knowledgeBaseDataMapper.getMultiSearchPageList(
            TABLE_NAME, List.of(), sortParams, new PageParams().toPage());
        assertNotNull(resultMultiSearchWithSort, "Results should not be null");
        assertEquals(2, resultMultiSearchWithSort.getTotal(), "Both records should match the search conditions");
        assertEquals("Test Name 1", resultMultiSearchWithSort.getRecords().get(0).get("name"), "First record should match the sorted order");
    }

    @Test
    void deleteTableDataByIds() {
        insertTestData();

        // Case 1: Delete a single record
        knowledgeBaseDataMapper.deleteTableDataByIds(TABLE_NAME, List.of(1));
        List<Map<String, Object>> remainingDataSingleDelete = jdbcTemplate.queryForList("SELECT * FROM " + TABLE_NAME);
        assertEquals(1, remainingDataSingleDelete.size(), "Only one record should remain");
        assertEquals("Test Name 2", remainingDataSingleDelete.get(0).get("name"), "Remaining record should match the expected name");

        // Case 2: Delete all records
        knowledgeBaseDataMapper.deleteTableDataByIds(TABLE_NAME, List.of(2));
        List<Map<String, Object>> remainingDataAllDelete = jdbcTemplate.queryForList("SELECT * FROM " + TABLE_NAME);
        assertEquals(0, remainingDataAllDelete.size(), "No records should remain after deletion");
    }

    @Test
    void insertTableData() {
        // Insert a single row
        knowledgeBaseDataMapper.insertTableData(new TableInsertDataParam(TABLE_NAME, List.of(new EntityColumn("name", "Inserted Name"))));

        // Query the data
        List<Map<String, Object>> results = jdbcTemplate.queryForList("SELECT * FROM " + TABLE_NAME);

        // Assertions
        assertEquals(1, results.size());
        assertEquals("Inserted Name", results.get(0).get("name"));
    }

    @Test
    void updateTableData() {
        insertTestData();

        // Define updated columns
        List<EntityColumn> updatedColumns = List.of(
            new EntityColumn("name", "Updated Name"),
            new EntityColumn("amount", 300)
        );

        // Call the mapper method
        knowledgeBaseDataMapper.updateTableData(TABLE_NAME, 1, updatedColumns);

        // Query the updated row
        Map<String, Object> updatedRow = knowledgeBaseDataMapper.selectOneByColNameAndValue(TABLE_NAME, "id", 1);

        // Assertions
        assertNotNull(updatedRow);
        assertEquals("Updated Name", updatedRow.get("name"));
        assertEquals(300, updatedRow.get("amount"));
    }

    @Test
    void selectAttrData() {
        insertTestData();

        // Case 1: No keyword, no situation, no sorting
        List<String> resultNoParams = knowledgeBaseDataMapper.selectAttrData(TABLE_NAME, "name", null, null, null);
        assertNotNull(resultNoParams, "Results should not be null");
        assertEquals(2, resultNoParams.size(), "Both records should be returned");
        assertTrue(resultNoParams.contains("Test Name 1"), "Result should contain 'Test Name 1'");
        assertTrue(resultNoParams.contains("Test Name 2"), "Result should contain 'Test Name 2'");

        // Case 2: With keyword, no situation, no sorting
        List<String> resultWithKeyword = knowledgeBaseDataMapper.selectAttrData(TABLE_NAME, "name", "Test Name 1", null, null);
        assertNotNull(resultWithKeyword, "Results should not be null");
        assertEquals(1, resultWithKeyword.size(), "Only one record should match the keyword");
        assertEquals("Test Name 1", resultWithKeyword.get(0), "The returned record should match the keyword");

        // Case 3: With keyword and situation, no sorting
        List<String> resultWithKeywordAndSituation = knowledgeBaseDataMapper.selectAttrData(TABLE_NAME, "name", "Test Name 1", "some_situation", null);
        assertNotNull(resultWithKeywordAndSituation, "Results should not be null");
        assertEquals(0, resultWithKeywordAndSituation.size(), "No records should match the keyword and situation");
    }
}