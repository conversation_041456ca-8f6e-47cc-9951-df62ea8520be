package com.trs.ai.moye.common.utils;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.contains;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class EasyExcelUtilsTest {

    @Mock
    private HttpServletResponse mockResponse;

    @TempDir
    File tempDir;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void innerExport_shouldWriteDataToOutputStream() {
        List<List<String>> headFiled = Arrays.asList(
            Arrays.asList("Header 1"),
            Arrays.asList("Header 2")
        );
        List<List<Object>> data = Arrays.asList(
            Arrays.asList("Data 1", "Data 2"),
            Arrays.asList("Data 3", "Data 4")
        );

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        assertDoesNotThrow(() -> EasyExcelUtils.innerExport(outputStream, headFiled, data));

        byte[] excelContent = outputStream.toByteArray();
        assertTrue(excelContent.length > 0, "Excel content should not be empty");

        // Verify the content is a valid Excel file
        assertDoesNotThrow(() -> {
            try (InputStream is = new ByteArrayInputStream(excelContent);
                Workbook workbook = WorkbookFactory.create(is)) {

                assertNotNull(workbook, "Workbook should not be null");
                assertEquals(1, workbook.getNumberOfSheets(), "Workbook should have 1 sheet");

                org.apache.poi.ss.usermodel.Sheet sheet = workbook.getSheetAt(0);
                assertNotNull(sheet, "Sheet should not be null");

                // Verify headers
                assertEquals("Header 1", sheet.getRow(0).getCell(0).getStringCellValue(), "Header 1 mismatch");
                assertEquals("Header 2", sheet.getRow(0).getCell(1).getStringCellValue(), "Header 2 mismatch");

                // Verify data
                assertEquals("Data 1", sheet.getRow(1).getCell(0).getStringCellValue(), "Data 1 mismatch");
                assertEquals("Data 2", sheet.getRow(1).getCell(1).getStringCellValue(), "Data 2 mismatch");
                assertEquals("Data 3", sheet.getRow(2).getCell(0).getStringCellValue(), "Data 3 mismatch");
                assertEquals("Data 4", sheet.getRow(2).getCell(1).getStringCellValue(), "Data 4 mismatch");
            }
        });
    }

    @Test
    void setExcelFileResponse_shouldSetCorrectHeaders() {
        String fileName = "test.xlsx";
        EasyExcelUtils.setExcelFileResponse(mockResponse, fileName);

        verify(mockResponse).setContentType("application/vnd.ms-excel");
        verify(mockResponse).setCharacterEncoding(StandardCharsets.UTF_8.name());
        verify(mockResponse).setHeader(eq("Content-disposition"), contains("attachment;filename="));
    }

    @Test
    void firstCellStyle_shouldReturnCorrectlyCongfiguredCellStyle() throws IOException {
        Workbook workbook = WorkbookFactory.create(true);
        CellStyle cellStyle = EasyExcelUtils.firstCellStyle(workbook);

        assertEquals(HorizontalAlignment.CENTER, cellStyle.getAlignment());
        assertEquals(VerticalAlignment.CENTER, cellStyle.getVerticalAlignment());
        assertEquals(FillPatternType.SOLID_FOREGROUND, cellStyle.getFillPattern());
        assertEquals(IndexedColors.SKY_BLUE.getIndex(), cellStyle.getFillForegroundColor());
        assertEquals(BorderStyle.THIN, cellStyle.getBorderBottom());
        assertEquals(BorderStyle.THIN, cellStyle.getBorderLeft());
        assertEquals(BorderStyle.THIN, cellStyle.getBorderRight());
        assertEquals(BorderStyle.THIN, cellStyle.getBorderTop());

        Font font = workbook.getFontAt(cellStyle.getFontIndex());
        assertTrue(font.getBold());
    }

    @Test
    void getHorizontalCellStyleStrategy_shouldReturnCorrectlyConfiguredStrategy() {
        HorizontalCellStyleStrategy strategy = EasyExcelUtils.getHorizontalCellStyleStrategy();

        assertNotNull(strategy);
        // Additional assertions can be added here to check specific properties of the strategy
    }
}