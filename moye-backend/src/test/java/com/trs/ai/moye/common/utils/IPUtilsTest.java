package com.trs.ai.moye.common.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import javax.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class IPUtilsTest {

    @Mock
    private HttpServletRequest request;

    @Test
    void getIpAddr_FromXForwardedFor() {
        when(request.getHeader("X-Forwarded-For")).thenReturn("***********,********");
        assertEquals("***********", IPUtils.getIpAddr(request));
    }

    @Test
    void getIpAddr_FromXRealIP() {
        when(request.getHeader("X-Forwarded-For")).thenReturn(null);
        when(request.getHeader("X-Real-IP")).thenReturn("***********");
        assertEquals("***********", IPUtils.getIpAddr(request));
    }

    @Test
    void getIpAddr_FromRemoteAddr() {
        when(request.getHeader("X-Forwarded-For")).thenReturn(null);
        when(request.getHeader("X-Real-IP")).thenReturn(null);
        when(request.getHeader("Proxy-Client-IP")).thenReturn(null);
        when(request.getHeader("WL-Proxy-Client-IP")).thenReturn(null);
        when(request.getHeader("HTTP_CLIENT_IP")).thenReturn(null);
        when(request.getHeader("HTTP_X_FORWARDED_FOR")).thenReturn(null);
        when(request.getRemoteAddr()).thenReturn("***********");
        assertEquals("***********", IPUtils.getIpAddr(request));
    }

    @Test
    void getIpAddr_IgnoreUnknown() {
        when(request.getHeader("X-Forwarded-For")).thenReturn("unknown");
        when(request.getHeader("X-Real-IP")).thenReturn("***********");
        assertEquals("***********", IPUtils.getIpAddr(request));
    }

    @Test
    void getIpAddr_NoIp() {
        when(request.getHeader("X-Forwarded-For")).thenReturn(null);
        when(request.getHeader("X-Real-IP")).thenReturn(null);
        when(request.getHeader("Proxy-Client-IP")).thenReturn(null);
        when(request.getHeader("WL-Proxy-Client-IP")).thenReturn(null);
        when(request.getHeader("HTTP_CLIENT_IP")).thenReturn(null);
        when(request.getHeader("HTTP_X_FORWARDED_FOR")).thenReturn(null);
        when(request.getRemoteAddr()).thenReturn(null);
        assertEquals(null, IPUtils.getIpAddr(request));
    }

    @Test
    void getIpAddr_SingleIPInXForwardedFor() {
        when(request.getHeader("X-Forwarded-For")).thenReturn("***********");
        assertEquals("***********", IPUtils.getIpAddr(request));
    }

    @Test
    void getIpAddr_EmptyXForwardedFor() {
        when(request.getHeader("X-Forwarded-For")).thenReturn("");
        when(request.getHeader("X-Real-IP")).thenReturn("***********");
        assertEquals("***********", IPUtils.getIpAddr(request));
    }

    @Test
    void getIpAddr_AllHeadersUnknown() {
        for (String header : new String[]{"X-Forwarded-For", "X-Real-IP", "Proxy-Client-IP", "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"}) {
            when(request.getHeader(header)).thenReturn("unknown");
        }
        when(request.getRemoteAddr()).thenReturn("***********");
        assertEquals("***********", IPUtils.getIpAddr(request));
    }

    @Test
    void getIpAddr_IPv6Address() {
        when(request.getHeader("X-Forwarded-For")).thenReturn("2001:db8::1");
        assertEquals("2001:db8::1", IPUtils.getIpAddr(request));
    }

    @Test
    void getIpAddr_MixedIPv4AndIPv6() {
        when(request.getHeader("X-Forwarded-For")).thenReturn("2001:db8::1,***********");
        assertEquals("2001:db8::1", IPUtils.getIpAddr(request));
    }
}