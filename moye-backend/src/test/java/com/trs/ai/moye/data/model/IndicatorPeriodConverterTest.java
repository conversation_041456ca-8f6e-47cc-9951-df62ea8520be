package com.trs.ai.moye.data.model;

import static com.trs.moye.base.data.indicator.enums.IndicatorConstants.LAST_DAY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.trs.moye.base.common.enums.TimeUnit;
import com.trs.moye.base.data.indicator.entity.DailyStatRange;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfig;
import com.trs.moye.base.data.indicator.entity.IntervalStatRange;
import com.trs.moye.base.data.indicator.entity.MonthlyStatRange;
import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.entity.TriggerConfig;
import com.trs.moye.base.data.indicator.entity.WeeklyStatRange;
import com.trs.moye.base.data.indicator.utils.IndicatorPeriodConverter;
import com.trs.moye.base.data.schedule.MultipleScheduleInfo;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

class IndicatorPeriodConverterTest {

    @Test
    void testConvertDaily() {
        // 构造按天统计的配置
        IndicatorPeriodConfig<DailyStatRange> config = new IndicatorPeriodConfig<>(
            new DailyStatRange(LocalTime.MIN, LocalTime.MAX),
            new TriggerConfig(null, null, null, LocalTime.MIN)
        );

        MultipleScheduleInfo result = IndicatorPeriodConverter.convert(config);

        assertEquals(TimeUnit.DAY, result.getPeriodUnit());
        assertEquals("00:00:00", result.getTimePoint());
        assertNull(result.getDatePoint());
    }

    @Test
    void testConvertWeekly() {
        // 构造按周统计的配置（触发时间为周一 08:30）
        IndicatorPeriodConfig<WeeklyStatRange> config = new IndicatorPeriodConfig<>(
            new WeeklyStatRange(DayOfWeek.THURSDAY, LocalTime.MIN, DayOfWeek.WEDNESDAY, LocalTime.MAX),
            new TriggerConfig(null, null, DayOfWeek.THURSDAY, LocalTime.of(8, 30))
        );

        MultipleScheduleInfo result = IndicatorPeriodConverter.convert(config);

        assertEquals(TimeUnit.WEEK, result.getPeriodUnit());
        assertEquals("4", result.getDatePoint());
        assertEquals("08:30:00", result.getTimePoint());
    }

    @Test
    void testConvertMonthlyFixedDay() {
        // 构造按月统计的配置（触发时间为每月5日 12:00）
        IndicatorPeriodConfig<MonthlyStatRange> config = new IndicatorPeriodConfig<>(
            new MonthlyStatRange(1, LocalTime.MIN, "31", LocalTime.MAX),
            new TriggerConfig(0, "5", null, LocalTime.NOON)
        );

        MultipleScheduleInfo result = IndicatorPeriodConverter.convert(config);

        assertEquals(TimeUnit.MONTH, result.getPeriodUnit());
        assertEquals("5", result.getDatePoint());
        assertEquals("12:00:00", result.getTimePoint());
    }

    @Test
    void testConvertMonthlyLastDay() {
        // 构造月末触发配置
        IndicatorPeriodConfig<MonthlyStatRange> config = new IndicatorPeriodConfig<>(
            new MonthlyStatRange(1, LocalTime.MIN, "31", LocalTime.MAX),
            new TriggerConfig(0, LAST_DAY, null, LocalTime.MIDNIGHT)
        );

        MultipleScheduleInfo result = IndicatorPeriodConverter.convert(config);

        assertEquals(TimeUnit.MONTH, result.getPeriodUnit());
        assertEquals(LAST_DAY, result.getDatePoint());
        assertEquals("00:00:00", result.getTimePoint());
    }

    @Test
    void testConvertQuarterlyWithOffset() {
        // 构造季度统计，offsetMonths=1（下个月的10日触发）
        IndicatorPeriodConfig<IntervalStatRange> config = new IndicatorPeriodConfig<>(
            new IntervalStatRange(1, 1, LocalTime.MIN, 3, LAST_DAY, LocalTime.MAX),
            new TriggerConfig(1, "10", null, LocalTime.of(9, 0))
        );

        MultipleScheduleInfo result = IndicatorPeriodConverter.convert(config);

        assertEquals(TimeUnit.MONTH, result.getPeriodUnit());
        assertEquals("10", result.getDatePoint());
        assertEquals("09:00:00", result.getTimePoint());
        assertEquals(List.of(4, 7, 10, 1), result.getTriggerMonths());
    }

    @Test
    void testConvertHalfYearly() {
        // 构造半年统计，offsetMonths=1（下个月的10日触发）
        IndicatorPeriodConfig<IntervalStatRange> config = new IndicatorPeriodConfig<>(
            new IntervalStatRange(1, 1, LocalTime.MIN, 6, LAST_DAY, LocalTime.MAX),
            new TriggerConfig(1, "10", null, LocalTime.of(9, 0))
        );

        MultipleScheduleInfo result = IndicatorPeriodConverter.convert(config);

        assertEquals(TimeUnit.MONTH, result.getPeriodUnit());
        assertEquals("10", result.getDatePoint());
        assertEquals("09:00:00", result.getTimePoint());
        assertEquals(List.of(7, 1), result.getTriggerMonths());
    }

    @Test
    void testConvertYearly() {
        // 构造按年统计的配置（触发时间为每年1月1日 00:00）
        IndicatorPeriodConfig<IntervalStatRange> config = new IndicatorPeriodConfig<>(
            new IntervalStatRange(1, 1, LocalTime.MIN, 12, LAST_DAY, LocalTime.MAX),
            new TriggerConfig(1, "1", null, LocalTime.MIDNIGHT)
        );

        MultipleScheduleInfo result = IndicatorPeriodConverter.convert(config);

        assertEquals(TimeUnit.MONTH, result.getPeriodUnit());
        assertEquals("1", result.getDatePoint());
        assertEquals("00:00:00", result.getTimePoint());
        assertEquals(List.of(1), result.getTriggerMonths());
    }

    //------------------------ 异常场景测试 ------------------------

    @ParameterizedTest
    @MethodSource("provideInvalidConfigs")
    void testInvalidConfigThrowsException(IndicatorPeriodConfig<?> config, String expectedMessage) {
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> IndicatorPeriodConverter.convert(config)
        );
        assertTrue(exception.getMessage().contains(expectedMessage));
    }

    private static Stream<Arguments> provideInvalidConfigs() {
        return Stream.of(
            // 按天统计配置了不允许的字段（dayOfWeek）
            Arguments.of(
                new IndicatorPeriodConfig<>(
                    new DailyStatRange(LocalTime.MIN, LocalTime.MAX),
                    new TriggerConfig(null, null, DayOfWeek.MONDAY, LocalTime.MIN)
                ),
                "Daily统计周期不支持offsetMonths/day/dayOfWeek配置"
            ),
            // 按周统计未配置dayOfWeek
            Arguments.of(
                new IndicatorPeriodConfig<>(
                    new WeeklyStatRange(DayOfWeek.MONDAY, LocalTime.MIN, DayOfWeek.SUNDAY, LocalTime.MAX),
                    new TriggerConfig(null, null, null, LocalTime.MIN)
                ),
                "Weekly统计周期必须配置dayOfWeek"
            ),
            // 按月统计未配置day
            Arguments.of(
                new IndicatorPeriodConfig<>(
                    new MonthlyStatRange(1, LocalTime.MIN, "31", LocalTime.MAX),
                    new TriggerConfig(0, null, null, LocalTime.MIN)
                ),
                "Monthly/Interval统计周期必须配置day"
            )
        );
    }

    //------------------------ calculateStatPeriod测试 ------------------------
    @Test
    void testCalculateStatPeriodWithOffset() {
        // 季度统计
        LocalDateTime baseTime = LocalDateTime.of(2025, 5, 28, 16, 0);
        IndicatorPeriodConfig<DailyStatRange> config = new IndicatorPeriodConfig<>(
            new DailyStatRange(LocalTime.of(16, 0), LocalTime.of(16, 0)),
            new TriggerConfig(null, null, null, LocalTime.of(16, 0))
        );

        StatisticPeriod result = IndicatorPeriodConverter.calculateStatPeriod(baseTime, config);
        assertEquals(LocalDateTime.of(2025, 5, 27, 16, 0), result.getStartTime());
        assertEquals(LocalDateTime.of(2025, 5, 28, 16, 0, 0), result.getEndTime());
    }

    @ParameterizedTest
    @MethodSource("provideCalculateStatPeriodCases")
    void testCalculateStatPeriod_normal(LocalDateTime baseTime, IndicatorPeriodConfig<?> config,
        LocalDateTime expectedStart, LocalDateTime expectedEnd) {
        // 测试正常的统计周期计算
        StatisticPeriod result = IndicatorPeriodConverter.calculateStatPeriod(baseTime, config);
        assertEquals(expectedStart, result.getStartTime());
        assertEquals(expectedEnd, result.getEndTime());
    }

    private static Stream<Arguments> provideCalculateStatPeriodCases() {
        return Stream.of(
            // ------------ 正常周期 --------------
            // 日统计：统计前一天范围
            Arguments.of(
                LocalDateTime.of(2025, 5, 20, 12, 30), // 触发时间
                new IndicatorPeriodConfig<>(
                    new DailyStatRange(LocalTime.MIN, LocalTime.MAX),
                    new TriggerConfig(null, null, null, LocalTime.of(12, 30)) // 每天12:30点触发
                ),
                LocalDateTime.of(2025, 5, 19, 0, 0),  // 预期开始（前一天）
                LocalDateTime.of(2025, 5, 20, 0, 0, 0) // 预期结束
            ),
            // 周统计：配置为统计上周四至本周三
            Arguments.of(
                LocalDateTime.of(2025, 5, 22, 8, 0), // 周四触发
                new IndicatorPeriodConfig<>(
                    new WeeklyStatRange(DayOfWeek.THURSDAY, LocalTime.MIN, DayOfWeek.WEDNESDAY, LocalTime.MAX),
                    new TriggerConfig(null, null, DayOfWeek.THURSDAY, LocalTime.of(8, 0))
                ),
                LocalDateTime.of(2025, 5, 15, 0, 0),  // 上周四
                LocalDateTime.of(2025, 5, 22, 0, 0, 0)  // 本周三
            ),
            // 月统计：固定日期（上月1号至最后一天）
            Arguments.of(
                LocalDateTime.of(2025, 5, 5, 12, 0), // 5月触发
                new IndicatorPeriodConfig<>(
                    new MonthlyStatRange(1, LocalTime.MIN, LAST_DAY, LocalTime.MAX),
                    new TriggerConfig(null, "5", null, LocalTime.of(9, 0)) // 每月5号触发
                ),
                LocalDateTime.of(2025, 4, 1, 0, 0),  // 上月1号
                LocalDateTime.of(2025, 5, 1, 0, 0, 0)  // 上月最后一天
            ),
            // 季度统计（1-3月），4月10日触发
            Arguments.of(
                LocalDateTime.of(2025, 4, 10, 9, 0),
                new IndicatorPeriodConfig<>(
                    new IntervalStatRange(1, 1, LocalTime.MIN, 3, LAST_DAY, LocalTime.MAX),
                    new TriggerConfig(1, "10", null, LocalTime.of(9, 0)) // 延迟1个月
                ),
                LocalDateTime.of(2025, 1, 1, 0, 0),
                LocalDateTime.of(2025, 4, 1, 0, 0, 0)
            ),
            // 跨年季度统计（11-1月）
            Arguments.of(
                LocalDateTime.of(2025, 2, 10, 9, 0),
                new IndicatorPeriodConfig<>(
                    new IntervalStatRange(11, 1, LocalTime.MIN, 3, LAST_DAY, LocalTime.MAX),
                    new TriggerConfig(1, "10", null, LocalTime.of(9, 0))
                ),
                LocalDateTime.of(2024, 11, 1, 0, 0),
                LocalDateTime.of(2025, 2, 1, 0, 0, 0)
            ),
            // 半年统计（1-6月）
            Arguments.of(
                LocalDateTime.of(2025, 3, 10, 9, 0),
                new IndicatorPeriodConfig<>(
                    new IntervalStatRange(1, 1, LocalTime.MIN, 6, LAST_DAY, LocalTime.MAX),
                    new TriggerConfig(1, "10", null, LocalTime.of(9, 0))
                ),
                LocalDateTime.of(2024, 7, 1, 0, 0),
                LocalDateTime.of(2025, 1, 1, 0, 0, 0)
            ),
            // 年统计（1-12月）
            Arguments.of(
                LocalDateTime.of(2025, 5, 10, 9, 0),
                new IndicatorPeriodConfig<>(
                    new IntervalStatRange(1, 1, LocalTime.MIN, 12, LAST_DAY, LocalTime.MAX),
                    new TriggerConfig(1, "10", null, LocalTime.of(9, 0))
                ),
                LocalDateTime.of(2024, 1, 1, 0, 0),
                LocalDateTime.of(2025, 1, 1, 0, 0, 0)
            ),

            // ------------ 自定义周期 --------------
            // 周统计：
            Arguments.of(
                LocalDateTime.of(2025, 5, 22, 16, 0), // 周四触发
                new IndicatorPeriodConfig<>(
                    new WeeklyStatRange(DayOfWeek.THURSDAY, LocalTime.of(16, 0), DayOfWeek.THURSDAY,
                        LocalTime.of(16, 0)),
                    new TriggerConfig(null, null, DayOfWeek.THURSDAY, LocalTime.of(16, 0))
                ),
                LocalDateTime.of(2025, 5, 15, 16, 0),  // 上周四
                LocalDateTime.of(2025, 5, 22, 16, 0, 0)  // 本周四
            ),
            // 月统计：固定日期
            Arguments.of(
                LocalDateTime.of(2025, 5, 21, 16, 0),
                new IndicatorPeriodConfig<>(
                    new MonthlyStatRange(21, LocalTime.of(16, 0), "21", LocalTime.of(16, 0)),
                    new TriggerConfig(null, "21", null, LocalTime.of(16, 0))
                ),
                LocalDateTime.of(2025, 4, 21, 16, 0),
                LocalDateTime.of(2025, 5, 21, 16, 0, 0)
            ),
            // 季度统计
            Arguments.of(
                LocalDateTime.of(2025, 3, 21, 16, 0),
                new IndicatorPeriodConfig<>(
                    new IntervalStatRange(12, 21, LocalTime.of(16, 0), 3, "21", LocalTime.of(16, 0)),
                    new TriggerConfig(0, "21", null, LocalTime.of(16, 0)) // 延迟1个月
                ),
                LocalDateTime.of(2024, 12, 21, 16, 0),
                LocalDateTime.of(2025, 3, 21, 16, 0, 0)
            ),
            // 跨年季度统计
            Arguments.of(
                LocalDateTime.of(2025, 2, 10, 9, 0),
                new IndicatorPeriodConfig<>(
                    new IntervalStatRange(11, 1, LocalTime.MIN, 3, LAST_DAY, LocalTime.MAX),
                    new TriggerConfig(1, "10", null, LocalTime.of(9, 0))
                ),
                LocalDateTime.of(2024, 11, 1, 0, 0),
                LocalDateTime.of(2025, 2, 1, 0, 0, 0)
            ),
            // 半年统计）
            Arguments.of(
                LocalDateTime.of(2025, 6, 21, 16, 0),
                new IndicatorPeriodConfig<>(
                    new IntervalStatRange(12, 21, LocalTime.of(16, 0), 6, "21", LocalTime.of(16, 0)),
                    new TriggerConfig(0, "21", null, LocalTime.of(16, 0))
                ),
                LocalDateTime.of(2024, 12, 21, 16, 0),
                LocalDateTime.of(2025, 6, 21, 16, 0, 0)
            ),
            // 年统计
            Arguments.of(
                LocalDateTime.of(2025, 12, 21, 16, 0),
                new IndicatorPeriodConfig<>(
                    new IntervalStatRange(12, 21, LocalTime.of(16, 0), 12, "21", LocalTime.of(16, 0)),
                    new TriggerConfig(0, "21", null, LocalTime.of(16, 0))
                ),
                LocalDateTime.of(2024, 12, 21, 16, 0),
                LocalDateTime.of(2025, 12, 21, 16, 0, 0)
            ),

            // ------------ 触发时间早于周期定义的结束时间 --------------
            // 日统计：触发时间在周期结束前
            Arguments.of(
                LocalDateTime.of(2025, 5, 20, 15, 0), // 触发时间
                new IndicatorPeriodConfig<>(
                    new DailyStatRange(LocalTime.of(16, 0), LocalTime.of(16, 0)),
                    new TriggerConfig(null, null, null, LocalTime.of(16, 0)) // 每天12:30点触发
                ),
                LocalDateTime.of(2025, 5, 18, 16, 0),  // 上一天
                LocalDateTime.of(2025, 5, 19, 16, 0, 0) // 当天
            ),
            // 周统计：
            Arguments.of(
                LocalDateTime.of(2025, 5, 21, 15, 0), // 周三触发
                new IndicatorPeriodConfig<>(
                    new WeeklyStatRange(DayOfWeek.THURSDAY, LocalTime.of(16, 0), DayOfWeek.THURSDAY,
                        LocalTime.of(16, 0)),
                    new TriggerConfig(null, null, DayOfWeek.THURSDAY, LocalTime.of(16, 0))
                ),
                LocalDateTime.of(2025, 5, 8, 16, 0),  // 上上周四
                LocalDateTime.of(2025, 5, 15, 16, 0, 0)  // 上周四
            ),
            // 月统计：固定日期
            Arguments.of(
                LocalDateTime.of(2025, 5, 20, 15, 0),
                new IndicatorPeriodConfig<>(
                    new MonthlyStatRange(21, LocalTime.of(16, 0), "21", LocalTime.of(16, 0)),
                    new TriggerConfig(null, "21", null, LocalTime.of(16, 0))
                ),
                LocalDateTime.of(2025, 3, 21, 16, 0),
                LocalDateTime.of(2025, 4, 21, 16, 0, 0)
            ),
            // 季度统计
            Arguments.of(
                LocalDateTime.of(2025, 3, 20, 15, 0),
                new IndicatorPeriodConfig<>(
                    new IntervalStatRange(12, 21, LocalTime.of(16, 0), 3, "21", LocalTime.of(16, 0)),
                    new TriggerConfig(0, "21", null, LocalTime.of(16, 0))
                ),
                LocalDateTime.of(2024, 9, 21, 16, 0),
                LocalDateTime.of(2024, 12, 21, 16, 0, 0)
            ),
            // 跨年季度统计
            Arguments.of(
                LocalDateTime.of(2024, 12, 10, 9, 0),
                new IndicatorPeriodConfig<>(
                    new IntervalStatRange(11, 1, LocalTime.MIN, 3, LAST_DAY, LocalTime.MAX),
                    new TriggerConfig(1, "10", null, LocalTime.of(9, 0))
                ),
                LocalDateTime.of(2024, 8, 1, 0, 0),
                LocalDateTime.of(2024, 11, 1, 0, 0, 0)
            ),
            // 半年统计）
            Arguments.of(
                LocalDateTime.of(2025, 6, 21, 15, 0),
                new IndicatorPeriodConfig<>(
                    new IntervalStatRange(12, 21, LocalTime.of(16, 0), 6, "21", LocalTime.of(16, 0)),
                    new TriggerConfig(0, "21", null, LocalTime.of(16, 0))
                ),
                LocalDateTime.of(2024, 6, 21, 16, 0),
                LocalDateTime.of(2024, 12, 21, 16, 0, 0)
            ),
            // 年统计
            Arguments.of(
                LocalDateTime.of(2025, 12, 21, 15, 0),
                new IndicatorPeriodConfig<>(
                    new IntervalStatRange(12, 21, LocalTime.of(16, 0), 12, "21", LocalTime.of(16, 0)),
                    new TriggerConfig(0, "21", null, LocalTime.of(16, 0))
                ),
                LocalDateTime.of(2023, 12, 21, 16, 0),
                LocalDateTime.of(2024, 12, 21, 16, 0, 0)
            )
        );
    }
}