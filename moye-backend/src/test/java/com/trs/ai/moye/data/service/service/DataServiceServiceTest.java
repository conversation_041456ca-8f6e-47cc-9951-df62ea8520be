package com.trs.ai.moye.data.service.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.trs.moye.base.common.response.PageResponse;
import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.ai.moye.data.service.config.AbilityCenterProperties;
import com.trs.ai.moye.data.service.dao.DataServiceCategoryMapper;
import com.trs.ai.moye.data.service.dao.DataServiceConfigMapper;
import com.trs.ai.moye.data.service.dao.DataServiceMapper;
import com.trs.ai.moye.data.service.dto.DataServiceDto;
import com.trs.ai.moye.data.service.entity.DataService;
import com.trs.ai.moye.data.service.entity.DataServiceCategoryTree;
import com.trs.ai.moye.data.service.entity.DataServiceConfig;
import com.trs.ai.moye.data.service.entity.params.CodeModeParams;
import com.trs.ai.moye.data.service.enums.ServiceConfigType;
import com.trs.ai.moye.data.service.enums.ServiceCreateMode;
import com.trs.ai.moye.data.service.enums.ServiceHealthStatus;
import com.trs.ai.moye.data.service.enums.ServicePublishStatus;
import com.trs.ai.moye.data.service.processer.process.DataServiceProcessorContext;
import com.trs.ai.moye.data.service.request.CheckNameDataServiceRequest;
import com.trs.ai.moye.data.service.request.CodeBlockRequest;
import com.trs.ai.moye.data.service.request.DataServicePreviewRequest;
import com.trs.ai.moye.data.service.request.DataServiceRequest;
import com.trs.ai.moye.data.service.response.DataServiceResponse;
import com.trs.ai.moye.data.service.response.ability.AbilityCheckUnpublishResponse;
import com.trs.ai.moye.data.service.response.ability.AbilityMsgResponse;
import com.trs.ai.moye.data.service.service.impl.DataServiceServiceImpl;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import com.trs.ai.moye.storageengine.response.StorageSearchResponse;
import com.trs.ai.moye.storageengine.service.StorageEngineService;
import com.trs.moye.base.common.request.PageParams;
import com.trs.moye.base.common.response.IdNameResponse;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockedStatic.Verification;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * 数据服务测试
 *
 * <AUTHOR>
 * @since 2024/10/15 18:42:44
 */
@ExtendWith(MockitoExtension.class)
class DataServiceServiceTest {

    @Mock
    private DataServiceMapper dataServiceMapper;

    @Mock
    private DataServiceConfigMapper dataServiceConfigMapper;

    @Mock
    private DataStorageMapper dataStorageMapper;

    @Mock
    private DataServiceAbilityCenterService dataServiceAbilityCenterService;

    @Mock
    private StorageEngineService storageEngineService;

    @Mock
    private DataServiceCategoryMapper dataServiceCategoryMapper;

    @Mock
    private DataServiceProcessorContext dataServiceProcessorContext;

    @Mock
    private AbilityCenterProperties abilityCenterProperties;

    @InjectMocks
    private DataServiceServiceImpl dataServiceService;

    @Mock
    private DynamicUserNameService dynamicUserNameService;

    @BeforeEach
    void setUp() {

    }


    @Test
    void getCategoryTree() {
        DataServiceCategoryTree category1 = new DataServiceCategoryTree();
        category1.setId(1);
        category1.setName("Category 1");

        DataServiceCategoryTree category2 = new DataServiceCategoryTree();
        category2.setId(2);
        category2.setName("Category 2");

        DataService dataService = buildDataService();
        dataService.setCategoryId(1);

        when(dataServiceCategoryMapper.selectCategoryTree()).thenReturn(List.of(category1, category2));
        when(dataServiceMapper.selectList(null)).thenReturn(List.of(dataService));

        List<DataServiceCategoryTree> result = dataServiceService.getCategoryTree();
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Category 1", result.get(0).getName());
        assertEquals("Category 2", result.get(1).getName());
        assertEquals(1, result.get(0).getChildren().size());

    }

    @Test
    void checkServiceName() {
        CheckNameDataServiceRequest mockDataService = new CheckNameDataServiceRequest();
        mockDataService.setName("Test Service");
        when(dataServiceService.checkServiceName(mockDataService)).thenReturn(true);
        boolean result = dataServiceService.checkServiceName(mockDataService);
        assertTrue(result);
    }

    @Test
    void addDataService() {
        DataService dataService = buildDataService();
        DataServiceRequest request = buildDataServiceRequest(dataService);
        // Arrange
        //when(dataServiceMapper.selectByCode(any())).thenReturn(null);

        // Act
        dataServiceService.addDataService(request);

        // Assert
        verify(dataServiceMapper).insert(request);
        verify(dataServiceConfigMapper).insert(request.getDataServiceConfig());

    }

    @Test
    void updateDataService() {
        DataService dataService = buildDataService();
        DataServiceRequest request = buildDataServiceRequest(dataService);
        // 更新成功
        when(dataServiceMapper.updateById(request)).thenReturn(1);
        // 返回 null 以触发插入
        when(dataServiceConfigMapper.selectOneByDataServiceId(1)).thenReturn(null);
        // 插入成功
        when(dataServiceConfigMapper.insert(request.getDataServiceConfig())).thenReturn(1);

        // Act
        dataServiceService.updateDataService(request);

        // Assert
        verify(dataServiceMapper).updateById(request);
        // 检查插入是否被调用
        verify(dataServiceConfigMapper).insert(request.getDataServiceConfig());
    }

    @Test
    void preview() {
        DataServicePreviewRequest request = new DataServicePreviewRequest();
        request.setCreateMode(ServiceCreateMode.GUIDE_MODE);
        request.setConnectionId(1);
        request.setStorageId(1);
        PageParams pageParams = new PageParams();
        pageParams.setPageNum(1);
        pageParams.setPageSize(20);
        request.setPageParams(pageParams);
        DataServiceConfig config = new DataServiceConfig();
        config.setDataServiceId(1);
        config.setType(ServiceConfigType.CODE);
        CodeModeParams params = new CodeModeParams();
        params.setType(ServiceConfigType.CODE);
        params.setCodeBlock("CODE_BLOCK");
        config.setParams(params);
        request.setDataServiceConfig(config);

        DataStorage storage = new DataStorage();
        storage.setId(1);
        storage.setEnName("test_storage");
        StorageSearchResponse storageSearchResponse = new StorageSearchResponse();
        storageSearchResponse.setItems(List.of(Map.of("name", "test_table")));
        storageSearchResponse.setTotal(1L);
        when(dataStorageMapper.selectById(request.getStorageId())).thenReturn(storage);
        when(dataServiceProcessorContext.queryData(any(), any(), any(), any())).thenReturn(storageSearchResponse);
        PageResponse<Map<String, Object>> preview = dataServiceService.preview(request);

        assertNotNull(preview);
        assertEquals(1, preview.getTotal());
        assertEquals(1, preview.getItems().size());
        assertEquals("test_table", preview.getItems().get(0).get("name"));
    }

    @Test
    void getDetail() {
        // Arrange
        Integer id = 1;
        MockedStatic<BeanUtil> mockedStatic = mockStatic(BeanUtil.class);
        mockedStatic.when((Verification) BeanUtil.getBean(DynamicUserNameService.class))
            .thenReturn(dynamicUserNameService);
        DataServiceDto dataServiceDto = new DataServiceDto(buildDataService());
        when(dataServiceMapper.selectDtoById(id)).thenReturn(dataServiceDto);

        // Act
        DataServiceResponse response = dataServiceService.getDetail(id);

        // Assert
        assertNotNull(response);
        assertEquals(dataServiceDto.getName(), response.getName());
        mockedStatic.close();
    }

    @Test
    void deleteDataService() {
        // Arrange
        Integer id = 1;
        List<Integer> ids = new ArrayList<>();
        ids.add(id);

        // Act
        dataServiceService.deleteDataService(ids);

        // Assert
        verify(dataServiceMapper).deleteByIds(ids);
        for (Integer i : ids) {
            verify(dataServiceConfigMapper).deleteByDataServiceId(i);
        }
    }

    @Test
    void publish() {
        // Arrange
        Integer id = 1;
        DataService dataService = buildDataService();
        DataServiceRequest request = buildDataServiceRequest(dataService);
        dataServiceService.addDataService(request);
        DataServiceDto dataServiceDto = new DataServiceDto(dataService);
        dataServiceDto.setDataServiceConfig(request.getDataServiceConfig());
        when(dataServiceMapper.selectDtoById(id)).thenReturn(dataServiceDto);
        when(dataServiceAbilityCenterService.getAppId()).thenReturn(1L);
        String invokeUrl = "/moye/out/service/%s/search-data";
        when(abilityCenterProperties.getInvokeServicePath()).thenReturn(invokeUrl);
        when(dataServiceMapper.selectDtoById(id)).thenReturn(dataServiceDto);
        when(dataServiceAbilityCenterService.publish(any())).thenReturn(
            new AbilityMsgResponse("新增能力成功！能力id为[1]"));

        // Act
        boolean result = dataServiceService.publish(id);

        // Assert
        assertTrue(result);
        verify(dataServiceMapper).updateById(dataServiceDto);
    }

    @Test
    void checkUnpublish() {
        Integer id = 1;
        DataService dataService = buildDataService();
        DataServiceRequest request = buildDataServiceRequest(dataService);
        dataServiceService.addDataService(request);
        DataServiceDto dataServiceDto = new DataServiceDto(dataService);
        dataServiceDto.setDataServiceConfig(request.getDataServiceConfig());
        dataServiceDto.setAbilityCenterId(1);
        when(dataServiceMapper.selectDtoById(id)).thenReturn(dataServiceDto);
        when(dataServiceAbilityCenterService.checkUnpublish(any(), any())).thenReturn(
            new AbilityCheckUnpublishResponse(true, ""));

        // Act
        AbilityCheckUnpublishResponse result = dataServiceService.checkUnpublish(id);

        // Assert
        assertNotNull(result);
        assertTrue(result.getCheckDelete());
    }

    @Test
    void unpublish() {
        // Arrange
        Integer id = 1;
        DataService dataService = buildDataService();
        DataServiceRequest request = buildDataServiceRequest(dataService);
        dataServiceService.addDataService(request);
        DataServiceDto dataServiceDto = new DataServiceDto(dataService);
        dataServiceDto.setDataServiceConfig(request.getDataServiceConfig());
        dataServiceDto.setAbilityCenterId(1);
        when(dataServiceMapper.selectDtoById(id)).thenReturn(dataServiceDto);
        when(dataServiceAbilityCenterService.checkAbilityExist(dataServiceDto.getAbilityCenterId())).thenReturn(true);
        when(dataServiceAbilityCenterService.unpublish(any())).thenReturn(new AbilityMsgResponse("操作成功！"));

        // Act
        boolean result = dataServiceService.unpublish(id);

        // Assert
        assertTrue(result);
        verify(dataServiceMapper).updatePublishStatusById(id, ServicePublishStatus.REVOKED);
    }

    @Test
    void republish() {
        // Arrange
        Integer id = 1;
        DataService dataService = buildDataService();
        DataServiceRequest request = buildDataServiceRequest(dataService);
        dataServiceService.addDataService(request);
        DataServiceDto dataServiceDto = new DataServiceDto(dataService);
        dataServiceDto.setDataServiceConfig(request.getDataServiceConfig());
        dataServiceDto.setAbilityCenterId(1);
        when(dataServiceMapper.selectDtoById(id)).thenReturn(dataServiceDto);
        String invokeUrl = "/moye/out/service/%s/search-data";
        when(abilityCenterProperties.getInvokeServicePath()).thenReturn(invokeUrl);
        when(dataServiceAbilityCenterService.checkAbilityExist(dataServiceDto.getAbilityCenterId())).thenReturn(true);
        when(dataServiceAbilityCenterService.republish(any())).thenReturn(new AbilityMsgResponse("操作成功！"));

        // Act
        boolean result = dataServiceService.republish(id);

        // Assert
        assertTrue(result);
        verify(dataServiceMapper).updatePublishStatusById(id, ServicePublishStatus.RELEASED);
    }

    @Test
    void getPublishStatus() {
        // Arrange
        Integer id = 1;
        DataService dataService = buildDataService();
        DataServiceRequest request = buildDataServiceRequest(dataService);
        dataServiceService.addDataService(request);
        DataServiceDto dataServiceDto = new DataServiceDto(dataService);
        dataServiceDto.setDataServiceConfig(request.getDataServiceConfig());
        dataServiceDto.setAbilityCenterId(1);
        when(dataServiceMapper.selectDtoById(id)).thenReturn(dataServiceDto);
        when(dataServiceAbilityCenterService.checkPublishStatus(any())).thenReturn(new HashMap<>() {{
            put(1, 2);
        }});

        // Act
        ServicePublishStatus result = dataServiceService.getPublishStatus(id);

        // Assert
        assertNotNull(result);
        assertEquals(ServicePublishStatus.RELEASED, result);
    }

    @Test
    void getRefConditionList() {
        Integer id = 1;
        DataService dataService = buildDataService();
        dataService.setId(id);
        dataService.setStorageId(1);
        Integer id2 = 2;
        DataService dataService2 = buildDataService();
        dataService2.setId(id2);
        dataService2.setStorageId(2);
        when(dataServiceMapper.selectDtoByStorageIds(any())).thenReturn(
            List.of(new DataServiceDto(dataService), new DataServiceDto(dataService2)));

        List<IdNameResponse> result = dataServiceService.getRefConditionList(1, 1);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(2, result.get(0).getId());
    }

    @Test
    void getTips() {
        String tips = dataServiceService.getTips(ConnectionType.MYSQL);
        assertNotNull(tips);
    }

    @Test
    void analyseCode() {
        CodeBlockRequest request = new CodeBlockRequest();
        request.setCodeBlock("select * from test where id = #{testId}");
        request.setDbType(ConnectionType.MYSQL);
        List<String> result = dataServiceService.analyseCode(request);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("testId", result.get(0));
    }

    @NotNull
    private static DataServiceRequest buildDataServiceRequest(DataService dataService) {
        DataServiceRequest dataServiceRequest = new DataServiceRequest(dataService);
        DataServiceConfig config = new DataServiceConfig();
        config.setDataServiceId(1);
        config.setType(ServiceConfigType.CODE);
        CodeModeParams params = new CodeModeParams();
        params.setType(ServiceConfigType.CODE);
        params.setCodeBlock("CODE_BLOCK");
        config.setParams(params);
        dataServiceRequest.setDataServiceConfig(config);
        return dataServiceRequest;
    }

    private static DataService buildDataService() {
        DataService dataService = new DataService();
        dataService.setId(1);
        dataService.setCode("test_code");
        dataService.setName("Test Service");
        dataService.setDescription("Description");
        dataService.setConnectionId(1);
        dataService.setCategoryId(1);
        dataService.setStorageId(1);
        dataService.setCreateMode(ServiceCreateMode.GUIDE_MODE);
        dataService.setPublishStatus(ServicePublishStatus.UNRELEASED);
        dataService.setHealthStatus(ServiceHealthStatus.HEALTHY);
        return dataService;
    }
}