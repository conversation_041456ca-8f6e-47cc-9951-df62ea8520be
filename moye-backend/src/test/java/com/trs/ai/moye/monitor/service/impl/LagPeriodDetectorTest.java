package com.trs.ai.moye.monitor.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.trs.ai.moye.monitor.entity.LagPeriod;
import com.trs.moye.base.monitor.entity.MonitorOdsLag;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;

class LagPeriodDetectorTest {

    private final LagPeriodDetector detector = new LagPeriodDetector();

    @Test
    void testUnclosedPeriod() {
        List<MonitorOdsLag> records = Arrays.asList(
            createRecord(1, LocalDateTime.of(2025, 3, 13, 10, 0), true, 100L),
            createRecord(1, LocalDateTime.of(2025, 3, 13, 11, 0), true, 200L),
            createRecord(1, LocalDateTime.of(2025, 3, 13, 12, 0), true, 300L)

        );

        List<LagPeriod> periods = detector.processRecords(records);
        assertEquals(1, periods.size());

        LagPeriod period = periods.get(0);
        assertEquals("2025-03-13T10:00", period.getStartTime().toString());
        assertEquals("2025-03-13T12:00", period.getEndTime().toString()); // 结束时间正确
        assertEquals(300, period.getPeakLag());
    }

    private MonitorOdsLag createRecord(int dataModelId, LocalDateTime monitorTime, boolean isLag, long lag) {
        MonitorOdsLag lagRecord = new MonitorOdsLag();
        lagRecord.setDataModelId(dataModelId);
        lagRecord.setMonitorTime(monitorTime);
        lagRecord.setIsLag(isLag);
        lagRecord.setLag(lag);
        return lagRecord;
    }

}