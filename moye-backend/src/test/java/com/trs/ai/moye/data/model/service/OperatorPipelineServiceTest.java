package com.trs.ai.moye.data.model.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.trs.ai.moye.data.ability.dao.AbilityMapper;
import com.trs.ai.moye.data.model.dao.operator.OperatorNewMapper;
import com.trs.ai.moye.data.model.dao.operator.OperatorPipelineDraftMapper;
import com.trs.ai.moye.data.model.dao.operator.OperatorPipelineMapper;
import com.trs.ai.moye.data.model.dto.arrangement.stream.OperatorPipelineDTO;
import com.trs.ai.moye.data.model.service.impl.ArrangementFieldService;
import com.trs.ai.moye.data.model.service.impl.OperatorPipelineServiceImpl;
import com.trs.moye.ability.base.storage.StorageAbility;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.Schema;
import com.trs.moye.ability.entity.operator.Operator;
import com.trs.moye.ability.entity.operator.OperatorCanvas;
import com.trs.moye.ability.entity.operator.OperatorPipeline;
import com.trs.moye.ability.entity.operator.OperatorRowType;
import com.trs.moye.ability.enums.AbilityFieldType;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * 算子编排测试
 *
 * <AUTHOR>
 * @since 2025/03/12 11:01:14
 */
@ExtendWith(MockitoExtension.class)
class OperatorPipelineServiceTest {

    @Mock
    private OperatorPipelineMapper operatorPipelineMapper;

    @Mock
    private OperatorNewMapper operatorNewMapper;

    @Mock
    private DataModelFieldMapper dataModelFieldMapper;

    @Mock
    private ArrangementFieldService arrangementFieldService;

    @Mock
    private DataStorageMapper datasetStorageMapper;

    @Mock
    private DataModelMapper dataModelMapper;

    @Mock
    private AbilityMapper abilityMapper;

    @InjectMocks
    private OperatorPipelineServiceImpl operatorPipelineService;

    @Mock
    private OperatorPipelineDraftMapper operatorPipelineDraftMapper;


    @Test
    void saveOperatorPipeline_ShouldSaveInputFieldsAndFieldMapping() {
        // Mock能力查询
        Ability mockAbility = new Ability();
        mockAbility.setId(1);
        mockAbility.setZhName("存储算子");
        Schema.ObjectTypeSchema outputSchema = new Schema.ObjectTypeSchema();
        outputSchema.setType(AbilityFieldType.OBJECT);
        outputSchema.setProperties(new HashMap<>());
        mockAbility.setOutputSchema(outputSchema);

        when(abilityMapper.selectByEnName(StorageAbility.SEATUNNEL_STORAGE_ABILITY)).thenReturn(mockAbility);

        // 捕获插入的流水线
        AtomicReference<OperatorPipeline> capturedPipeline = new AtomicReference<>();
        doAnswer(invocation -> {
            capturedPipeline.set(invocation.getArgument(0));
            return 1;
        }).when(operatorPipelineMapper).insert(any(OperatorPipeline.class));
        Mockito.doNothing().when(operatorPipelineDraftMapper).deleteByDataModelId(any(Integer.class));

        // 创建测试DTO，包含输入字段和字段映射
        OperatorPipelineDTO dto = createTestOperatorPipeline();

        // 执行测试
        operatorPipelineService.saveOperatorPipeline(dto.getDataModelId(), dto);

        // 验证
        verify(operatorPipelineMapper).insert(any(OperatorPipeline.class));

        // 验证inputFields和fieldMapping已正确保存
        assertThat(capturedPipeline.get().getInputFields()).isNotNull();
        assertThat(capturedPipeline.get().getInputFields()).hasSize(2);
        assertThat(capturedPipeline.get().getInputFields().get("name").getZhName()).isEqualTo("姓名");
        assertThat(capturedPipeline.get().getInputFields().get("age").getZhName()).isEqualTo("年龄");

        assertThat(capturedPipeline.get().getFieldMapping()).isNotNull();
        assertThat(capturedPipeline.get().getFieldMapping()).hasSize(2);
        assertThat(capturedPipeline.get().getFieldMapping().get("userName")).containsExactly("name");
        assertThat(capturedPipeline.get().getFieldMapping().get("userAge")).containsExactly("age");
    }


    @Test
    void getOperatorPipeline_ShouldReturnInputFieldsAndFieldMapping() {
        // 准备测试数据
        Integer dataModelId = 1;
        OperatorPipeline pipeline = new OperatorPipeline();
        pipeline.setId(1L);
        pipeline.setDataModelId(Long.valueOf(dataModelId));
        pipeline.setCanvas(new OperatorCanvas());

        // 添加inputFields

        Schema.BaseTypeSchema nameSchema = new Schema.BaseTypeSchema();
        nameSchema.setEnName("name");
        nameSchema.setZhName("姓名");
        nameSchema.setType(AbilityFieldType.STRING);
        OperatorRowType inputFields = new OperatorRowType();
        inputFields.put("name", nameSchema);
        pipeline.setInputFields(inputFields);

        // 添加fieldMapping
        Map<String, List<String>> fieldMapping = new HashMap<>();
        fieldMapping.put("userName", List.of("name"));
        pipeline.setFieldMapping(fieldMapping);

        List<Operator> operators = new ArrayList<>();
        Operator operator = new Operator();
        operator.setId(1);
        operator.setAbility(new Ability());
        operators.add(operator);

        // Mock 行为
        when(operatorPipelineMapper.selectByDataModelId(dataModelId)).thenReturn(pipeline);
        when(operatorNewMapper.selectByPipelineId(pipeline.getId())).thenReturn(operators);

        // 执行测试
        OperatorPipelineDTO result = operatorPipelineService.getOperatorPipeline(dataModelId);

        // 验证
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(pipeline.getId());
        assertThat(result.getDataModelId()).isEqualTo(dataModelId);
        assertThat(result.getOperators()).hasSize(1);

        // 验证inputFields和fieldMapping
        assertThat(result.getInputFields()).isNotNull();
        assertThat(result.getInputFields()).hasSize(1);
        assertThat(result.getInputFields().get("name").getZhName()).isEqualTo("姓名");

        assertThat(result.getFieldMapping()).isNotNull();
        assertThat(result.getFieldMapping()).hasSize(1);
        assertThat(result.getFieldMapping().get("userName")).containsExactly("name");
    }

    /**
     * 创建OperatorPipelineDTO
     *
     * @return {@link OperatorPipelineDTO }
     */
    private OperatorPipelineDTO createTestOperatorPipeline() {
        String operatorDto = """
            {
                "dataModelId": 1,
                "canvas": {
                    "width": 1000,
                    "height": 600,
                    "top": 0,
                    "left": 0,
                    "operatorViewInfo": [
                        {
                            "type": "table",
                            "dataModelId": 1,
                            "enabled": true,
                            "desc": "贴源表1",
                            "style": {
                                "width": 200,
                                "height": 100,
                                "top": 100,
                                "left": 100
                            },
                            "targetIds": [1000002],
                            "displayId": 1000001,
                            "order": 1,
                            "tableType": "ODS"
                        }
                    ]
                },
                "inputFields": {
                    "name": {
                        "enName": "name",
                        "zhName": "姓名",
                        "type": "STRING",
                        "description": "用户姓名"
                    },
                    "age": {
                        "enName": "age",
                        "zhName": "年龄",
                        "type": "INT",
                        "description": "用户年龄"
                    }
                },
                "fieldMapping": {
                    "userName": ["name"],
                    "userAge": ["age"]
                },
                "operators":[]
            }
            """;
        return JsonUtils.parseObject(operatorDto, OperatorPipelineDTO.class);
    }

    @Test
    void getOperatorPipeline_WhenPipelineExists_ShouldReturnDTO() {
        // 准备测试数据
        Integer dataModelId = 1;
        OperatorPipeline pipeline = new OperatorPipeline();
        pipeline.setId(1L);
        pipeline.setDataModelId(Long.valueOf(dataModelId));
        pipeline.setCanvas(new OperatorCanvas());

        List<Operator> operators = new ArrayList<>();
        Operator operator = new Operator();
        operator.setId(1);
        operator.setAbility(new Ability());
        operators.add(operator);

        // Mock 行为
        when(operatorPipelineMapper.selectByDataModelId(dataModelId)).thenReturn(pipeline);
        when(operatorNewMapper.selectByPipelineId(pipeline.getId())).thenReturn(operators);

        // 执行测试
        OperatorPipelineDTO result = operatorPipelineService.getOperatorPipeline(dataModelId);

        // 验证
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(pipeline.getId());
        assertThat(result.getDataModelId()).isEqualTo(dataModelId);
        assertThat(result.getOperators()).hasSize(1);
    }

    @Test
    void getOperatorPipeline_WhenPipelineNotExists_ShouldReturnEmptyDTO() {
        // 准备测试数据
        int dataModelId = 999;

        // Mock 行为
        when(operatorPipelineMapper.selectByDataModelId(dataModelId)).thenReturn(null);

        // 执行测试
        OperatorPipelineDTO result = operatorPipelineService.getOperatorPipeline(dataModelId);

        // 验证
        assertThat(result).isNotNull();
        assertThat(result.getDataModelId()).isNull();
    }

    @Test
    void deleteOperatorPipeline_WhenPipelineExists_ShouldReturnTrue() {
        // 准备测试数据
        int dataModelId = 1;
        OperatorPipeline pipeline = new OperatorPipeline();
        pipeline.setId(1L);

        // Mock 行为
        when(operatorPipelineMapper.selectByDataModelId(dataModelId)).thenReturn(pipeline);
        doNothing().when(operatorNewMapper).deleteByPipelineId(pipeline.getId());
        when(operatorPipelineMapper.deleteById(pipeline.getId())).thenReturn(1);

        // 执行测试
        boolean result = operatorPipelineService.deleteOperatorPipeline(dataModelId);

        // 验证
        assertThat(result).isTrue();
        verify(operatorNewMapper, times(1)).deleteByPipelineId(pipeline.getId());
        verify(operatorPipelineMapper, times(1)).deleteById(pipeline.getId());
    }

    @Test
    void deleteOperatorPipeline_WhenPipelineNotExists_ShouldReturnFalse() {
        // 准备测试数据
        int dataModelId = 999;

        // Mock 行为
        when(operatorPipelineMapper.selectByDataModelId(dataModelId)).thenReturn(null);

        // 执行测试
        boolean result = operatorPipelineService.deleteOperatorPipeline(dataModelId);

        // 验证
        assertThat(result).isFalse();
    }


}