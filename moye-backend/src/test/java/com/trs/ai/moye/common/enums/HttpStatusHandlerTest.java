package com.trs.ai.moye.common.enums;

import com.trs.moye.base.common.constants.ErrorMessages;
import com.trs.moye.base.common.exception.GlobalFeignException;
import feign.Request;
import feign.RequestTemplate;
import feign.Response;
import java.util.Collections;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;

class HttpStatusHandlerTest {


    private Response createMockResponse(HttpStatus status, String url) {
        RequestTemplate requestTemplate = new RequestTemplate();
        requestTemplate.method(Request.HttpMethod.GET);
        requestTemplate.uri(url);
        Request request = requestTemplate.resolve(Collections.emptyMap()).request();
        return Response.builder()
            .status(status.value())
            .reason(status.getReasonPhrase())
            .request(request)
            .build();
    }

    @Test
    void testBadRequestHandler() {
        Response response = createMockResponse(HttpStatus.BAD_REQUEST, "/example.com/test1");
        GlobalFeignException exception = HttpStatusHandler.BAD_REQUEST.createException("testMethod", response);
        Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(), exception.getCode());
        Assertions.assertTrue(exception.getMessage().contains(ErrorMessages.BAD_REQUEST));
    }

    @Test
    void testNotFoundHandler() {
        Response response = createMockResponse(HttpStatus.NOT_FOUND, "example.com/test2");
        GlobalFeignException exception = HttpStatusHandler.NOT_FOUND.createException("testMethod", response);
        Assertions.assertEquals(HttpStatus.NOT_FOUND.value(), exception.getCode());
        Assertions.assertTrue(exception.getMessage().contains(ErrorMessages.NOT_FOUND));
    }

    @Test
    void testBadGatewayHandler() {
        Response response = createMockResponse(HttpStatus.BAD_GATEWAY, "example.com/test3");
        GlobalFeignException exception = HttpStatusHandler.BAD_GATEWAY.createException("testMethod", response);
        Assertions.assertEquals(HttpStatus.BAD_GATEWAY.value(), exception.getCode());
        Assertions.assertTrue(exception.getMessage().contains(ErrorMessages.BAD_GATEWAY));
    }

    @Test
    void testUnauthorizedHandler() {
        Response response = createMockResponse(HttpStatus.UNAUTHORIZED, "example.com/test4");
        GlobalFeignException exception = HttpStatusHandler.UNAUTHORIZED.createException("testMethod", response);
        Assertions.assertEquals(HttpStatus.UNAUTHORIZED.value(), exception.getCode());
        Assertions.assertTrue(exception.getMessage().contains(ErrorMessages.AUTHORIZATION_ERROR));
    }

    @Test
    void testDefaultHandler() {
        Response response = createMockResponse(HttpStatus.INTERNAL_SERVER_ERROR, "example.com/test5");
        GlobalFeignException exception = HttpStatusHandler.DEFAULT.createException("testMethod", response);
        Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), exception.getCode());
        Assertions.assertTrue(exception.getMessage().contains(ErrorMessages.SYSTEM_ERROR));
    }
}