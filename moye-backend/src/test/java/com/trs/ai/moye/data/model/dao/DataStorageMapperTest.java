package com.trs.ai.moye.data.model.dao;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.ai.moye.data.model.dto.DataStorageWithModelAndCategoryDTO;
import com.trs.moye.base.data.model.dao.BusinessCategoryMapper;
import com.trs.moye.base.data.model.entity.BusinessCategory;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.enums.CreateModeEnum;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.request.SearchParams;
import java.util.List;
import javax.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class DataStorageMapperTest {

    @Resource
    private DataStorageDisplayMapper dataStorageDisplayMapper;
    @Resource
    private DataStorageMapper dataStorageMapper;
    @Resource
    private BusinessCategoryMapper businessCategoryMapper;
    @Resource
    private DataModelMapper dataModelMapper;
    @Resource
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        jdbcTemplate.execute("TRUNCATE TABLE data_storage");
        jdbcTemplate.execute("TRUNCATE TABLE data_model");
        jdbcTemplate.execute("TRUNCATE TABLE business_category");
    }

    @Test
    void selectPagedByConnectionId() {
        // 业务分类
        int businessCategoryId = businessCategoryMapper.insert(
            new BusinessCategory("业务分类", "business category", "业务分类描述"));
        // 数据建模
        DataModel dataModel = new DataModel();
        dataModel.setBusinessCategoryId(businessCategoryId);
        dataModel.setLayer(ModelLayer.ODS);
        dataModel.setEnName("data model");
        dataModel.setZhName("数据建模");
        dataModel.setCreateMode(CreateModeEnum.NORMAL);
        dataModel.setExecuteStatus(ModelExecuteStatus.STOP);
        int dataModelId = dataModelMapper.insert(dataModel);
        // 数据存储
        DataStorage dataStorage = new DataStorage();
        dataStorage.setConnectionId(1);
        dataStorage.setEnName("data_storage");
        dataStorage.setZhName("数据存储表");
        dataStorage.setDataModelId(dataModelId);
        dataStorageMapper.insert(dataStorage);

        // 模糊查询 comment
        SearchParams searchParams = new SearchParams();
        searchParams.setFields(List.of("name"));
        searchParams.setKeyword("数据存储表");
        Page<DataStorageWithModelAndCategoryDTO> dtoPage = dataStorageDisplayMapper.selectPagedByConnectionId(1,
            searchParams,
            Page.of(1, 5));

        assertNotNull(dtoPage);

        // 模糊查询 name
        searchParams.setFields(List.of("name"));
        searchParams.setKeyword("data_storage");
        dtoPage = dataStorageDisplayMapper.selectPagedByConnectionId(1, searchParams,
            Page.of(1, 5));

        assertNotNull(dtoPage);
    }
}