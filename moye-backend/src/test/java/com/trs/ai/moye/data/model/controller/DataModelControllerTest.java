package com.trs.ai.moye.data.model.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.trs.ai.moye.common.web.response.CustomerResponseBodyConvert;
import com.trs.ai.moye.common.web.response.GlobalExceptionProcessor;
import com.trs.ai.moye.data.model.request.MonitorConfigVersionRequest;
import com.trs.ai.moye.data.standard.dao.MetaDataStandardMapper;
import com.trs.ai.moye.data.standard.entity.GraphicsMetaDataStandard;
import com.trs.ai.moye.data.standard.response.MetaDataStandardDetailResponse;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import com.trs.moye.base.common.annotaion.validation.SafeSqlField;
import com.trs.moye.base.common.enums.SortOrder;
import com.trs.moye.base.common.request.SortParams;
import com.trs.moye.base.common.response.ResponseMessage;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.standard.enums.MetaDataStandardTypeEnum;
import com.trs.moye.base.monitor.enums.MonitorConfigType;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(MockitoExtension.class)
class DataModelControllerTest {

    @InjectMocks
    private DataModelController dataModelController;

    private MockMvc mockMvc;
    @Mock
    private MetaDataStandardMapper metaDataStandardMapper;

    @Mock
    private DynamicUserNameService dynamicUserNameService;

    @BeforeEach
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(dataModelController)
            .setControllerAdvice(new CustomerResponseBodyConvert(), new GlobalExceptionProcessor())
            .setMessageConverters(new MappingJackson2HttpMessageConverter(JsonUtils.OBJECT_MAPPER))
            .build();
    }

    @Test
    void modelMetaDataStandard() throws Exception {
        MetaDataStandardDetailResponse expected = new MetaDataStandardDetailResponse();
        expected.setEnName("meta_data_standard");
        expected.setZhName("yuan shu ju standard");
        expected.setType(MetaDataStandardTypeEnum.GRAPHICS);
        expected.setVidType("string");

        GraphicsMetaDataStandard metaDataStandard = new GraphicsMetaDataStandard();
        metaDataStandard.setEnName(expected.getEnName());
        metaDataStandard.setZhName(expected.getZhName());
        metaDataStandard.setType(expected.getType());
        metaDataStandard.setVidType(expected.getVidType());
        when(metaDataStandardMapper.selectByDataModelId(any(Integer.class))).thenReturn(metaDataStandard);
        when(dynamicUserNameService.getUserName(any())).thenReturn("test");
        MetaDataStandardDetailResponse response = MetaDataStandardDetailResponse.from(metaDataStandard,
            dynamicUserNameService);
        String expect = JsonUtils.toJsonString(ResponseMessage.ok(response));
        mockMvc.perform(get("/data-model/1/meta-data-standard"))
            .andExpect(status().isOk())
            .andExpect(content().string(expect));
    }

    @Test
    void modelWithoutMetaDataStandard() throws Exception {
        when(metaDataStandardMapper.selectByDataModelId(any(Integer.class))).thenReturn(null);
        mockMvc.perform(get("/data-model/1/meta-data-standard"))
            .andExpect(status().isOk())
            .andExpect(content().string("{\"code\":200,\"success\":true}"));
    }


    /**
     * {@link DataModelController#monitorTypeVersionPageList(Integer, MonitorConfigVersionRequest)}
     */
    @Test
    void monitorTypeVersionPageList_sqlInjection() throws Exception {
        // 构造请求体，测试sql注入
        Integer dataModelId = 1;
        MonitorConfigVersionRequest request = new MonitorConfigVersionRequest();
        request.setMonitorType(MonitorConfigType.LAG);
        request.setSortParams(new SortParams("update_time%27%3B", SortOrder.ASC));
        String requestBody = JsonUtils.toJsonString(request);

        // 执行 POST 请求
        String responseContent = mockMvc.perform(
                post(String.format("/data-model/%s/monitor-config/version/page-list", dataModelId))
                    .contentType("application/json")
                    .content(requestBody))
            .andExpect(status().isInternalServerError())
            .andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);

        // 解析并验证响应
        ResponseMessage responseMessage = JsonUtils.parseObject(responseContent, ResponseMessage.class);
        assertNotNull(responseMessage);
        // 获取注解的默认值
        Method messageMethod = SafeSqlField.class.getMethod("message");
        String defaultValue = (String) messageMethod.getDefaultValue();
        assertEquals(defaultValue, responseMessage.getMessage());
    }
}