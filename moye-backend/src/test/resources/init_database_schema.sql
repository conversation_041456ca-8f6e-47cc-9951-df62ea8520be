-- 数据连接
CREATE TABLE IF NOT EXISTS data_connection
(
    id                BIGINT AUTO_INCREMENT PRIMARY KEY,
    name              VA<PERSON>HAR(255) NOT NULL,
    connection_type   VARCHAR(50)  NOT NULL,
    connection_params text,
    is_source         TINYINT      NOT NULL,
    is_test_success   TINYINT      NOT NULL,
    certificate_id    BIGINT,
    create_time       TIMESTAMP,
    create_by         VARCHAR(255),
    update_time       TIMESTAMP,
    update_by         VA<PERSON>HAR(255)
);

-- 数据存储
CREATE TABLE IF NOT EXISTS data_storage
(
    `id`                  BIGINT AUTO_INCREMENT PRIMARY KEY,
    `en_name`             varchar(255),
    `zh_name`             varchar(255),
    `data_model_id`       bigint,
    `connection_id`       bigint,
    `create_table_status` varchar(20),
    `field_ids`           varchar(1000),
    `create_by`           bigint,
    `create_time`         datetime,
    `update_by`           bigint,
    `update_time`         datetime
);

-- 数据建模
CREATE TABLE IF NOT EXISTS `data_model`
(
    `id`                    BIGINT AUTO_INCREMENT PRIMARY KEY,
    `data_source_id`        bigint,
    `create_mode`           varchar(20),
    `execute_status`        varchar(20),
    `zh_name`               varchar(255),
    `en_name`               varchar(255),
    `business_category_id`  int,
    `layer`                 varchar(20),
    `description`           varchar(255),
    `is_arranged`           tinyint,
    `is_sync_field`         tinyint,
    `meta_data_standard_id` bigint,
    `create_by`             tinyint,
    `create_time`           datetime,
    `update_by`             tinyint,
    `update_time`           datetime
);

-- 数据建模字段
CREATE TABLE IF NOT EXISTS `data_model_field`
(
    `id`              bigint AUTO_INCREMENT PRIMARY KEY,
    `data_model_id`   bigint,
    `zh_name`         varchar(100),
    `en_name`         varchar(100),
    `type`            varchar(100),
    `type_name`       varchar(100),
    `description`     text,
    `is_use_standard` tinyint,
    `is_multi_value`  tinyint,
    `is_statistic`    tinyint,
    `is_nullable`     tinyint,
    `is_built_in`     tinyint,
    `is_primary_key`  tinyint,
    `is_increment`    tinyint,
    `is_partition`    tinyint,
    `advance_config`  text,
    `fields`          text,
    `create_by`       bigint,
    `create_time`     datetime,
    `update_by`       bigint,
    `update_time`     datetime,
    `default_value`   varchar(100),
    `is_title`        tinyint,
    `is_provide_service` tinyint,
    `is_foreign_key` tinyint DEFAULT 0 ,
    `foreign_key_config` JSON DEFAULT NULL
);

-- 业务分类
CREATE TABLE IF NOT EXISTS `business_category`
(
    `id`          BIGINT AUTO_INCREMENT PRIMARY KEY,
    `zh_name`     varchar(50),
    `en_name`     varchar(50),
    `description` text,
    `create_by`   int,
    `update_by`   int,
    `update_time` datetime,
    `create_time` datetime
);

CREATE TABLE IF NOT EXISTS auth_certificate_kerberos
(
    id          BIGINT AUTO_INCREMENT PRIMARY KEY,
    krb5_path   VARCHAR(255),
    principal   VARCHAR(255),
    keytab_path VARCHAR(255),
    create_time TIMESTAMP,
    create_by   VARCHAR(255),
    update_time TIMESTAMP,
    update_by   VARCHAR(255)
);

CREATE TABLE IF NOT EXISTS data_standard_field
(
    id             BIGINT AUTO_INCREMENT PRIMARY KEY,
    category_id    BIGINT       NOT NULL,
    zh_name        VARCHAR(100) NOT NULL,
    en_name        VARCHAR(100) NOT NULL,
    type           VARCHAR(100) NOT NULL,
    type_name      VARCHAR(100) NOT NULL,
    description    TEXT         NOT NULL,
    is_multi_value TINYINT      NOT NULL DEFAULT 0,
    is_nullable    TINYINT      NOT NULL DEFAULT 1,
    is_statistic   TINYINT      NOT NULL DEFAULT 0,
    is_built_in    TINYINT      NOT NULL,
    advance_config VARCHAR(1000)         DEFAULT NULL,
    create_by      BIGINT                DEFAULT NULL,
    create_time    DATETIME              DEFAULT NULL,
    update_by      BIGINT                DEFAULT NULL,
    update_time    DATETIME              DEFAULT NULL,
    default_value  VARCHAR(100)          DEFAULT NULL
);

-- 元数据标准
CREATE TABLE IF NOT EXISTS meta_data_standard
(
    id                            int auto_increment
        primary key,
    zh_name                       varchar(50) not null,
    en_name                       varchar(50) not null,
    type                          varchar(20) not null,
    description                   text        null,
    meta_data_standard_catalog_id int         not null,
    vid_type                      varchar(50) null,
    create_by                     varchar(50) null,
    create_time                   datetime    null,
    update_by                     varchar(50) null,
    update_time                   datetime    null
);

-- 元数据标准字段
CREATE TABLE IF NOT EXISTS meta_data_standard_field
(
    id                    int auto_increment
        primary key,
    meta_data_standard_id int         not null,
    zh_name               varchar(50) not null,
    en_name               varchar(50) not null,
    description           text        null,
    type                  varchar(20) not null,
    fields                longtext    not null,
    primary_key           varchar(30) null,
    create_by             varchar(50) null,
    create_time           varchar(50) null,
    update_by             varchar(50) null,
    update_time           varchar(50) null
);
-- 元数据标准目录
CREATE TABLE IF NOT EXISTS meta_data_standard_catalog
(
    id          int auto_increment comment '目录编号'
        primary key,
    name        varchar(50)   null comment '名称',
    description text          null comment '描述',
    pid         int default 0 null comment '目录父编号；第一级目录编号为0',
    create_by   varchar(50)   null,
    create_time datetime      null,
    update_by   varchar(50)   null,
    update_time datetime      null
);

-- 数据服务
CREATE TABLE IF NOT EXISTS data_service
(
    `id`                bigint       NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键id',
    `code`              varchar(50)  NOT NULL COMMENT '数据服务编码',
    `name`              varchar(255) NOT NULL,
    `description`       text     DEFAULT NULL COMMENT '描述',
    `connection_id`     bigint       NOT NULL COMMENT '数据连接id',
    `storage_id`        bigint   DEFAULT NULL COMMENT '数据存储id',
    `category_id`       bigint       NOT NULL COMMENT '分类id',
    `create_mode`       varchar(20)  NOT NULL COMMENT '服务模式：向导/代码',
    `publish_status`    varchar(20)  NOT NULL COMMENT '发布状态',
    `ability_center_id` int      DEFAULT NULL COMMENT '发布后在能力中心的id',
    `health_status`     varchar(20)  NOT NULL COMMENT '健康状态',
    `create_by`         int      DEFAULT NULL COMMENT '创建人',
    `update_by`         int      DEFAULT NULL COMMENT '更新人',
    `create_time`       datetime DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime DEFAULT NULL COMMENT '更新时间'
);
-- 数据服务分类
CREATE TABLE IF NOT EXISTS data_service_category
(
    `id`          bigint NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `name`        varchar(50) DEFAULT NULL COMMENT '数据服务分类名称',
    `create_by`   int         DEFAULT NULL COMMENT '创建人id',
    `update_by`   int         DEFAULT NULL COMMENT '更新人id',
    `create_time` datetime    DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime    DEFAULT NULL COMMENT '更新时间',
    `pid`         int         DEFAULT NULL COMMENT '父id',
    `description` text        DEFAULT NULL COMMENT '描述'
);

-- 数据服务配置
CREATE TABLE IF NOT EXISTS `data_service_config`
(
    `id`              bigint NOT NULL AUTO_INCREMENT COMMENT '配置id' PRIMARY KEY,
    `data_service_id` bigint      DEFAULT NULL COMMENT '数据服务id',
    `type`            varchar(20) DEFAULT NULL COMMENT '配置类型：查询、计数、更新、统计',
    `params`          text        DEFAULT NULL COMMENT '数据服务具体配置'
);

-- 能力
CREATE TABLE IF NOT EXISTS ability
(
    id                  BIGINT AUTO_INCREMENT PRIMARY KEY,
    en_name             VARCHAR(255) NOT NULL COMMENT '英文名',
    zh_name             VARCHAR(255) COMMENT '中文名',
    description         TEXT COMMENT '能力描述',
    type                VARCHAR(50)  NOT NULL COMMENT '能力类型',
    path                VARCHAR(255) NOT NULL COMMENT '能力路径',
    operator_category_id INT          NOT NULL DEFAULT 1 COMMENT '算子业务分类ID',
    icon_name           VARCHAR(50)  NOT NULL DEFAULT 'ability-default' COMMENT '图标名称',
    update_status       VARCHAR(50)  NOT NULL DEFAULT 'UPDATED' COMMENT '更新状态',
    test_status         VARCHAR(50)  NOT NULL DEFAULT 'UNTESTED' COMMENT '测试状态',
    http_request_config TEXT COMMENT 'HTTP请求配置',
    input_schema        TEXT         NOT NULL COMMENT '输入参数',
    output_schema       TEXT         NOT NULL COMMENT '输出参数',
    input_size          INT,
    output_size         INT,
    field_retention_mode VARCHAR(50) DEFAULT 'MERGE_AND_APPEND',
    is_batch_supported   TINYINT      NOT NULL DEFAULT 1 COMMENT '是否支持批处理：0-不支持，1-支持',
    is_batch_condition_supported TINYINT      NOT NULL DEFAULT 1 COMMENT '是否支持批处理条件：0-不支持，1-支持',
    create_by           INT COMMENT '创建人',
    update_by           INT COMMENT '更新人',
    create_time         DATETIME COMMENT '创建时间',
    update_time         DATETIME COMMENT '更新时间'
);

-- 批处理算子
CREATE TABLE IF NOT EXISTS `batch_operator` (
                                      `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY,
                                      `arrangement_id` int NOT NULL COMMENT '编排id',
                                      `data_model_id` int NOT NULL COMMENT '数据建模id',
                                      `canvas` text COMMENT '画布信息',
                                      `display_id` bigint COMMENT '算子编排中算子顺序（前端生成）',
                                      `target_display_ids` text COMMENT '子节点id',
                                      `parent_display_ids` text COMMENT '父节点id（order字段），json数组',
                                      `type` varchar(100) COMMENT '节点类型: TABLE/OPERATOR',
                                      `name` varchar(255) COMMENT '算子名称',
                                      `desc` text COMMENT '描述',
                                      `enabled` tinyint DEFAULT 1 COMMENT '是否启用',
                                      `conditions` text COMMENT '条件',
                                      `input_fields` text COMMENT '输入字段',
                                      `output_fields` text COMMENT '输出字段',
                                      `input_bind` text COMMENT '输入绑定',
                                      `output_bind` text COMMENT '输出绑定',
                                      `ability_id` int COMMENT '能力id',
                                      `table_type` VARCHAR(50) COMMENT 'TABLE类型算子的建模分层',
                                      `table_data_model_id` int COMMENT 'TABLE类型算子的数据建模id',
                                      `table_storage_id` int COMMENT 'TABLE类型算子的数据存储id',
                                      `table_is_increment` int COMMENT 'TABLE类型算子的是否启用增量',
                                      `output_table_name` varchar(500) COMMENT '输出表名',
                                      `create_by` int,
                                      `create_time` datetime,
                                      `update_by` int,
                                      `update_time` datetime
);

CREATE TABLE IF NOT EXISTS `api_log_trace` (
       `id` bigint COMMENT 'id',
       `log_id` bigint COMMENT '日志id',
       `node` text COMMENT '节点',
       `application_name` text COMMENT '应用名称',
       `create_timestamp` bigint COMMENT '创建时间戳',
       `execute_duration` bigint COMMENT '执行时长',
       `execute_result` text COMMENT '执行结果',
       `input_details` text COMMENT '输入详情',
       `output_details` text COMMENT '输出详情'
);
INSERT INTO api_log_trace (id, log_id, node, application_name, create_timestamp, execute_duration, execute_result, input_details, output_details) VALUES (491668757011234817, 491668757002846209, '开始进入http接口', 'moye', 1742291766435, 0, 'SUCCESS', '', '{}');
INSERT INTO api_log_trace (id, log_id, node, application_name, create_timestamp, execute_duration, execute_result, input_details, output_details) VALUES (491668757011234818, 491668757002846209, '查询数据连接', 'moye', 1742291766435, 1, 'SUCCESS', '{"连接id":76}', '{"连接名称":"ga","连接类型":"MYSQL"}');
INSERT INTO api_log_trace (id, log_id, node, application_name, create_timestamp, execute_duration, execute_result, input_details, output_details) VALUES (491668757015429120, 491668757002846209, '调用存储引擎查询数据', 'moye', 1742291766436, 20, 'SUCCESS', '', '{"调用结果":{"items":[{"日均违法犯罪警情数量":0.0}],"total":1}}');
INSERT INTO api_log_trace (id, log_id, node, application_name, create_timestamp, execute_duration, execute_result, input_details, output_details) VALUES (491668757179010049, 491668757002846209, '进入存储引擎-连接76', 'moye-storage-engine', 1742291766475, 2, 'SUCCESS', '', '{}');

CREATE TABLE IF NOT EXISTS `stream_exclusive_node_config`
(
    `id`                 int  NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    `data_models`        text NOT NULL COMMENT '数据建模列表：id和名称',
    `expect_node_count`  int  NOT NULL COMMENT '期望节点数量',
    `priority`           int  NOT NULL COMMENT '优先级',
    `create_by`          int      DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime DEFAULT NULL COMMENT '创建时间',
    `update_by`          int      DEFAULT NULL COMMENT '修改人',
    `update_time`        datetime DEFAULT NULL COMMENT '更新时间',
    `concurrent_threads` int      DEFAULT NULL COMMENT '并发线程数',
    `rate_limit`         int      DEFAULT NULL COMMENT '限流, 条/分钟'
);

CREATE TABLE IF NOT EXISTS `data_connection_monitor_config`
(
    `id`            INT AUTO_INCREMENT PRIMARY KEY,
    `connection_id` BIGINT NOT NULL COMMENT '数据连接ID',
    `enabled`       TINYINT DEFAULT 1 COMMENT '是否启用',
    `xxl_job_id`    INT COMMENT 'xxl-job任务ID',
    `create_by`     INT COMMENT '创建人',
    `create_time`   DATETIME COMMENT '创建时间',
    `update_by`     INT COMMENT '更新人',
    `update_time`   DATETIME COMMENT '更新时间'
);

-- clickhouse 组件监控表
CREATE TABLE IF NOT EXISTS basic_component_detection_statistics
(
    `id`                  INT PRIMARY KEY,
    `component_name`      VARCHAR COMMENT '组件名称',
    `error_count`         INT COMMENT '异常次数',
    `detection_count`     INT COMMENT '检测次数',
    `last_status`         INT COMMENT '最近一次检测状态',
    `last_error_time`     DateTime COMMENT '最近一次异常时间',
    `last_detection_time` DateTime COMMENT '最近一次检测时间',
    `last_error_message`  VARCHAR COMMENT '最近一次异常信息'
);
