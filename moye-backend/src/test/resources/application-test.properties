# H2 Database Configuration for tests
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;DATABASE_TO_UPPER=false
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:init_database_schema.sql
# MyBatis-Plus Configuration
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
#mybatis-plus.type-aliases-package=com.trs.ai.moye.data.connection.entity
# Logging
logging.level.com.trs.ai.moye=INFO
# Disable Flyway for tests
spring.flyway.enabled=false

server.port=9014
server.servlet.context-path=/moye
server.servlet.session.timeout=1d
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=1GB
spring.servlet.multipart.max-request-size=1GB

#登陆认证相关配置
com.trs.security.shiro-session-id=DEVSESSIONID
com.trs.security.enable-code=false
com.trs.security.login-fail-num-limit=5
com.trs.security.warn-update-pwd-days=90
com.trs.security.checksum-enable-config=false

#logging configuration
logging.file.path=./logs

#mybatis-plus configuration
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

#存储引擎
storage.engine.url=http://localhost:8084

#批处理引擎
batch.engine.url=http://localhost:10999
batch.engine.dag-execute-url=/batch-engine/task/dag/execute
ty.engine.url=http://trs-moye-engine-svc:9001/engine

#能力中心相关配置
com.trs.ability.base-url=http://***************:90
com.trs.ability.client-name=TRS_MOYE
com.trs.ability.authorization=MTk5MzU5MDM2MTI6YXM1NDYyMzE=
com.trs.ability.app-path=/athena/power/center/app/external/getByAppKey
com.trs.ability.publish-path=/athena/power/center/management/external/addOne
com.trs.ability.republish-path=/athena/power/center/management/external/rePublish
com.trs.ability.check-delete-path=/athena/power/center/management/external/checkDelete
com.trs.ability.unpublish-path=/athena/power/center/management/external/unPublish
com.trs.ability.publish-status-path=/athena/power/center/management/external/getPublishStatus
com.trs.ability.check-exist-path=/athena/power/center/management/external/exist
com.trs.ability.search-by-ability-name=/ability-app/#/ability/management?searchField=powerName&searchValue=
com.trs.ability.app-list-path=/athena/power/center/hall/external/appList
com.trs.ability.power-list-path=/athena/power/center/management/external/list
com.trs.ability.invoke-service-path=/moye/out/service/%s/search-data
com.trs.ability.power-version-path=/athena/power/center/common/external/powerVersion
com.trs.ability.parametric-variation-path=/athena/power/center/common/external/parametricVariation
#ty-engine
com.trs.ty.engine.ty-engine-base-url=http://trs-moye-engine-svc:8083
com.trs.ty.engine.operator-test-url=/engine/ability/test
ty.engine.retryRule=/operator/retry

xxl.job.admin.addresses=http://**************:8096/xxl-job-admin
xxl.job.login.userName=admin
xxl.job.login.password=123456
xxl.job.executor.log-path=./logs/xxl-job
xxl.job.executor.log-retention-days=1

etcd.node.url=**************:2389
etcd.user.name=root
etcd.user.password=wzwxbszt2021!@#

model.xxl-job.realtime-cron=0 */5 * * * ?

kerberos.dir.path=/Users/<USER>/Desktop/TRS
kerberos.dir.name=proof

#是否开启moye应用初始化Runner；本地环境默认不开启
moye.initializer.enable=false

#minio
com.trs.minio.endpoint=http://**************:59000
com.trs.minio.accessKey=admin
com.trs.minio.secretKey=minioadmin
com.trs.minio.bucket.logs=logs
com.trs.minio.bucket.yarn-log=yarn-logs
com.trs.minio.bucket.hive-log=hive-logs

spring.cache.type=simple

#推送服务能力中心服务名
com.trs.msg.send.ability.client.name=MOYE_MSG_SEND

redis.mode=standalone
redis.nodes=***************:6379
redis.password=!QAZ2wsx1234
redis.database=4
redis.timeout=

com.trs.security.jwt-secret-key=b506c5088ce617d73444fbabc5c2b030924b53baa1f88025eb6544745783f675
com.trs.security.token-expiration-millis=43200000

spring.redis.host=***************
spring.redis.port=6379
spring.redis.database=4
spring.redis.connect-timeout=5s
spring.redis.timeout=5s
spring.redis.password=!QAZ2wsx1234

ck.normal.data.retention.day=15
ck.error.data.retention.day=45

#spark配置
spark.driver-cores=2
spark.driver-memory=1G
spark.executor-memory=1G
spark.cores-max=4
spark.executor-cores=2

com.trs.arrangement.stream-engine-url=http://***************:8083/engine/ability/single
moye.merger.config.enable=false