spring.application.name=moye
spring.cloud.nacos.server-addr=nacos-svc:8848
spring.cloud.nacos.username=nacos
spring.cloud.nacos.password=nacos
spring.cloud.nacos.config.namespace=develop
spring.cloud.nacos.discovery.namespace=${spring.cloud.nacos.config.namespace}
spring.config.import[0]=nacos:moye.properties?refresh=true
spring.config.import[1]=nacos:mysql-moye-v4.properties
spring.config.import[2]=nacos:clickhouse.properties
spring.config.import[3]=nacos:xxl-job.properties
spring.config.import[4]=nacos:redis.properties
spring.config.import[5]=nacos:mysql-moye-v4-dynamic.properties
spring.config.import[6]=nacos:minio.properties
spring.config.import[7]=nacos:kafka.properties

logging.level.com.alibaba.cloud.nacos=debug
#
# clickhouse datasource config
#mariadb database configuration
#spring.datasource.dynamic.primary=mysql
#spring.datasource.dynamic.datasource.mysql.url=***************************************************************************************************************************************************************************
#spring.datasource.dynamic.datasource.mysql.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.datasource.dynamic.datasource.mysql.username=root
#spring.datasource.dynamic.datasource.mysql.password=!QAZ2wsx1234
#spring.datasource.dynamic.datasource.mysql.lazy=true
#spring.datasource.dynamic.datasource.mysql.druid.max-wait=100000
#spring.datasource.dynamic.datasource.mysql.druid.validation-query=select 1
#spring.datasource.dynamic.datasource.mysql.druid.validation-query-timeout=200
#spring.datasource.dynamic.datasource.mysql.druid.initial-size=5
#spring.datasource.dynamic.datasource.mysql.druid.max-active=5
#spring.datasource.dynamic.datasource.mysql.druid.min-idle=5
##clickhouse database configuration
#spring.datasource.dynamic.datasource.clickhouse.url=*******************************************************************************************************************************************************************************
#spring.datasource.dynamic.datasource.clickhouse.driver-class-name=com.clickhouse.jdbc.ClickHouseDriver
#spring.datasource.dynamic.datasource.clickhouse.username=admin
#spring.datasource.dynamic.datasource.clickhouse.password=trsadmin
#spring.datasource.dynamic.datasource.clickhouse.lazy=true
#spring.datasource.dynamic.datasource.clickhouse.druid.max-wait=100000
#spring.datasource.dynamic.datasource.clickhouse.druid.validation-query=select 1
#spring.datasource.dynamic.datasource.clickhouse.druid.validation-query-timeout=200
#spring.datasource.dynamic.datasource.clickhouse.druid.initial-size=5
#spring.datasource.dynamic.datasource.clickhouse.druid.max-active=5
#spring.datasource.dynamic.datasource.clickhouse.druid.min-idle=5
#logging configuration
logging.file.path=./logs
logging.level.root=INFO
spring.flyway.enabled=false

moye.merger.config.enable=false