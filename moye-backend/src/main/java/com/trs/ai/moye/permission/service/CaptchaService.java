package com.trs.ai.moye.permission.service;

import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.security.SecureRandom;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 生成验证码
 *
 * <AUTHOR>
 * @since 2024/11/7 17:25
 */
@Slf4j
@Service
public class CaptchaService {

    /**
     *  定义图形验证码中绘制的字符的字体
     */
    private final Font mFont = new Font("Arial Black", Font.PLAIN, 23);

    /**
     * 图形验证码的大小
     */
    private static final int IMG_WIDTH = 72;
    private static final int IMG_HEIGHT = 27;

    private final SecureRandom secureRandom = new SecureRandom();

    /**
     * 生成随机验证码图片
     *
     * @param request  HttpServletRequest
     * @param response HttpServletResponse
     */
    public void authCodeService(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        response.setContentType("image/jpeg");
        BufferedImage image = new BufferedImage(IMG_WIDTH, IMG_HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics g = image.createGraphics();

        // 填充背景色
        g.setColor(getRandColor(200, 250));
        g.fillRect(1, 1, IMG_WIDTH - 1, IMG_HEIGHT - 1);
        // 为图形验证码绘制边框
        g.setColor(new Color(102, 102, 102));
        g.drawRect(0, 0, IMG_WIDTH, IMG_HEIGHT);
        // 生成随机干扰线
        g.setColor(getRandColor(160, 200));
        for (int i = 0; i < 80; i++) {
            int x = secureRandom.nextInt(IMG_WIDTH - 1);
            int y = secureRandom.nextInt(IMG_HEIGHT - 1);
            int x1 = secureRandom.nextInt(6) + 1;
            int y1 = secureRandom.nextInt(12) + 1;
            g.drawLine(x, y, x + x1, y + y1);
        }
        // 生成随机干扰线
        g.setColor(getRandColor(160, 200));
        for (int i = 0; i < 80; i++) {
            int x = secureRandom.nextInt(IMG_WIDTH - 1);
            int y = secureRandom.nextInt(IMG_HEIGHT - 1);
            int x1 = secureRandom.nextInt(12) + 1;
            int y1 = secureRandom.nextInt(6) + 1;
            g.drawLine(x, y, x - x1, y - y1);
        }
        // 设置绘制字符的字体
        g.setFont(mFont);
        // 用于保存系统生成的随机字符串
        StringBuilder sRand = new StringBuilder();
        for (int i = 0; i < 4; i++) {
            String tmp = getRandomChar();
            log.debug("---------- tmp:" + tmp);
            log.debug("---------- g:" + g);
            sRand.append(tmp);
            g.setColor(new Color(20 + secureRandom.nextInt(110), 20 + secureRandom.nextInt(110),
                20 + secureRandom.nextInt(110)));
            g.drawString(tmp, 15 * i + 10, 20);
        }
        // 获取HttpSession对象
        HttpSession session = request.getSession(true);
        session.removeAttribute("rand");
        log.debug("验证码: {}", sRand);
        session.setAttribute("rand", sRand);
        g.dispose();
        // 向输出流中输出图片
        ImageIO.write(image, "JPEG", response.getOutputStream());
    }

    /**
     * 获取随机颜色的方法
     *
     * @param fc 颜色范围
     * @param bc 颜色范围
     * @return 颜色
     */
    private Color getRandColor(int fc, int bc) {
        if (fc > 255) {
            fc = 255;
        }
        if (bc > 255) {
            bc = 255;
        }
        int r = fc + secureRandom.nextInt(bc - fc);
        int g = fc + secureRandom.nextInt(bc - fc);
        int b = fc + secureRandom.nextInt(bc - fc);
        return new Color(r, g, b);
    }

    /**
     * 获取随机字符串
     *
     * @return 字符串
     */
    private String getRandomChar() {
        int charType = secureRandom.nextInt(3) + 1;
        long charCode;
        char randomChar;
        switch (charType) {
            case 1 -> { // Uppercase letter
                charCode = secureRandom.nextInt(26) + 65L;
                randomChar = (char) (shouldReplace(charCode) ? (charCode + 2) : charCode);
                return String.valueOf(randomChar);
            }
            case 2 -> { // Lowercase letter
                charCode = secureRandom.nextInt(26) + 97L;
                randomChar = (char) (shouldReplace(charCode) ? (charCode + 2) : charCode);
                return String.valueOf(randomChar);
            }
            default -> { // Digit
                charCode = secureRandom.nextInt(10);
                return String.valueOf(shouldReplace(charCode) ? (charCode + 2) : charCode);
            }
        }
    }

    private boolean shouldReplace(long charCode) {
        return charCode == 0 || charCode == 1 || charCode == 'l' || charCode == 'o' || charCode == 'O'
            || charCode == 'I';
    }
}
