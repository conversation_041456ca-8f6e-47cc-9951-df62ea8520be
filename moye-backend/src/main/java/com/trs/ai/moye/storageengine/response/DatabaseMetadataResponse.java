package com.trs.ai.moye.storageengine.response;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DatabaseMetadata
 * <br>
 * 数据库 元信息
 *
 * <AUTHOR>
 * @since 2024/11/22 11:22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DatabaseMetadataResponse {

    List<Database> databases;

    /**
     * 库
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Database {
        private String databaseName;
        private List<Table> tables;
    }

    /**
     * 表
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Table {
        private String tableName;
        private List<Column> columns;
    }

    /**
     * 字段
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Column {
        private String columnName;
    }
}
