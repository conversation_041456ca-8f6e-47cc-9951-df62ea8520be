package com.trs.ai.moye.knowledgebase.request;

import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-11-25 10:25
 */
@Data
@NoArgsConstructor
public class BaseDataBatchDeleteRequest {

    @NotNull(message = "知识库id不能为空")
    private Integer baseId;

    @NotEmpty(message = "数据id列表不能为空")
    private List<Integer> dataIds;
}
