package com.trs.ai.moye.storageengine.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.trs.ai.moye.data.connection.request.ApiGetTreeRequest;
import com.trs.ai.moye.data.connection.request.TestConnectionRequest;
import com.trs.ai.moye.data.connection.response.ApiUrlTreeResponse;
import com.trs.ai.moye.data.model.entity.StorageTask;
import com.trs.ai.moye.data.model.response.StorageEngineResponse;
import com.trs.ai.moye.monitor.enums.DetectionType;
import com.trs.ai.moye.out.request.NebulaStorageRequest;
import com.trs.ai.moye.storageengine.request.CodeSearchParams;
import com.trs.ai.moye.storageengine.request.ConditionSearchParams;
import com.trs.ai.moye.storageengine.request.FileColumnRequest;
import com.trs.ai.moye.storageengine.request.FileTableRequest;
import com.trs.ai.moye.storageengine.request.SubmitJobRequest;
import com.trs.ai.moye.storageengine.response.ConnectionTestDetailResponse;
import com.trs.ai.moye.storageengine.response.DatabaseMetadataResponse;
import com.trs.ai.moye.storageengine.response.DirectoryAndFileResponse;
import com.trs.ai.moye.storageengine.response.RestfulResponse;
import com.trs.ai.moye.storageengine.response.StorageSearchResponse;
import com.trs.ai.moye.storageengine.response.alltable.TableResponse;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.field.mapping.FieldMappingResponse;
import com.trs.moye.base.data.model.field.mapping.MoyeFieldResponse;
import com.trs.moye.base.data.storage.nebula.ChangeFieldNameDto;
import com.trs.moye.base.data.storage.setting.DataStorageSettings;
import java.util.List;

/**
 * 存储引擎服务，主要用于调用存储引擎接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-09-13 17:28
 */
public interface StorageEngineService {

    /**
     * 测试连接
     * <p>
     * 原方法：testConnection
     *
     * @param request 请求参数
     * @return 布尔
     */
    boolean testConnection(TestConnectionRequest request);

    /**
     * 测试连接
     * <p>
     *
     * @param request       请求参数
     * @param detectionType 检测类型
     * @param userName      用户名
     * @return success与失败原因
     */
    ConnectionTestDetailResponse testConnectionAndRecord(TestConnectionRequest request, DetectionType detectionType,
        String userName);

    /**
     * 获取数据库metadata
     *
     * @param connectionId 连接id
     * @return metadata
     */
    DatabaseMetadataResponse getMetadata(Integer connectionId);

    /**
     * 获取连接表信息 原方法：backFillInfo
     *
     * @param connectionId 连接id
     * @return 回填数据列表
     */
    List<TableResponse> getDbTables(Integer connectionId);

    /**
     * 获取连接指定表的字段信息 原方法：getFieldInfoAndProperty
     *
     * @param connectionId 连接id
     * @param tableName    表名
     * @return 字段信息列表
     */
    FieldMappingResponse getDbTableFields(Integer connectionId, String tableName);

    /**
     * 文件全部表
     *
     * @param connectionId 连接id
     * @param request      请求参数
     * @return 表信息
     */
    DirectoryAndFileResponse getFileTables(Integer connectionId, FileTableRequest request);

    /**
     * 文件表字段
     *
     * @param connectionId 连接id
     * @param request      请求参数
     * @return 文件列信息
     */
    FieldMappingResponse getFileTableFields(Integer connectionId, FileColumnRequest request);

    /**
     * 调用storageEngine服务的findByList接口
     *
     * @param connectionId 连接id
     * @param tableName    表名称
     * @param request      查询请求
     * @return {@link StorageSearchResponse}
     * @since 2024/10/18 11:27:39
     */
    StorageSearchResponse conditionQuery(Integer connectionId, String tableName, ConditionSearchParams request);

    /**
     * 调用storageEngine服务的条件查询视图数据
     *
     * @param connectionId 连接id
     * @param sql          视图sql
     * @param request      查询请求
     * @return {@link StorageSearchResponse}
     * @since 2024/11/08 18:01:39
     */
    StorageSearchResponse conditionSqlQuery(Integer connectionId, String sql, ConditionSearchParams request);

    /**
     * 调用storageEngine服务的preview接口
     *
     * @param connectionId 连接id
     * @param tableName    表名称
     * @param request      查询请求
     * @return {@link StorageSearchResponse}
     * @since 2024/10/18 11:27:43
     */
    StorageSearchResponse codeQuery(Integer connectionId, String tableName, CodeSearchParams request);

    /**
     * 获取mq连接所有表信息
     *
     * @param connectionId 连接id
     * @return 所有表
     */
    List<TableResponse> getMqTables(Integer connectionId);

    /**
     * 获取表字段 <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/164155">...</a>
     *
     * @param id        主键
     * @param tableName 表名称
     * @return 表字段
     */
    FieldMappingResponse getMqTableFields(Integer id, String tableName);

    /**
     * 建表
     *
     * @param connectionId  连接id
     * @param dataModelId   数据模型id
     * @param dataStorageId 数据存储id
     * @param settings      建表参数
     * @return {@link  StorageEngineResponse}
     */
    StorageEngineResponse createTable(Integer connectionId, Integer dataModelId, Integer dataStorageId,
        DataStorageSettings settings);


    /**
     * 代码查询 Nebula 原始数据
     *
     * @param connectionId 连接id
     * @param request      请求
     * @return {@link StorageSearchResponse }
     * <AUTHOR>
     * @since 2024/12/23 11:17:48
     */
    StorageSearchResponse codeQueryNebulaRawData(Integer connectionId, CodeSearchParams request);

    /**
     * 提交任务
     *
     * @param request 请求体
     * @return {@link  RestfulResponse}
     * <AUTHOR>
     * @since 2024/10/12 14:21
     */
    RestfulResponse submitJob(SubmitJobRequest request);

    /**
     * 停止任务
     *
     * @param dataModelId 数据模型id
     */
    void stopJob(Integer dataModelId);

    /**
     * 获取该url下的返回参数 拼装成树结构
     *
     * @param request 参数
     * @return 接口响应
     */
    ApiUrlTreeResponse getUrlResponseTree(ApiGetTreeRequest request) throws JsonProcessingException;

    /**
     * 创建存储算子的topic
     *
     * @param dataModelId 数据建模id
     */
    void createStorageTopic(Integer dataModelId);

    /**
     * 更新该任务的状态
     *
     * @param storageJobId jobId
     * @return {@link StorageTask}
     */
    StorageTask updateJobStatus(String storageJobId);

    /**
     * 单个停止任务
     *
     * @param storageJobId jobId
     */
    void stopJobSingle(String storageJobId);

    /**
     * 通过 Storage Engine 获取 moye 字段
     *
     * @param dataConnection    数据连接
     * @param tableName         表名称
     * @param fileColumnRequest File 列请求
     * @param dataModelId       数据模型id
     * @return {@link List }<{@link MoyeFieldResponse }>
     * <AUTHOR>
     * @since 2024/12/13 16:26:46
     */
    List<MoyeFieldResponse> getMoyeFieldsByStorageEngine(DataConnection dataConnection, String tableName,
        FileColumnRequest fileColumnRequest, Integer dataModelId);

    /**
     * nebula图数据库存储
     *
     * @param request 请求参数
     */
    void nebulaStorage(NebulaStorageRequest request);

    /**
     * Nebula 数据传输
     *
     * @param sourceConnectionId 源连接id
     * @param targetConnectionId 目标连接id
     * @return boolean
     * <AUTHOR>
     * @since 2024/12/27 19:21:17
     */
    boolean nebulaDataTransfer(Integer sourceConnectionId, Integer targetConnectionId);

    /**
     * 增加tag/edge物理库
     *
     * @param connectionIds 连接id
     * @param fields        字段
     */
    void addTagEdge(List<Integer> connectionIds, List<DataModelField> fields);

    /**
     * 删除tag/edge物理库
     *
     * @param connectionIds 连接id
     * @param fields        字段
     */
    void deleteTagEdge(List<Integer> connectionIds, List<DataModelField> fields);

    /**
     * 修改tag/edge名称
     *
     * @param connectionIds 连接id
     * @param fields        字段
     */
    void changeTagEdgeName(List<Integer> connectionIds, List<ChangeFieldNameDto> fields);
}
