package com.trs.ai.moye.backstage.response;

import com.trs.ai.moye.backstage.entity.NoticeSendConfHttp;
import com.trs.ai.moye.backstage.enums.NoticeType;
import com.trs.ai.moye.backstage.utils.NoticeCenterUtils;
import com.trs.ai.moye.data.operator.domain.vo.AbilityDTO;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * api推送配置返回对象
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/11/1 10:56
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class ApiNoticeResponse extends InsideNoticeResponse {

    /**
     * api信息
     */
    private AbilityDTO apiInfo;

    /**
     * 配置信息
     */
    private String configuration;


    /**
     * 构造响应体
     *
     * @param messageSendConfInside 消息
     * @param users                 用户
     * @param roles                 角色
     * @param category              层级
     * <AUTHOR>
     * @since 2024/11/1 11:46
     */
    public ApiNoticeResponse(NoticeSendConfHttp messageSendConfInside, Map<Integer, String> users,
        Map<Integer, String> roles, Map<Integer, String> category) {
        super.setId(messageSendConfInside.getId());
        super.setName(messageSendConfInside.getName());
        super.setEnable(messageSendConfInside.getState());
        super.setCreateTime(messageSendConfInside.getCreateTime());
        super.setUpdateTime(messageSendConfInside.getUpdateTime());
        super.setNotifyUserIds(messageSendConfInside.getSendUserIds());
        super.setNotifyUserNames(
            NoticeCenterUtils.getCorrespondingValue(users, messageSendConfInside.getSendUserIds()));
        super.setNotifyRoleIds(messageSendConfInside.getSendRoleIds());
        super.setNotifyRoleNames(
            NoticeCenterUtils.getCorrespondingValue(roles, messageSendConfInside.getSendRoleIds()));
        super.setMessageTypeIds(messageSendConfInside.getMessageTypeIds());
        super.setMessageTypeNames(
            NoticeCenterUtils.getCorrespondingValue(NoticeType.toMap(), messageSendConfInside.getMessageTypeIds()));
        super.setBusinessScopeIds(messageSendConfInside.getBusinessIds());
        super.setBusinessScopeNames(
            NoticeCenterUtils.getCorrespondingValue(category, messageSendConfInside.getBusinessIds()));
        super.setUpdateId(messageSendConfInside.getUpdateBy());
        super.setUpdateBy(users.getOrDefault(messageSendConfInside.getUpdateBy(), ""));
        super.setCreateId(messageSendConfInside.getCreateBy());
        super.setCreateBy(users.getOrDefault(messageSendConfInside.getCreateBy(), ""));
        this.apiInfo = messageSendConfInside.getApiInfo();
        this.configuration = messageSendConfInside.getSendParameter();
    }
}
