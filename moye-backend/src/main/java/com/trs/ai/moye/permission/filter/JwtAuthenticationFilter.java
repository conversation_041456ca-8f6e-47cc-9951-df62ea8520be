package com.trs.ai.moye.permission.filter;

import com.alibaba.excel.util.StringUtils;
import com.auth0.jwt.JWT;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.trs.ai.moye.backstage.entity.User;
import com.trs.ai.moye.backstage.service.UserService;
import com.trs.ai.moye.common.exception.UnauthorizedException;
import com.trs.ai.moye.permission.service.JwtService;
import com.trs.ai.moye.permission.service.TokenStore;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.HandlerExceptionResolver;

/**
 * 权限filter，校验请求所带的token是否可用，是则放行
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private static final String ADMIN = "admin";
    private static final String AUTHORIZATION = "Authorization";
    private static final List<String> OUT_API_PREFIXES = List.of("/moye/out");
    private static final String ABILITY_CENTER_AUTHORIZATION_PREFIX = "Basic ";
    @Resource(name = "handlerExceptionResolver")
    private HandlerExceptionResolver handlerExceptionResolver;
    @Resource
    private JwtService jwtService;
    @Resource
    private TokenStore tokenStore;
    @Resource
    private UserService userService;

    private static final List<String> ALLOWED_ALGORITHMS =
        List.of("HS256", "HS384", "HS512", "RS256", "RS384", "RS512");


    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
        @NonNull FilterChain filterChain) throws ServletException, IOException {

        // 外部接口不需要token
        if (outApiFilter(request, response, filterChain)) {
            return;
        }

        final String jwt = JwtService.getJwtToken(request);
        if (jwt == null) {
            filterChain.doFilter(request, response);
            return;
        }
        try {
            // 先解码检查算法
            DecodedJWT unverified = JWT.decode(jwt);
            String algorithm = unverified.getAlgorithm();
            // 检查算法是否为"none"
            if (algorithm == null || "none".equalsIgnoreCase(algorithm)) {
                throw new JWTVerificationException("不允许None算法");
            }
            if (!ALLOWED_ALGORITHMS.contains(algorithm.toUpperCase())) {
                throw new JWTVerificationException("不支持的算法: " + algorithm);
            }

            //校验token合法性，同时从token中提取用户名
            final DecodedJWT decoded = jwtService.extractJwt(jwt);
            Long loginTime = decoded.getClaim(JwtService.LOGIN_TIME).asLong();
            String account = decoded.getIssuer();
            //与redis中的可用token比对，判断是否已被删除
            String validToken = tokenStore.getTokenByUser(account, loginTime);
            if (validToken == null || !StringUtils.equals(validToken, jwt)) {
                throw new UnauthorizedException("token已失效，用户已退出或在别处登录！");
            }
            //把用户信息设置到SecurityContextHolder
            setAuthentication(account, request);
            //刷新redis中token有效时间
            tokenStore.saveToken(account, loginTime, jwt);
            filterChain.doFilter(request, response);
        } catch (Exception exception) {
            handlerExceptionResolver.resolveException(request, response, null, exception);
        }
    }

    private void setAuthentication(String account, HttpServletRequest request) {
        User user = userService.getUserByAccountCacheable(account);
        UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
            user, null, new ArrayList<>());
        authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        SecurityContextHolder.getContext().setAuthentication(authToken);
    }


    private boolean outApiFilter(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response,
        @NotNull FilterChain filterChain) throws IOException, ServletException {
        String requestUri = request.getRequestURI();

        // 判断是否为指定接口
        if (isOutApi(requestUri)) {
            // 设置默认用户
            setDefaultUser(request);
            filterChain.doFilter(request, response);
            return true;
        }
        return false;
    }

    private boolean isOutApi(String requestUri) {
        return OUT_API_PREFIXES.stream().anyMatch(requestUri::startsWith);
    }


    /**
     * 设置默认用户到 SecurityContext
     *
     * @param request 当前 HTTP 请求
     */
    private void setDefaultUser(HttpServletRequest request) {
        String account = getOutUser(request);
        if (StringUtils.isBlank(account)) {
            account = ADMIN;
        }
        setAuthentication(account, request);
    }

    /**
     * 提取token字符串
     *
     * @param request HttpServletRequest
     * @return token
     */
    public static String getOutUser(HttpServletRequest request) {
        final String authHeader = request.getHeader(AUTHORIZATION);
        if (StringUtils.isBlank(authHeader)) {
            return "";
        }
        String account = "";
        if (isAbilityCenterAuthorization(authHeader)) {
            return getAbilityCenterAccount(authHeader);
        }
        try {
            account = new String(Base64.getDecoder().decode(authHeader));
        } catch (Exception e) {
            log.error("解析外部用户失败", e);
        }
        return account;
    }

    private static boolean isAbilityCenterAuthorization(String authorization) {
        return authorization != null && authorization.startsWith(ABILITY_CENTER_AUTHORIZATION_PREFIX);
    }

    private static String getAbilityCenterAccount(String authorization) {
        try {
            String accountAndPassword = new String(
                Base64.getDecoder().decode(authorization.substring(ABILITY_CENTER_AUTHORIZATION_PREFIX.length())));
            return accountAndPassword.split(":")[0];
        } catch (Exception e) {
            log.error("解析能力中心账号", e);
            return "";
        }
    }
}