package com.trs.ai.moye.backstage.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.backstage.entity.Notice;
import com.trs.ai.moye.backstage.request.NoticeListRequest;
import com.trs.ai.moye.backstage.response.NoticeResponse;
import com.trs.moye.base.common.request.SearchParams;
import com.trs.moye.base.common.request.SortParams;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 消息日志mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/11/4 11:33
 **/
@Mapper
public interface NoticeMapper extends BaseMapper<Notice> {


    /**
     * 分页查询
     *
     * @param searchParams  检索参数
     * @param messageTypeId 消息类型
     * @param toPage        分页
     * @param sortParams    排序
     * @return {@link Notice}
     * <AUTHOR>
     * @since 2024/11/4 14:35
     */
    Page<Notice> selectNoticeList(@Param("searchParams") SearchParams searchParams,
        @Param("messageTypeId") Integer messageTypeId, @Param("sortParams") SortParams sortParams,
        Page<Notice> toPage);

    /**
     * 获取该用户全部未读消息
     *
     * @param userId 用户id
     * @return 未读消息
     */
    List<NoticeResponse> getUnreadAll(@Param("userId") Integer userId);

    /**
     * 用户消息中心列表
     *
     * @param request 参数
     * @param page    分页参数
     * @param userId  当前用户id
     * @return 列表
     */
    Page<NoticeResponse> userList(@Param("request") NoticeListRequest request, Page<NoticeResponse> page,
        @Param("userId") Integer userId);
}
