package com.trs.ai.moye.backstage.response;

import com.trs.ai.moye.backstage.entity.AuthCertificateKerberos;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/31
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CertificateResponse {

    private Integer id;
    private String principal;


    /**
     * 将实体转换为响应参数
     *
     * @param authCertificateKerberos 实体
     * @return 响应参数
     */
    public static CertificateResponse from(AuthCertificateKerberos authCertificateKerberos) {
        CertificateResponse certificateResponse = new CertificateResponse();
        certificateResponse.setId(authCertificateKerberos.getId());
        certificateResponse.setPrincipal(authCertificateKerberos.getPrincipal());
        return certificateResponse;
    }

}
