package com.trs.ai.moye.backstage.controller;


import com.trs.ai.moye.backstage.dao.NoticeSendConfHttpMapper;
import com.trs.ai.moye.backstage.dao.NoticeSendConfInsideMapper;
import com.trs.ai.moye.backstage.dao.RoleMapper;
import com.trs.ai.moye.backstage.dao.UserMapper;
import com.trs.ai.moye.backstage.entity.NoticeSendConfHttp;
import com.trs.ai.moye.backstage.entity.NoticeSendConfInside;
import com.trs.ai.moye.backstage.enums.NoticePushTypeEnum.NoticePushType;
import com.trs.ai.moye.backstage.enums.NoticeSendConfInsideCreateType;
import com.trs.ai.moye.backstage.enums.NoticeType;
import com.trs.ai.moye.backstage.enums.SendObjEnum;
import com.trs.ai.moye.backstage.request.AddInsideNoticeRequest;
import com.trs.ai.moye.backstage.request.ApiMsgSendStrategyRequest;
import com.trs.ai.moye.backstage.request.NoticeListRequest;
import com.trs.ai.moye.backstage.request.UpdateInsideNoticeRequest;
import com.trs.ai.moye.backstage.response.ApiNoticeResponse;
import com.trs.ai.moye.backstage.response.InsideNoticeResponse;
import com.trs.ai.moye.backstage.response.MessagePushRecordResponse;
import com.trs.ai.moye.backstage.response.NoticeResponse;
import com.trs.ai.moye.backstage.response.SendObjResponse;
import com.trs.ai.moye.backstage.service.NoticeCenterService;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.ai.moye.data.model.request.MessagePushRecordRequest;
import com.trs.ai.moye.data.operator.domain.vo.AbilityDTO;
import com.trs.ai.moye.data.operator.service.OperatorAbilityCenterService;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.response.IdNameResponse;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * V4消息中心控制层
 *
 * <AUTHOR>
 * @since 2024/8/28
 */
@RestController
@RequestMapping("/notice-center")
@Validated
public class NoticeCenterController {

    @Value("${com.trs.msg.send.ability.client.name:MOYE_MSG_SEND}")
    private String msgSendApiName;

    @Resource
    private NoticeCenterService noticeCenterService;

    @Resource
    private OperatorAbilityCenterService operatorAbilityCenterService;

    @Resource
    private NoticeSendConfInsideMapper noticeSendConfInsideMapper;

    @Resource
    private NoticeSendConfHttpMapper noticeSendConfHttpMapper;


    @Resource
    private UserMapper userMapper;

    @Resource
    private RoleMapper roleMapper;


    /**
     * 获取租户/角色/用户  <a href="http://**************:3001/project/5419/interface/api/165300">...</a>
     *
     * @param type 需要返回的类型
     * @return {@link SendObjResponse}
     * <AUTHOR>
     * @since 2024/10/30 15:00
     */
    @GetMapping("/members")
    public List<SendObjResponse> getSendObj(@NotNull @RequestParam SendObjEnum type) throws BizException {
        return switch (type) {
            case ROLE ->
                // 处理角色相关逻辑
                roleMapper.selectAll().stream().map(role -> new SendObjResponse(role.getId(), role.getName())).toList();
            case USER ->
                // 处理用户相关逻辑
                userMapper.selectAll().stream().map(user -> new SendObjResponse(user.getId(), user.getName())).toList();
            default -> List.of();
        };
    }

    /**
     * 获取发送消息类型列表 <a href="http://**************:3001/project/5419/interface/api/165315">...</a>
     *
     * @return {@link NoticeType}
     * <AUTHOR>
     * @since 2024/10/30 16:17
     */
    @GetMapping("/message-type")
    public List<IdNameResponse> getSendMsgTypeList() {
        return NoticeType.toObjectList();
    }

    /**
     * 获取能力中心的api接口 <a href="http://**************:3001/project/5419/interface/api/165310">...</a>
     *
     * @return {@link AbilityDTO}
     * <AUTHOR>
     * @since 2024/10/30 16:17
     */
    @GetMapping("/ability-center/api/list")
    public List<AbilityDTO> listAbility() throws BizException {
        List<AbilityDTO> abilityList = operatorAbilityCenterService.getAllAbilityApiList();
        return abilityList.stream().filter(abilityVO -> abilityVO.getAppCode().equals(msgSendApiName)).toList();
    }


    /**
     * 校验站内消息名称是否重复 <a href="http://**************:3001/project/5419/interface/api/165305">...</a>
     *
     * @param name 名称
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/10/31 17:06
     */

    @GetMapping("/inside/check-name")
    public Boolean checkInsideName(@NotBlank @RequestParam("name") String name) throws BizException {
        return noticeSendConfInsideMapper.selectByName(name) > 0;
    }


    /**
     * 新增站内消息 <a href="http://**************:3001/project/5419/interface/api/165335">...</a>
     *
     * @param request 请求
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/10/30 17:39
     */
    @PostMapping("/inside-notice")
    public Boolean addInsideNotice(@Valid @RequestBody AddInsideNoticeRequest request) throws BizException {
        return noticeSendConfInsideMapper.insert(NoticeSendConfInside.formHandAdd(request)) > 0;
    }


    /**
     * 站内消息更新 <a href="http://**************:3001/project/5419/interface/api/165365">...</a>
     *
     * @param request 前段请求
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/10/31 11:00
     */
    @PutMapping("/inside/update-notice")
    public Boolean updateInsideNotice(@RequestBody @Valid UpdateInsideNoticeRequest request) throws BizException {
        NoticeSendConfInside noticeSendConfInside = noticeSendConfInsideMapper.selectById(request.getId());
        if (noticeSendConfInside == null) {
            throw new BizException("所更改的记录不存在！");
        }
        if (NoticeSendConfInsideCreateType.HAND.equals(noticeSendConfInside.getCreateModel())
            && request.getBusinessScopeIds().isEmpty()) {
            throw new BizException("业务范围不能为空！");
        }
        return noticeSendConfInsideMapper.updateById(NoticeSendConfInside.formUpdate(request, noticeSendConfInside))
            > 0;
    }


    /**
     * 删除站内消息 <a href="http://**************:3001/project/5419/interface/api/165370">...</a>
     *
     * @param id 消息id
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/10/31 13:53
     */
    @DeleteMapping("/inside/{id}")
    public Boolean deleteInsideNotice(@PathVariable @NotNull Integer id) throws BizException {
        return noticeSendConfInsideMapper.deleteById(id) > 0;

    }


    /**
     * 站内消息列表查询  <a href="http://**************:3001/project/5419/interface/api/165355">...</a>
     *
     * @param request 前端请求
     * @return {@link InsideNoticeResponse}
     * <AUTHOR>
     * @since 2024/10/31 16:25
     */
    @PostMapping("/inside/page-list")
    public PageResponse<InsideNoticeResponse> listInsideNotice(@RequestBody NoticeListRequest request) {
        return noticeCenterService.getInsideNoticeList(request);
    }


    /**
     * 站内消息启动 <a href="http://**************:3001/project/5419/interface/api/165375">...</a>
     *
     * @param id ID
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/10/31 17:25
     */
    @PutMapping("/inside/{id}/start")
    public Boolean startInsideNotice(@PathVariable Integer id) throws BizException {
        try {
            return noticeSendConfInsideMapper.updateStateById(id, Boolean.TRUE);
        } catch (Exception e) {
            throw new BizException("消息推送启动失败");
        }
    }


    /**
     * 站内消息停止  <a href="http://**************:3001/project/5419/interface/api/165380">...</a>
     *
     * @param id ID
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/10/31 17:25
     */
    @PutMapping("/inside/{id}/stop")
    public Boolean stopInsideNotice(@PathVariable Integer id) throws BizException {
        try {
            return noticeSendConfInsideMapper.updateStateById(id, Boolean.FALSE);
        } catch (Exception e) {
            throw new BizException("消息推送停止失败！");
        }
    }


    /**
     * API消息推送配置名称校验 <a href="http://**************:3001/project/5419/interface/api/165340">...</a>
     *
     * @param name 名称
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/11/1 10:19
     */
    @GetMapping("/api/check-name")
    public Boolean checkName(@NotBlank @RequestParam("name") String name) {
        List<NoticeSendConfHttp> noticeSendConfHttp = noticeSendConfHttpMapper.selectByName(name);
        //如果ID不为空就排除改id
        return !noticeSendConfHttp.isEmpty();
    }


    /**
     * 新增aip消息推送配置 <a href="http://**************:3001/project/5419/interface/api/165335">...</a>
     *
     * @param request 请求
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/11/1 10:41
     */
    @PostMapping("/api")
    public Boolean addApiMsgSendStrategy(@RequestBody @Validated ApiMsgSendStrategyRequest request) {
        return noticeSendConfHttpMapper.insert(request.getAddEntity()) > 0;
    }


    /**
     * API列表查询 <a href="http://**************:3001/project/5419/interface/api/165320">...</a>
     *
     * @param request 请求
     * @return {@link ApiNoticeResponse}
     * <AUTHOR>
     * @since 2024/11/1 11:50
     */
    @PostMapping("/api/page-list")
    public PageResponse<ApiNoticeResponse> getApiMsgSendStrategyList(
        @RequestBody @Validated NoticeListRequest request) {
        return noticeCenterService.getApiMsgSendStrategyList(request);
    }


    /**
     * 更新api推送策略 <a href="http://**************:3001/project/5419/interface/api/165350">...</a>
     *
     * @param request 前端请求
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/11/1 14:30
     */
    @PutMapping("/api")
    public Boolean updateApiSendStrategy(@RequestBody ApiMsgSendStrategyRequest request) {
        Integer id = request.getId();
        if (Objects.isNull(id)) {
            throw new BizException("更新主键ID不能为空！");
        }
        NoticeSendConfHttp noticeSendConfHttp = noticeSendConfHttpMapper.selectById(id);
        return noticeSendConfHttpMapper.updateById(request.getUpdateEntity(noticeSendConfHttp)) > 0;
    }


    /**
     * 通过ID删除api策略 <a href="http://**************:3001/project/5419/interface/api/165345">...</a>
     *
     * @param id id
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/11/1 14:34
     */
    @DeleteMapping("/{id}/api")
    public Boolean deleteApiSendStrategy(@NotNull @PathVariable Integer id) {
        return noticeSendConfHttpMapper.deleteById(id) > 0;
    }


    /**
     * 停止api推送 <a href="http://**************:3001/project/5419/interface/api/165325">...</a>
     *
     * @param id ID
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/11/1 14:43
     */
    @PutMapping("/{id}/api/stop")
    public Boolean stopApiNotice(@PathVariable Integer id) throws BizException {
        try {
            return noticeSendConfHttpMapper.updateStateById(id, Boolean.FALSE);
        } catch (Exception e) {
            throw new BizException("消息推送停止失败！");
        }
    }


    /**
     * 启用API推送 <a href="http://**************:3001/project/5419/interface/api/165330">...</a>
     *
     * @param id ID
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/11/1 14:44
     */
    @PutMapping("/{id}/api/start")
    public Boolean startApiNotice(@PathVariable Integer id) throws BizException {
        try {
            return noticeSendConfHttpMapper.updateStateById(id, Boolean.TRUE);
        } catch (Exception e) {
            throw new BizException("消息推送启动失败");
        }
    }


    /**
     * 获取消息中心列表 <a href="http://**************:3001/project/5419/interface/api/165395">...</a>
     *
     * @param request 前端请求
     * @return {@link NoticeResponse}
     * <AUTHOR>
     * @since 2024/11/4 11:22
     */
    @PostMapping("/list")
    public PageResponse<NoticeResponse> getNoticeList(@RequestBody @Validated NoticeListRequest request) {
        return noticeCenterService.getNoticeList(request);
    }

    /**
     * 获取消息中心日志列表 <a href="http://**************:3001/project/5419/interface/api/165405">...</a>
     *
     * @param request 前端请求
     * @return {@link MessagePushRecordResponse}
     * <AUTHOR>
     * @since 2024/11/4 16:13
     */
    @PostMapping("/push-records/page-list")
    public PageResponse<MessagePushRecordResponse> listPushRecords(@RequestBody MessagePushRecordRequest request) {
        return noticeCenterService.listPushRecords(request);
    }

    /**
     * 获取 消息推送类型 <br/>
     * <a href=https://yapi-192.trscd.com.cn/project/4220/interface/api/164260>YApi</a>
     *
     * @return 所有枚举值
     */
    @GetMapping("/push-type")
    public List<NoticePushType> getPushTypeList() {
        return NoticePushType.getAllNoticePushTypes();
    }

    /**
     * 用户消息列表查询
     *
     * @param request 参数
     * @return 消息列表
     */
    @PostMapping("/user/list")
    public PageResponse<NoticeResponse> userList(@RequestBody @Validated NoticeListRequest request) {
        return noticeCenterService.userList(request);
    }

    /**
     * 已读全部消息
     */
    @GetMapping("/read-all")
    public void readAllNotice() {
        noticeCenterService.readAllNotice();
    }

    /**
     * 已读单条消息
     *
     * @param id 消息id
     */
    @GetMapping("/{id}/read")
    public void readSingleNotice(@PathVariable Integer id) {
        noticeCenterService.readSingleNotice(id);
    }

    /**
     * 查询全部未读消息
     *
     * @return 全部未读消息
     */
    @GetMapping("/unread/all")
    public List<NoticeResponse> getAllUnread() {
        return noticeCenterService.getAllUnread();
    }
}
