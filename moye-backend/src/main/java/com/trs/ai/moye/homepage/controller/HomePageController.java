package com.trs.ai.moye.homepage.controller;

import com.trs.ai.moye.homepage.service.HomePageService;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @since 2025/1/17
 **/

@RequestMapping("/home/<USER>")
@RestController()
public class HomePageController {


    @Resource
    private HomePageService homePageService;


    /**
     * 获取首页的流水线展示数据
     *
     * @return {@link Map}<{@link String}, {@link Map}<{@link String}, {@link Long}>>
     */
    @GetMapping("/pipeline")
    public Map<String, Map<String, Long>> getHomePagePipelineData() {
        return homePageService.getHomePagePipelineData();
    }
}

