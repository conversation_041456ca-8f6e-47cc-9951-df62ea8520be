package com.trs.ai.moye.permission.properties;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/**
 * 安全配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "com.trs.security")
@Data
@Validated
public class SecurityProperties {

    /**
     * 是否开启验证码
     */
    @NotNull(message = "enableCode不能为空")
    private Boolean enableCode;

    /**
     * 登录错误次数限制
     */
    @NotNull(message = "loginFailNumLimit不能为空")
    private Integer loginFailNumLimit;

    /**
     * 提示更新密码天数
     */
    @NotNull(message = "warnUpdatePwdDays不能为空")
    private Integer warnUpdatePwdDays;

    /**
     * 是否开启url checksum编译
     */
    @NotNull(message = "checksumEnableConfig不能为空")
    private Boolean checksumEnableConfig;

    /**
     * token过期时间（ms） 默认24h
     */
    @NotNull(message = "com.trs.security.token-expiration-millis不能为空")
    private Long tokenExpirationMillis = 43200000L;

    /**
     * redis中token过期时间（ms） 默认30分钟
     */
    @NotNull(message = "com.trs.security.redis-token-expiration-millis不能为空")
    private Long redisTokenExpirationMillis = 1800000L;

    /**
     * jwt生成token密钥
     */
    @NotBlank(message = "com.trs.security.jwt-secret-key不能为空")
    private String jwtSecretKey;
}
