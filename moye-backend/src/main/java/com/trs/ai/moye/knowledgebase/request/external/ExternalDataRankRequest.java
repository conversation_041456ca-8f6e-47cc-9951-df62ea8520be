package com.trs.ai.moye.knowledgebase.request.external;

import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import com.trs.moye.base.common.request.SortParams;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-11-20 17:55
 */
@Data
@NoArgsConstructor
public class ExternalDataRankRequest {

    @NotNull(message = "知识库类型不能为空")
    private KnowledgeBaseType type;

    @NotBlank(message = "实体名称不能为空")
    private String enName;

    @NotBlank(message = "搜索字段不能为空")
    private String searchField;

    @NotEmpty(message = "关键字不能为空")
    private List<String> keywords;

    private SortParams sortParams;
}
