package com.trs.ai.moye.data.model.request;

import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.model.enums.DataProcessMode;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * 要素库数据来源请求实体
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/11 17:35
 **/
@Data
@Validated
public class DataSourceModelRequest {

    /**
     * 数据处理方式，可以为空，为空时不进行过滤
     */
    private DataProcessMode dataProcessMode;

    /**
     * 业务分类id 通过业务分类id筛选
     */
    private Integer categoryId;

    /**
     * 治理状态 是否已治理
     */
    private Boolean arrangeStatus;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 具体要获取那一层的来源，要素库，主题库，还是专题库。
     */
    @NotNull(message = "modelLayer不能为空")
    private ModelLayer modelLayer;

    /**
     * 检索的层级
     */
    private ModelLayer searchLayer;

    /**
     * 连接类型
     */
    private ConnectionType connectionType;

    /**
     * 是否过滤掉所有存储点都是未建表状态的数据建模 当该参数为 true 时，只返回至少有一个存储点已建表的数据建模 当该参数为 false 或未传入时，保持原有逻辑不变
     */
    private Boolean filterNotCreatedTables;

}
