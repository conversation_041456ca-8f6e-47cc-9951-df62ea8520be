package com.trs.ai.moye.backstage.service;

import com.trs.ai.moye.backstage.entity.ui.AppConfigParam;
import com.trs.ai.moye.backstage.enums.AppConfigType;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-12-05 14:41
 */
public interface AppConfigService {

    /**
     * 根据类型获取配置
     *
     * @param type 类型
     * @return 配置参数
     */
    AppConfigParam getAppConfigParam(AppConfigType type);

    /**
     * 修改配置
     *
     * @param config 配置
     */
    void updateAppConfig(AppConfigParam config);

    /**
     * 获取所有配置
     *
     * @return {@link List }<{@link AppConfigParam }>
     */
    List<AppConfigParam> getAllAppConfig();
}
