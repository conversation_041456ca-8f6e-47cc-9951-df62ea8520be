package com.trs.ai.moye.streamengine.feign;

import com.trs.ai.moye.common.config.OpenFeignConfig;
import com.trs.moye.ability.domain.TestDataProcessRequest;
import com.trs.moye.ability.entity.AbilityExecuteParams;
import com.trs.moye.ability.request.RetryOperatorRequest;
import com.trs.moye.base.common.response.ResponseMessage;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 流引擎 Feign
 *
 * <AUTHOR>
 * @since 2025/03/05 14:13:24
 */
@FeignClient(name = "moye-stream-engine", path = "/engine", configuration = OpenFeignConfig.class)
public interface StreamEngineFeign {

    /**
     * 能力执行
     *
     * @param params 参数
     * @return {@link ResponseMessage }
     */
    @PostMapping("/ability/execute")
    ResponseMessage abilityExecute(@RequestBody AbilityExecuteParams params);

    /**
     * 验证算子
     *
     * @param ruleRequest 规则请求
     * @return {@link ResponseMessage }
     */
    @PostMapping("/ability/operator/retry")
    ResponseMessage retryOperator(@RequestBody RetryOperatorRequest ruleRequest);

    /**
     * 测试消息处理
     *
     * @param dataModelId 建模id
     * @param request     测试请求
     * @return 处理结果
     */
    @PostMapping("/test/{dataModelId}")
    ResponseMessage test(@PathVariable("dataModelId") Integer dataModelId, @RequestBody TestDataProcessRequest request);


    /**
     * 触发引擎节点分配
     *
     * <AUTHOR>
     */
    @PostMapping("/node-allocation/allocate-server-node")
    void allocateServerNode();
}
