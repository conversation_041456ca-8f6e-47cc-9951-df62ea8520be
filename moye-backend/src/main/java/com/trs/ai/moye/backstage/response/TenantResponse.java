package com.trs.ai.moye.backstage.response;

import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 租户列表
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/12/28 13:36
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TenantResponse {

    /**
     * 租户id
     */
    private Integer id;

    /**
     * 租户名称
     */
    private String name;

    /**
     * 租户描述
     */
    private String description;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 最近更新人
     */
    private String updateUser;

    /**
     * 最近更新时间
     */
    private Timestamp updateTime;

}
