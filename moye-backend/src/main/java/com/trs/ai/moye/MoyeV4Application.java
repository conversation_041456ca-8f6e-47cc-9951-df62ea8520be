package com.trs.ai.moye;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * SpringApplication  启动类
 */
@SpringBootApplication
@EnableCaching
@EnableFeignClients
@EnableScheduling
@MapperScan(basePackages = {"com.trs.**.dao"})
public class MoyeV4Application {

    /**
     * main方法
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        SpringApplication.run(MoyeV4Application.class, args);
    }

}
