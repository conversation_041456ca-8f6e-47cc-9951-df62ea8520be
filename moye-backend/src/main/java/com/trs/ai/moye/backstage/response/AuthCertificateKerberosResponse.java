package com.trs.ai.moye.backstage.response;

import com.trs.ai.moye.backstage.entity.AuthCertificateKerberos;
import com.trs.ai.moye.common.response.AuditBaseResponse;
import java.nio.file.Path;
import java.nio.file.Paths;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/20
 **/
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AuthCertificateKerberosResponse extends AuditBaseResponse<AuthCertificateKerberos> {


    /**
     * 用户名称
     */
    private String principal;

    /**
     * krb5配置文件路径
     */
    private String krb5Path;

    /**
     * 密钥文件地址
     */
    private String keytabPath;

    private String krbFileName;

    private String keytabFileName;

    /**
     * 是否在使用
     */
    private Boolean inUse;

    @Override
    protected void populateProperties(AuthCertificateKerberos entity) {
        this.principal = entity.getPrincipal();
        this.krb5Path = entity.getKrb5Path();
        this.keytabPath = entity.getKeytabPath();
        this.inUse = entity.getInUse();
        this.krbFileName = getFileNameFromPath(entity.getKrb5Path());
        this.keytabFileName = getFileNameFromPath(entity.getKeytabPath());
    }

    /**
     * 根据{@link AuthCertificateKerberos} 构造response
     *
     * @param entity {@link AuthCertificateKerberos}
     */
    public AuthCertificateKerberosResponse(AuthCertificateKerberos entity) {
        super.populateFrom(entity);
    }

    private String getFileNameFromPath(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return null;
        }
        // 将字符串形式的路径转化为 Path 对象
        Path path = Paths.get(filePath);
        // 使用 getFileName() 获取最后一级的文件或目录名称
        return path.getFileName().toString();
    }
}
