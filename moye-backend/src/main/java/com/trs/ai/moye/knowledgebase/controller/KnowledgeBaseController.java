package com.trs.ai.moye.knowledgebase.controller;

import com.trs.ai.moye.common.entity.UsageInfoResponse;
import com.trs.ai.moye.knowledgebase.request.KnowledgeBaseRequest;
import com.trs.ai.moye.knowledgebase.request.KnowledgeBaseUpdateRequest;
import com.trs.ai.moye.knowledgebase.response.KnowledgeBaseListResponse;
import com.trs.ai.moye.knowledgebase.service.KnowledgeBaseService;
import com.trs.moye.base.common.request.BaseRequestParams;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.knowledgebase.entity.ImportEntityDataResult;
import com.trs.moye.base.knowledgebase.entity.KnowledgeBaseField;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import com.trs.moye.base.knowledgebase.request.KnowledgeBaseFieldRequest;
import java.io.IOException;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * V4可能要用 暂时不删除
 *
 * <AUTHOR>
 * @since 2020/6/8 09:59
 */
@RestController()
@RequestMapping("/knowledge-base")
@Validated
public class KnowledgeBaseController {


    @Resource
    private KnowledgeBaseService knowledgeBaseService;

    /**
     * 添加知识库
     * <a href="http://**************:3001/project/5419/interface/api/165697">知识库-新增</a>
     *
     * @param request 请求参数
     * @return 知识库id
     */
    @PostMapping
    public int addKnowledgeBase(@Validated @RequestBody KnowledgeBaseRequest request) {
        return knowledgeBaseService.addKnowledgeBase(request);
    }

    /**
     * 检查知识库名称是否可用 存在
     *
     * @param type     知识库类型
     * @param name     知识库名称
     * @param isZhName 是否为中文名称
     * @return 是否存在
     */
    @GetMapping("/exist-name")
    public boolean existKnowledgeBaseName(@RequestParam KnowledgeBaseType type, @RequestParam String name,
        @RequestParam boolean isZhName) {
        return knowledgeBaseService.existKnowledgeBaseName(type, name, isZhName);
    }

    /**
     * 修改知识库
     * <a href="http://**************:3001/project/5419/interface/api/165709">知识库-编辑</a>
     *
     * @param baseId  知识库id
     * @param request 请求参数
     */
    @PutMapping("/{baseId}")
    public void updateKnowledgeBase(@PathVariable Integer baseId,
        @Validated @RequestBody KnowledgeBaseUpdateRequest request) {
        knowledgeBaseService.updateKnowledgeBase(baseId, request);
    }

    /**
     * 获取知识库使用信息
     *
     * @param baseId 知识库id
     * @return 知识库使用信息
     */
    @GetMapping("/{baseId}/usage")
    public UsageInfoResponse getKnowledgeBaseUsageInfo(@PathVariable Integer baseId) {
        return knowledgeBaseService.getKnowledgeBaseUsageInfo(baseId);
    }

    /**
     * 删除知识库
     *
     * @param baseId 知识库id
     */
    @DeleteMapping("/{baseId}")
    public void deleteKnowledgeBase(@PathVariable Integer baseId) {
        knowledgeBaseService.deleteKnowledgeBase(baseId);
    }

    /**
     * 知识库列表
     *
     * @param type 知识库类型
     * @return 知识库列表
     */
    @GetMapping("/list")
    public List<KnowledgeBaseListResponse> getKnowledgeBaseList(@RequestParam KnowledgeBaseType type) {
        return knowledgeBaseService.getKnowledgeBaseList(type);
    }

    /**
     * 获取知识库列表
     *
     * @return {@link List }<{@link KnowledgeBaseListResponse }>
     */
    @GetMapping("/list-all")
    public List<KnowledgeBaseListResponse> getKnowledgeBaseList() {
        return knowledgeBaseService.getKnowledgeBaseList();
    }

    /**
     * 修改字段
     *
     * @param baseId  知识库id
     * @param request 请求参数
     * @return 字段id
     */
    @PostMapping("/{baseId}/field")
    public int addField(@PathVariable Integer baseId, @RequestBody KnowledgeBaseFieldRequest request) {
        return knowledgeBaseService.addField(baseId, request);
    }

    /**
     * 检查字段名称是否存在
     *
     * @param baseId   知识库id
     * @param name     字段名称
     * @param isZhName 是否为中文名称
     * @return 是否存在
     */
    @GetMapping("/{baseId}/field/exist-name")
    public boolean existFieldName(@NotNull(message = "知识库id不允许为空") @PathVariable Integer baseId,
        @NotBlank(message = "字段名称不允许为空") @RequestParam String name, @RequestParam boolean isZhName) {
        return knowledgeBaseService.existFieldName(baseId, name, isZhName);
    }

    /**
     * 获取字段分页列表
     *
     * @param baseId  知识库id
     * @param request 请求参数
     * @return 字段列表
     */
    @PostMapping("/{baseId}/field/page-list")
    public PageResponse<KnowledgeBaseField> getFieldPageList(@PathVariable Integer baseId,
        @RequestBody BaseRequestParams request) {
        return knowledgeBaseService.getFieldPageList(baseId, request);
    }

    /**
     * 获取字段列表
     *
     * @param baseId 知识库id
     * @return 字段列表
     */
    @GetMapping("/{baseId}/field/list")
    public List<KnowledgeBaseField> getFieldList(@PathVariable Integer baseId) {
        return knowledgeBaseService.getFieldList(baseId);
    }

    /**
     * 修改字段顺序
     *
     * @param baseId   知识库id
     * @param fieldIds 字段id列表
     */
    @PutMapping("/{baseId}/field/show-order")
    public void updateFieldShowOrder(@PathVariable Integer baseId, @RequestBody List<Integer> fieldIds) {
        knowledgeBaseService.updateFieldShowOrder(baseId, fieldIds);
    }

    /**
     * 获取字段列表
     *
     * @param baseId 知识库id
     * @return 字段列表
     */
    @GetMapping("/{baseId}/field/support-fuzzy-search")
    public List<KnowledgeBaseField> getSupportFuzzySearchFieldList(@PathVariable Integer baseId) {
        return knowledgeBaseService.getSupportFuzzySearchFieldList(baseId);
    }


    /**
     * Excel模板下载 <a href="http://**************:3001/project/5419/interface/api/165841">...</a>
     *
     * @param baseId   知识库ID
     * @param response 响应
     * <AUTHOR>
     * @since 2024/11/25 11:22
     */
    @GetMapping("/{baseId}/excel-template-download")
    public void excelTemplateDownload(@PathVariable @NotNull Integer baseId, HttpServletResponse response)
        throws IOException {
        knowledgeBaseService.excelTemplateDownload(baseId, response);
    }


    /**
     * 导入知识库数据 <a href="http://**************:3001/project/5419/interface/api/165751">...</a>
     *
     * @param baseId 知识库id
     * @param file   文件
     * @return 导入结果
     */
    @PostMapping("/{baseId}/import")
    public ImportEntityDataResult importKnowledgeBaseDataList(@PathVariable @NotNull Integer baseId,
        @RequestParam @NotNull MultipartFile file) throws IOException {
        return knowledgeBaseService.importKnowledgeBaseDataList(baseId, file);
    }

    /**
     * 导出知识库数据 <a href="http://**************:3001/project/5419/interface/api/165757">...</a>
     *
     * @param baseId   知识库id
     * @param response http响应
     */
    @GetMapping("/{baseId}/export")
    public void exportKnowledgeBaseDataList(@PathVariable @NotNull Integer baseId, HttpServletResponse response)
        throws IOException {
        knowledgeBaseService.exportKnowledgeBaseDataList(baseId, response);
    }

    /**
     * 恢复数据备份
     *
     * @param baseId 知识库id
     */
    @GetMapping("/{baseId}/restore-backup")
    public void restoreKnowledgeBaseDataBackup(@PathVariable Integer baseId) {
        knowledgeBaseService.restoreKnowledgeBaseDataBackup(baseId);
    }

    /**
     * 修改字段
     *
     * @param fieldId 字段id
     * @param request 请求参数
     */
    @PutMapping("/field/{fieldId}")
    public void updateField(@PathVariable Integer fieldId, @RequestBody KnowledgeBaseFieldRequest request) {
        knowledgeBaseService.updateField(fieldId, request);
    }

    /**
     * 删除字段
     *
     * @param fieldId 字段id
     */
    @DeleteMapping("/field/{fieldId}")
    public void deleteField(@PathVariable Integer fieldId) {
        knowledgeBaseService.deleteField(fieldId);
    }

}
