package com.trs.ai.moye.batchengine.entity;

import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 执行结果
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExecuteResultRequest {

    /**
     * 数据建模id
     */
    @NotNull(message = "数据建模id不能为空")
    private Integer dataModelId;

    /**
     * 执行id
     */
    @NotNull(message = "执行id不能为空")
    private List<String> executeIds;

    /**
     * 第几个sql片段(结果1,结果2)
     */
    private Integer part;

    /**
     * 分页页码
     */
    @NotNull(message = "分页页码不能为空")
    private Integer pageNo;

    /**
     * 分页参数
     */
    @NotNull(message = "分页大小不能为空")
    private Integer pageSize;
}
