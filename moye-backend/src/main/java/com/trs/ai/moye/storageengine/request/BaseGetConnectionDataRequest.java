package com.trs.ai.moye.storageengine.request;

import com.trs.moye.base.data.connection.entity.KerberosCertificate;
import com.trs.moye.base.data.connection.entity.params.ConnectionParams;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 获取连接数据请求基类
 */
@Data
public abstract class BaseGetConnectionDataRequest {

    /**
     * 连接参数
     */
    @NotNull(message = "连接信息不能为空")
    private ConnectionParams connectionParams;

    /**
     * 凭证id（kerberos认证使用）
     */
    private KerberosCertificate authCertificate;
}
