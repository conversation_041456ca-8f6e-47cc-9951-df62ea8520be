package com.trs.ai.moye.backstage.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.backstage.dao.MessagePushRecordMapper;
import com.trs.ai.moye.backstage.dao.NoticeMapper;
import com.trs.ai.moye.backstage.dao.NoticeReadRelationMapper;
import com.trs.ai.moye.backstage.dao.NoticeSendConfHttpMapper;
import com.trs.ai.moye.backstage.dao.NoticeSendConfInsideMapper;
import com.trs.ai.moye.backstage.dao.RoleMapper;
import com.trs.ai.moye.backstage.dao.UserMapper;
import com.trs.ai.moye.backstage.entity.MessagePushRecord;
import com.trs.ai.moye.backstage.entity.Notice;
import com.trs.ai.moye.backstage.entity.NoticeReadRelation;
import com.trs.ai.moye.backstage.entity.NoticeSendConfHttp;
import com.trs.ai.moye.backstage.entity.NoticeSendConfInside;
import com.trs.ai.moye.backstage.entity.Role;
import com.trs.ai.moye.backstage.entity.User;
import com.trs.ai.moye.backstage.enums.NoticePushTypeEnum;
import com.trs.ai.moye.backstage.enums.NoticeType;
import com.trs.ai.moye.backstage.request.NoticeListRequest;
import com.trs.ai.moye.backstage.response.ApiNoticeResponse;
import com.trs.ai.moye.backstage.response.InsideNoticeResponse;
import com.trs.ai.moye.backstage.response.MessagePushRecordResponse;
import com.trs.ai.moye.backstage.response.NoticeResponse;
import com.trs.ai.moye.backstage.service.NoticeCenterService;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.data.model.dao.BusinessCategoryMapper;
import com.trs.ai.moye.data.model.request.MessagePushRecordRequest;
import com.trs.ai.moye.permission.service.CurrentUserService;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.request.PageParams;
import com.trs.moye.base.common.request.SearchParams;
import com.trs.moye.base.common.request.SortParams;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 消息配置中心实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/30 14:46
 **/
@Service
@Slf4j
public class NoticeCenterServiceImpl implements NoticeCenterService {

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private BusinessCategoryMapper businessCategoryMapper;

    @Resource
    private NoticeSendConfInsideMapper noticeSendConfInsideMapper;

    @Resource
    private NoticeSendConfHttpMapper noticeSendConfHttpMapper;

    @Resource
    private MessagePushRecordMapper messagePushRecordMapper;

    @Resource
    private NoticeMapper noticeMapper;

    @Resource
    private NoticeReadRelationMapper noticeReadRelationMapper;

    @Resource
    private CurrentUserService currentUserService;

    @Resource
    private DataModelMapper dataModelMapper;

    /**
     * 角色缓存
     */
    private final Map<Integer, String> roles = new HashMap<>();

    /**
     * 用户缓存
     */
    private final Map<Integer, String> users = new HashMap<>();

    /**
     * 业务层缓存
     */
    private final Map<Integer, String> category = new HashMap<>();

    @Override
    public PageResponse<InsideNoticeResponse> getInsideNoticeList(NoticeListRequest request) {
        SearchParams searchParams = request.getSearchParams();
        PageParams pageParams = request.getPageParams();
        Integer messageTypeId = request.getMessageTypeId();
        String createTypeName = request.getCreateTypeName();
        if (Objects.isNull(pageParams)) {
            throw new BizException("分页参数不能为空");
        }
        Page<NoticeSendConfInside> insideList = noticeSendConfInsideMapper.getNoticePageList(searchParams,
            messageTypeId, pageParams.toPage(), createTypeName);
        List<InsideNoticeResponse> insideNoticeResponse = buildInsideNoticeResponse(insideList.getRecords());
        // 封装分页数据
        PageResponse<InsideNoticeResponse> pageResponse = new PageResponse<>();
        pageResponse.setItems(insideNoticeResponse);
        pageResponse.setTotal(insideList.getTotal());
        pageResponse.setPageSize((int) insideList.getSize());
        pageResponse.setPageNum((int) insideList.getCurrent());
        return pageResponse;
    }

    private void cacheBaseData() {
        //缓存所有的角色
        roleMapper.selectAll().forEach(role -> roles.put(role.getId(), role.getName()));
        //缓存用户
        userMapper.selectAll().forEach(user -> users.put(user.getId(), user.getName()));
        //缓存所有的业务层
        businessCategoryMapper.getAllCategory().forEach(c -> category.put(c.getId(), c.getZhName()));
    }

    private List<InsideNoticeResponse> buildInsideNoticeResponse(List<NoticeSendConfInside> noticeSendConfInsides) {
        //先缓存基础信息
        cacheBaseData();
        return noticeSendConfInsides.stream().map(m -> new InsideNoticeResponse(m, users, roles, category)).toList();
    }


    private List<ApiNoticeResponse> buildHttpNoticeResponse(List<NoticeSendConfHttp> messageSendConfInsides) {
        //先缓存基础信息
        cacheBaseData();
        return messageSendConfInsides.stream().map(m -> new ApiNoticeResponse(m, users, roles, category)).toList();
    }


    @Override
    public PageResponse<ApiNoticeResponse> getApiMsgSendStrategyList(NoticeListRequest request) {
        SearchParams searchParams = request.getSearchParams();
        PageParams pageParams = request.getPageParams();
        Integer messageTypeId = request.getMessageTypeId();
        if (Objects.isNull(pageParams)) {
            throw new BizException("分页参数不能为空");
        }
        Page<NoticeSendConfHttp> apiList = noticeSendConfHttpMapper.getNoticePageList(searchParams, messageTypeId,
            pageParams.toPage());
        List<ApiNoticeResponse> insideNoticeResponse = buildHttpNoticeResponse(apiList.getRecords());
        // 封装分页数据
        PageResponse<ApiNoticeResponse> pageResponse = new PageResponse<>();
        pageResponse.setItems(insideNoticeResponse);
        pageResponse.setTotal(apiList.getTotal());
        pageResponse.setPageSize((int) apiList.getSize());
        pageResponse.setPageNum((int) apiList.getCurrent());
        return pageResponse;

    }


    @Override
    public PageResponse<NoticeResponse> getNoticeList(NoticeListRequest request) {
        PageParams pageParams = request.getPageParams();
        SearchParams searchParams = request.getSearchParams();
        SortParams sortParams = request.getSortParams();
        Integer messageTypeId = request.getMessageTypeId();
        Page<Notice> noticeList = noticeMapper.selectNoticeList(searchParams, messageTypeId, sortParams,
            pageParams.toPage());
        List<NoticeResponse> noticeResponses = noticeList.getRecords().stream().map(notice -> {
            NoticeResponse noticeResponse = new NoticeResponse();
            noticeResponse.setId(notice.getId());
            noticeResponse.setTitle(notice.getTitle());
            noticeResponse.setNoticeTypeCode(notice.getNoticeType());
            noticeResponse.setContent(notice.getContent());
            noticeResponse.setPublishTime(notice.getPublishTime());
            noticeResponse.setNoticeType(NoticeType.getNameByCode(notice.getNoticeType()));
            return noticeResponse;
        }).toList();
        return PageResponse.of(noticeResponses, (int) noticeList.getCurrent(), noticeList.getTotal(),
            (int) noticeList.getSize());
    }

    @Override
    public PageResponse<MessagePushRecordResponse> listPushRecords(MessagePushRecordRequest request) {
        if (request.getMessageType() != null) {
            request.setMessageType(NoticeType.getNameByCode(Integer.valueOf(request.getMessageType())));
        }
        if (request.getPushType() != null) {
            request.setPushType(NoticePushTypeEnum.getNameByCode(Integer.valueOf(request.getPushType())));
        }

        Page<MessagePushRecord> page = messagePushRecordMapper.getMessagePushRecordsByPage(request,
            request.getPageParams()
                .toPage());
        // 匹配 部门、角色、用户 名称
        Map<Integer, Role> id2Role = roleMapper.selectAllAsMap();
        Map<Integer, User> id2User = userMapper.selectAllAsMap();
        List<MessagePushRecordResponse> responses = page.getRecords().stream()
            .map(messagePushRecord -> MessagePushRecordResponse.from(messagePushRecord, id2Role, id2User))
            .toList();
        return PageResponse.of(responses, (int) page.getCurrent(), page.getTotal(), (int) page.getSize());
    }

    @Override
    public PageResponse<NoticeResponse> userList(NoticeListRequest request) {
        Integer userId = currentUserService.getUserId();
        Page<NoticeResponse> page = noticeMapper.userList(request, request.getPageParams().toPage(), userId);
        page.getRecords().forEach(this::fillNoticeResponseProperty);
        return PageResponse.of(page.getRecords(), request.getPageParams().getPageNum(), page.getTotal(),
            request.getPageParams().getPageSize());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void readAllNotice() {
        Integer userId = currentUserService.getUserId();
        noticeReadRelationMapper.readAll(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void readSingleNotice(Integer id) {
        Notice notice = noticeMapper.selectById(id);
        if (Objects.isNull(notice)) {
            throw new BizException("id:[" + id + "]的消息不存在！");
        }
        Integer userId = currentUserService.getUserId();
        NoticeReadRelation noticeReadRelation = noticeReadRelationMapper.selectByNoticeIdAndUserId(id, userId);
        if (Objects.isNull(noticeReadRelation)) {
            noticeReadRelation = new NoticeReadRelation(id, userId);
            noticeReadRelationMapper.insert(noticeReadRelation);
        } else {
            noticeReadRelation.setIsRead(true);
            noticeReadRelationMapper.updateById(noticeReadRelation);
        }
    }

    @Override
    public List<NoticeResponse> getAllUnread() {
        List<NoticeResponse> unreadAll = noticeMapper.getUnreadAll(currentUserService.getUserId());
        unreadAll.forEach(NoticeCenterServiceImpl::setNoticeType);
        return unreadAll;
    }

    private static void setNoticeType(NoticeResponse e) {
        e.setNoticeType(Objects.requireNonNull(NoticeType.fromCode(e.getNoticeTypeCode())).getLabel());
    }

    private void fillNoticeResponseProperty(NoticeResponse e) {
        setNoticeType(e);
        setModelLayer(e);
    }

    private void setModelLayer(NoticeResponse e) {
        if (Objects.nonNull(e) && Objects.nonNull(e.getDataModelId())) {
            Optional.ofNullable(
                dataModelMapper.selectById(e.getDataModelId())
            ).ifPresent(
                dataModel -> e.setLayer(dataModel.getLayer())
            );
        }
    }

}
