package com.trs.ai.moye.backstage.request;


import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 添加部门请求参数
 *
 * <AUTHOR>
 * @since 2024/9/25 10:22
 */
@Data
public class DepartmentAddRequest {

    /**
     * 父id
     */
    @NotNull(message = "父级id不能为空！")
    private Integer pid;

    /**
     * 部门名称
     */
    @Length(min = 0, max = 60)
    @NotBlank(message = "部门名称不能为空！")
    private String name;

}
