package com.trs.ai.moye.backstage.request;

import com.trs.ai.moye.backstage.response.exclusive.ExclusiveDataModelResponse;
import com.trs.moye.base.common.enums.TimeUnit;
import com.trs.moye.base.common.utils.DateTimeUtils.Formatter;
import java.util.function.Predicate;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 专属数据模型请求参数 最近时间枚举
 *
 * <AUTHOR>
 * @since 2025-01-13 20:31
 */
@Data
@NoArgsConstructor
public class ExclusiveDataModelListRequest {

    /**
     * 最近时间值
     */
    @NotNull(message = "最近时间值不能为空")
    private Integer latelyTimeValue;

    /**
     * 最近时间单位枚举
     */
    @NotNull(message = "最近时间单位不能为空")
    private TimeUnit latelyTimeUnit;

    /**
     * 数据建模名称
     */
    private String dataModelName;

    /**
     * 构建最近时间
     *
     * @return 最近时间
     */
    public String buildLatelyTime() {
        return latelyTimeUnit.getLatelyTime(latelyTimeValue)
            .format(Formatter.YYYY_MM_DD_HH_MM_SS.getDateTimeFormatter());
    }

    /**
     * 构建过滤条件
     *
     * @return 过滤条件
     */
    public Predicate<ExclusiveDataModelResponse> buildFilter() {
        return ObjectUtils.isEmpty(dataModelName) ? e -> true : e -> e.getName().contains(dataModelName);
    }
}
