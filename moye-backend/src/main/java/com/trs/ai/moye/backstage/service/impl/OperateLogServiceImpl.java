package com.trs.ai.moye.backstage.service.impl;

import com.trs.ai.moye.backstage.dao.OperateLogMapper;
import com.trs.ai.moye.backstage.request.OperateLogPageListRequest;
import com.trs.ai.moye.backstage.service.OperateLogService;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.log.operate.OperateLog;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025-03-06 15:40
 */
@Slf4j
@Service
public class OperateLogServiceImpl implements OperateLogService {

    @Resource
    private OperateLogMapper mapper;

    @Override
    public PageResponse<OperateLog> pageList(OperateLogPageListRequest request) {
        return PageResponse.of(mapper.pageList(request.getOperateType()
            , request.getOperateResult()
            , request.getTimeRange().getMinTimeStr()
            , request.getTimeRange().getMaxTimeStr()
            , request.getSearchParams()
            , request.getPageParams().toPage()));
    }
}
