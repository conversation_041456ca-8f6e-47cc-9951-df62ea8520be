package com.trs.ai.moye.backstage.request;


import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 部门修改参数
 *
 * <AUTHOR>
 * @since 2024/9/25 14:27
 */
@Data
public class DepartmentUpdateRequest {

    /**
     * 部门id
     */
    @NotNull(message = "id不能为空！")
    private Integer id;

    /**
     * 部门名称
     */
    @Length(min = 0, max = 60)
    @NotBlank(message = "部门名称不能为空！")
    private String name;


}
