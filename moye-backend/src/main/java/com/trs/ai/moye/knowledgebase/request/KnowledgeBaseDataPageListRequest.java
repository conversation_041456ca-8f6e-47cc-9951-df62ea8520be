package com.trs.ai.moye.knowledgebase.request;

import com.trs.moye.base.common.request.BaseRequestParams;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2024-11-19 17:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class KnowledgeBaseDataPageListRequest extends BaseRequestParams {

    @NotNull(message = "知识库id不能为空")
    private Integer baseId;

    /**
     * 查询所有字段
     *
     * @return boolean
     */
    public boolean isSearchAllField() {
        return ObjectUtils.isNotEmpty(searchParams)
            && ObjectUtils.isNotEmpty(searchParams.getFields())
            && searchParams.getFields().size() == 1
            && "*".equals(searchParams.getFields().get(0));
    }
}
