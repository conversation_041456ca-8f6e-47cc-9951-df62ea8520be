package com.trs.ai.moye.knowledgebase.response;

import com.trs.moye.base.knowledgebase.entity.KnowledgeBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-11-22 14:01
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class KnowledgeBaseListResponse extends KnowledgeBase {

    /**
     * 获取名称，前端展示用
     *
     * @return 名称
     */
    public String getName() {
        return getZhName();
    }

    public KnowledgeBaseListResponse(KnowledgeBase knowledgeBase) {
        setId(knowledgeBase.getId());
        setZhName(knowledgeBase.getZhName());
        setEnName(knowledgeBase.getEnName());
        setType(knowledgeBase.getType());
        setSysSource(knowledgeBase.isSysSource());
        setBackup(knowledgeBase.isBackup());
        setRecentBackupTime(knowledgeBase.getRecentBackupTime());
        setCreateBy(knowledgeBase.getCreateBy());
        setCreateTime(knowledgeBase.getCreateTime());
        setUpdateBy(knowledgeBase.getUpdateBy());
        setUpdateTime(knowledgeBase.getUpdateTime());
        setFields(knowledgeBase.getFields());
        setDesc(knowledgeBase.getDesc());
        setTags(knowledgeBase.getTags());
    }
}
