package com.trs.ai.moye.backstage.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.ai.moye.backstage.entity.ui.AppConfigParam;
import com.trs.ai.moye.backstage.enums.AppConfigType;
import com.trs.moye.base.common.typehandler.PolymorphismTypeHandler;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 前端配置
 *
 * <AUTHOR>
 * @since 2024-12-05 13:47
 */
@Data
@TableName(value = "app_config", autoResultMap = true)
@NoArgsConstructor
public class AppConfig {

    /**
     * 配置类型
     */
    @TableId(type = IdType.INPUT)
    private AppConfigType type;

    /**
     * 配置
     */
    @TableField(typeHandler = PolymorphismTypeHandler.class)
    private AppConfigParam config;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT, value = "create_by")
    protected Integer createBy;

    /**
     * 最近更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, value = "update_by")
    protected Integer updateBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT, value = "create_time")
    protected LocalDateTime createTime;

    /**
     * 最近更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, value = "update_time")
    protected LocalDateTime updateTime;
}
