package com.trs.ai.moye.storageengine.request.nebula;

import com.trs.ai.moye.storageengine.dto.nebula.DeleteModelTagEdgeFieldDto;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MultiValuedMap;

/**
 * <AUTHOR>
 * @since 2025-01-20 10:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class DeleteModelTagEdgeFieldsRequest extends BaseModelTagEdgeFieldsRequest {


    /**
     * 要添加的tag
     */
    private List<DeleteModelTagEdgeFieldDto> tag;
    /**
     * 要添加的edge
     */
    private List<DeleteModelTagEdgeFieldDto> edge;

    public DeleteModelTagEdgeFieldsRequest(Integer modelId
        , MultiValuedMap<String, String> updateTagFieldMap,
        MultiValuedMap<String, String> updateEdgeFieldMap) {
        setDataModelId(modelId);
        this.tag = buildModelTagEdgeFieldDtoList(updateTagFieldMap);
        this.edge = buildModelTagEdgeFieldDtoList(updateEdgeFieldMap);
    }

    private List<DeleteModelTagEdgeFieldDto> buildModelTagEdgeFieldDtoList(
        MultiValuedMap<String, String> updateTagFieldMap) {
        Set<String> keySet = updateTagFieldMap.keySet();
        List<DeleteModelTagEdgeFieldDto> dtoList = new ArrayList<>(keySet.size());
        for (String key : keySet) {
            dtoList.add(new DeleteModelTagEdgeFieldDto(key, new ArrayList<>(updateTagFieldMap.get(key))));
        }
        return dtoList;
    }
}
