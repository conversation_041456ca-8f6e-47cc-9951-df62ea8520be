package com.trs.ai.moye.backstage.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 消息表实体
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/11/4 11:35
 **/
@Data
@TableName(value = "notice")
public class Notice {

    /**
     * id主键
     */
    private Integer id;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息类型
     */
    private Integer noticeType;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 消息子类型
     */
    private String noticeSubType;
    /**
     * 消息子类型名称
     */
    private Integer dataModelId;

}
