package com.trs.ai.moye.backstage.controller;

import com.trs.ai.moye.backstage.service.AuthCertificateKerberosService;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2024/10/20
 **/

@RequestMapping("certificate/manager")
@RestController
public class AuthCertificateManagerController {

    @Resource
    private AuthCertificateKerberosService authCertificateKerberosService;

    /**
     * 上传认证文件
     * <a href="http://**************:3001/project/5419/interface/api/165175">上传</a>
     *
     * @param file krb5配置文件
     * @return 认证文件信息
     */
    @PostMapping("/upload")
    public String addCertificate(
        @RequestParam("file") MultipartFile file) {
        return authCertificateKerberosService.uploadFile2TempDir(file);
    }


}
