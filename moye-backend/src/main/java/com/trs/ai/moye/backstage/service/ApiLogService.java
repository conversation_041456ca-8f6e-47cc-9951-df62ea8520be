package com.trs.ai.moye.backstage.service;

import com.trs.ai.moye.backstage.request.ApiLogPageListRequest;
import com.trs.ai.moye.backstage.response.ApiLogTraceResponse;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.log.api.ApiLog;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-04 14:37
 */
public interface ApiLogService {

    /**
     * 分页列表
     *
     * @param request 请求参数
     * @return 配置
     */
    PageResponse<ApiLog> pageList(ApiLogPageListRequest request);

    /**
     * 日志执行链路
     *
     * @param id 日志记录id
     * @return 配置
     */
    List<ApiLogTraceResponse> getTracerList(Long id);
}
