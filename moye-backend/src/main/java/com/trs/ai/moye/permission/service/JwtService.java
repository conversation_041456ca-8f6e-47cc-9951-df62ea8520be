package com.trs.ai.moye.permission.service;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.trs.ai.moye.backstage.entity.User;
import com.trs.ai.moye.common.exception.UnauthorizedException;
import com.trs.ai.moye.permission.properties.SecurityProperties;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

/**
 * jwt密钥生成和检验
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class JwtService {

    @Resource
    private SecurityProperties securityProperties;

    private static final String BEARER_PREFIX = "Bearer ";

    private static final String AUTHORIZATION = "Authorization";
    /**
     * 登录时间字段
     */
    public static final String LOGIN_TIME = "lgt";


    /**
     * 创建token
     *
     * @param user      用户
     * @param loginTime 登录时间
     * @return token
     */
    public String generateToken(User user, LocalDateTime loginTime) {
        UserDetails userDetails = user.createUserDetails();
        //保存登录时间
        Map<String, Object> claimMap = Map.of(
            LOGIN_TIME,
            loginTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        return generateToken(claimMap, userDetails);
    }

    /**
     * 创建token
     *
     * @param extraClaims 额外参数
     * @param userDetails 用户信息
     * @return token
     */
    public String generateToken(Map<String, Object> extraClaims, UserDetails userDetails) {
        return buildToken(extraClaims, userDetails, securityProperties.getTokenExpirationMillis());
    }

    /**
     * 创建token
     *
     * @param extraClaims 额外参数
     * @param userDetails 用户信息
     * @param expiration  过期时间（ms）
     * @return token
     */
    private String buildToken(Map<String, Object> extraClaims, UserDetails userDetails, long expiration) {
        return JWT.create()
            // 设置发布者
            .withIssuer(userDetails.getUsername())
            // 设置过期时间
            .withExpiresAt(Instant.ofEpochMilli(System.currentTimeMillis() + expiration))
            // 不要把密码封装进去，不安全
            .withPayload(extraClaims)
            // 加密
            .sign(Algorithm.HMAC256(securityProperties.getJwtSecretKey()));
    }

    /**
     * 获取DecodedJWT
     *
     * @param token token
     * @return {@link DecodedJWT}
     */
    public DecodedJWT extractJwt(String token) {
        try {
            return JWT.require(Algorithm.HMAC256(securityProperties.getJwtSecretKey()))
                .build()
                .verify(token);
        } catch (Exception e) {
            throw new UnauthorizedException(e.getMessage(), e);
        }
    }

    /**
     * 从token中获取用户名
     *
     * @param token token
     * @return account
     */
    public String extractUsername(String token) {
        return extractJwt(token)
            .getIssuer();
    }

    /**
     * 从token中获取登录时间
     *
     * @param token token
     * @return login time
     */
    public Long extractLoginTime(String token) {
        return extractJwt(token).getClaim(LOGIN_TIME).asLong();
    }

    /**
     * 检验token是否合法
     *
     * @param token       token
     * @param userDetails 用户信息
     * @return 结果
     */
    public boolean isTokenValid(String token, UserDetails userDetails) {
        try {
            JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC256(securityProperties.getJwtSecretKey()))
                .withIssuer(userDetails.getUsername())
                .build();
            DecodedJWT jwt = jwtVerifier.verify(token);
            log.info("jwt info: {}", jwt);
            return true;
        } catch (Exception e) { // 如果 token 过期会报错 TokenExpiredException
            log.error("Token is invalid or expired", e);
            return false;
        }
    }

    /**
     * 提取token字符串
     *
     * @param request HttpServletRequest
     * @return token
     */
    public static String getJwtToken(HttpServletRequest request) {
        final String authHeader = request.getHeader(AUTHORIZATION);
        if (authHeader == null || !authHeader.startsWith(BEARER_PREFIX)) {
            return null;
        }
        return authHeader.replace(BEARER_PREFIX, "");
    }
}