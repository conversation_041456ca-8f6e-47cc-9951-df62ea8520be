package com.trs.ai.moye.backstage.controller;

import com.trs.ai.moye.backstage.service.SparkConfigService;
import com.trs.ai.moye.backstage.response.SparkConfigResponse;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * spark配置
 *
 * <AUTHOR>
 * @since 2024/12/10
 */
@RestController
@RequestMapping("/spark")
public class SparkConfigController {

    @Resource
    private SparkConfigService sparkConfigService;

    /**
     * 获取sparkConfig项
     *
     * @return sparkConfig
     */
    @GetMapping("/config")
    public List<SparkConfigResponse> getSparkConfig(){
        return sparkConfigService.getSparkConfig();
    }

}
