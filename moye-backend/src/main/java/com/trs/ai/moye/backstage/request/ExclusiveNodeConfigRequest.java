package com.trs.ai.moye.backstage.request;

import com.trs.ai.moye.backstage.entity.StreamExclusiveNodeConfig;
import java.util.Set;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-01-13 20:31
 */
@Data
@NoArgsConstructor
public class ExclusiveNodeConfigRequest {

    private Integer id;

    /**
     * 数据建模id列表
     **/
    @NotEmpty(message = "数据建模id列表不允许空")
    private Set<Integer> dataModelIds;

    /**
     * 期望节点数
     **/
    @NotNull(message = "期望节点数不允许空")
    @Min(value = 0, message = "期望节点数不允许小于1")
    private Integer expectNodeCount;

    /**
     * 优先级
     **/
    @NotNull(message = "优先级不允许空")
    private Integer priority;

    /**
     * 并发线程数, nacos上对此有默认值
     */
    @NotNull(message = "并发线程数不允许空")
    @Min(value = 1, message = "并发线程数不允许小于1")
    private Integer concurrentThreads;

    /**
     * 限流, 单位: 条数据/分钟
     */
    @Min(value = 1, message = "限流不允许小于1条数据/分钟")
    private Integer rateLimit;

    /**
     * 是否为通用节点配置
     *
     * @return true 表示通用节点配置，false 非通用节点配置
     */
    public boolean isCommonNodeConfig() {
        return dataModelIds != null && dataModelIds.size() == 1 && dataModelIds.contains(
            StreamExclusiveNodeConfig.COMMON_DATA_MODEL_ID);
    }
}
