package com.trs.ai.moye.knowledgebase.init;

import com.trs.ai.moye.init.async.AsyncInitialize;
import com.trs.ai.moye.knowledgebase.service.impl.KnowledgeBaseServiceImpl;
import com.trs.moye.base.knowledgebase.dao.KnowledgeBaseMapper;
import com.trs.moye.base.knowledgebase.entity.KnowledgeBase;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/**
 * 标签库打标能力初始化 在系统启动时，自动更新所有标签库的打标能力
 *
 * <AUTHOR>
 * @since 2025/04/08 18:38:32
 */
@Slf4j
@Component
public class TagLibMarkAbilityInitialize implements AsyncInitialize {

    @Resource
    private KnowledgeBaseMapper knowledgeBaseMapper;

    @Resource
    private KnowledgeBaseServiceImpl knowledgeBaseService;

    @Override
    public void initialize() {
        log.info("开始初始化标签库打标能力...");
        try {
            // 查询所有标签库
            List<KnowledgeBase> tagLibList = knowledgeBaseMapper.listByType(KnowledgeBaseType.TAG.name());
            if (ObjectUtils.isEmpty(tagLibList)) {
                log.info("未发现标签库，跳过标签库打标能力初始化");
                return;
            }

            log.info("发现{}个标签库，开始更新打标能力", tagLibList.size());
            int successCount = updateTagLibsMarkAbility(tagLibList);
            log.info("标签库打标能力初始化完成，成功更新{}个，失败{}个",
                successCount, tagLibList.size() - successCount);
        } catch (Exception e) {
            log.error("标签库打标能力初始化失败", e);
        }
    }

    /**
     * 更新所有标签库的打标能力
     *
     * @param tagLibList 标签库列表
     * @return 成功更新的标签库数量
     */
    private int updateTagLibsMarkAbility(List<KnowledgeBase> tagLibList) {
        int successCount = 0;
        for (KnowledgeBase tagLib : tagLibList) {
            try {
                // 更新标签库的打标能力
                knowledgeBaseService.updateTagLibMarkAbility(tagLib);
                successCount++;
                log.info("标签库[{}]打标能力更新成功", tagLib.getZhName());
            } catch (Exception e) {
                log.error("标签库[{}]打标能力更新失败: {}", tagLib.getZhName(), e.getMessage(), e);
            }
        }
        return successCount;
    }
}