package com.trs.ai.moye.backstage.entity.exclusive;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 摘要信息：专属节点分配结果的摘要信息
 *
 * <AUTHOR>
 * @since 2025-01-10 13:47
 */
@Data
@NoArgsConstructor
public class Summary {

    /**
     * 期望专属节点总数
     */
    private Integer expectExclusiveNodeCount;

    /**
     * 实际专属节点总数
     */
    private Integer actualExclusiveNodeCount;

    /**
     * 期望通用节点总数
     */
    private Integer expectCommonNodeCount;

    /**
     * 实际通用节点总数
     */
    private Integer actualCommonNodeCount;

    /**
     * 期望节点总数
     *
     * @return 期望节点总数
     */
    @JsonProperty
    public Integer getExpectNodeCount() {
        return expectCommonNodeCount + expectExclusiveNodeCount;
    }

    /**
     * 实际节点总数
     *
     * @return 实际节点总数
     */
    @JsonProperty
    public Integer getActualNodeCount() {
        return actualCommonNodeCount + actualExclusiveNodeCount;
    }
}
