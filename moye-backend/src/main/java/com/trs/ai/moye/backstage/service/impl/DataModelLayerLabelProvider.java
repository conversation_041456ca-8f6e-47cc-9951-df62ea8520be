package com.trs.ai.moye.backstage.service.impl;

import com.trs.ai.moye.backstage.entity.ui.ModelLayerAppConfigParam;
import com.trs.ai.moye.backstage.enums.AppConfigType;
import com.trs.ai.moye.backstage.service.AppConfigService;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.service.ModelLayerLabelProvider;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import javax.annotation.Resource;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * 数据建模分层标签提供程序
 *
 * <AUTHOR>
 * @since 2025/04/14 17:26:16
 */
@Service
public class DataModelLayerLabelProvider implements ModelLayerLabelProvider {

    @Resource
    private AppConfigService appConfigService;

    private static final Map<ModelLayer, Function<ModelLayerAppConfigParam, String>> LABEL_EXTRACTORS = Map.of(
        ModelLayer.ODS, ModelLayerAppConfigParam::getOdsName,
        ModelLayer.DWD, ModelLayerAppConfigParam::getDwdName,
        ModelLayer.THEME, ModelLayerAppConfigParam::getThemeName,
        ModelLayer.SUBJECT, ModelLayerAppConfigParam::getSubjectName,
        ModelLayer.INDICATOR, ModelLayerAppConfigParam::getIndicatorName
    );

    @Override
    @Cacheable(value = "moye:model-layer-names", key = "#layer.name()")
    public String getLabel(ModelLayer layer) {
        return Optional.ofNullable(appConfigService.getAppConfigParam(AppConfigType.MODEL_LAYER))
            .filter(ModelLayerAppConfigParam.class::isInstance)
            .map(ModelLayerAppConfigParam.class::cast)
            .flatMap(param -> Optional.ofNullable(LABEL_EXTRACTORS.get(layer))
                .map(extractor -> extractor.apply(param)))
            .orElse(null);
    }
}
