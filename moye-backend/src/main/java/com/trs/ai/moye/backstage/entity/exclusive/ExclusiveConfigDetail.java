package com.trs.ai.moye.backstage.entity.exclusive;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.trs.moye.base.common.response.IdNameResponse;
import java.util.LinkedList;
import java.util.Set;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 引擎配置列表：触发节点分配时的引擎配置及实际分配给其的实际节点
 *
 * <AUTHOR>
 * @since 2025-01-10 13:47
 */
@Data
@NoArgsConstructor
public class ExclusiveConfigDetail {

    /**
     * 数据模型ID列表，冗余字段
     */
    @JSONField(serialize = false, deserialize = false)
    private Set<Integer> dataModelIds;

    /**
     * 数据建模列表
     */
    @NotEmpty(message = "数据模型列表不能为空")
    private Set<IdNameResponse> dataModels;

    /**
     * 期望节点数
     */
    @NotNull(message = "期望节点数不能为空")
    private Integer expectNodeCount;

    /**
     * 分配的节点列表
     */
    @NotNull(message = "分配的节点列表不能为null")
    private LinkedList<String> actualNodes;

    /**
     * 实际节点数
     *
     * @return 实际节点数
     */
    @JsonProperty
    public int getActualNodeCount() {
        return actualNodes == null ? 0 : actualNodes.size();
    }

}
