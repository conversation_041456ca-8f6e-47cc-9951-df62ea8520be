package com.trs.ai.moye.backstage.service;

import com.trs.ai.moye.backstage.request.DepartmentAddRequest;

/**
 * 部门管理相关服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/25 10:29
 **/
public interface DepartmentService {


    /**
     * 添加部门
     *
     * @param request 前端请求
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/9/25 10:32
     */
    Integer add(DepartmentAddRequest request);


    /**
     * 部门删除
     *
     * @param id 部门ID
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/9/25 14:43
     */
    Boolean delete(Integer id);


    /**
     * 判断是否可以删除
     *
     * @param id 部门ID
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/9/25 15:47
     */
    Boolean deleteAble(Integer id);
}
