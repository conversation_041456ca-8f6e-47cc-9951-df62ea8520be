package com.trs.ai.moye.backstage.service.impl;

import com.trs.ai.moye.backstage.dao.DepartmentMapper;
import com.trs.ai.moye.backstage.dao.UserMapper;
import com.trs.ai.moye.backstage.entity.Department;
import com.trs.ai.moye.backstage.request.DepartmentAddRequest;
import com.trs.ai.moye.backstage.service.DepartmentService;
import com.trs.moye.base.common.exception.BizException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 部门服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/25 10:29
 **/

@Service
@Slf4j
public class DepartmentServiceImpl implements DepartmentService {

    @Resource
    private DepartmentMapper departmentsMapper;


    @Resource
    private UserMapper userMapper;

    /**
     * 部门最大层级
     */
    private static final int DEPARTMENT_MAX_LEVEL = 10;

    /**
     * 层级
     */
    int level = 0;

    @Override
    public Integer add(DepartmentAddRequest request) {
        if (Boolean.TRUE.equals(departmentsMapper.existsByName(request.getName()))) {
            throw new BizException("部门名称重复！");
        } else {
            int i = checkLevel(request.getPid());
            level = 0;
            if (i > DEPARTMENT_MAX_LEVEL) {
                throw new BizException("部门不可超过10级！");
            } else {
                return departmentsMapper.insert(Department.fromAdd(request));
            }
        }
    }


    private int checkLevel(Integer pid) {
        List<Department> departments = departmentsMapper.selectList(null);
        countParentDepartments(departments, pid);
        return level + 1;
    }

    private void countParentDepartments(List<Department> departments, int pid) {
        for (Department department : departments) {
            if (department.getId() == pid) {
                level++;
                countParentDepartments(departments, department.getPid());
            }
        }
    }


    @Override
    public Boolean delete(Integer id) {
        //该部门以及子部门所有的ID
        Set<Integer> departmentIdAndChildDepartmentIds = findDepartmentIdAndChildDepartmentIds(id);
        if (userMapper.countDepartmentByDepartmentId(departmentIdAndChildDepartmentIds) == 0) {
            return departmentsMapper.deleteByIds(departmentIdAndChildDepartmentIds) > 0;
        } else {
            throw new BizException("注意：该部门或其下级部门仍存在人员（用户），不可删除！");
        }
    }


    /**
     * 找到要删除的部门的id和其子部门的id
     *
     * @param departmentId 部门ID
     * @return {@link Integer}
     * <AUTHOR>
     * @since 2024/9/25 14:46
     */
    private Set<Integer> findDepartmentIdAndChildDepartmentIds(Integer departmentId) {
        List<Department> childDepartment = new ArrayList<>();
        findChildDepartments(departmentsMapper.selectList(null), departmentId, childDepartment);
        Set<Integer> departmentIds = childDepartment.stream().map(Department::getId).collect(Collectors.toSet());
        departmentIds.add(departmentId);
        return departmentIds;
    }

    /**
     * 递归找出该部门下所有子部门信息
     *
     * @param departments     系统所有部门
     * @param departmentId    要删除的部门id
     * @param childDepartment 存放子部门
     * <AUTHOR>
     * @since 2024/9/25 14:46
     */
    private void findChildDepartments(List<Department> departments, int departmentId,
        List<Department> childDepartment) {
        for (Department department : departments) {
            if (department.getPid() == departmentId) {
                findChildDepartments(departments, department.getId(), childDepartment);
                childDepartment.add(department);
            }
        }
    }

    @Override
    public Boolean deleteAble(Integer id) {
        Set<Integer> departmentIdAndChildDepartmentIds = findDepartmentIdAndChildDepartmentIds(id);
        return userMapper.countDepartmentByDepartmentId(departmentIdAndChildDepartmentIds) == 0;
    }
}
