package com.trs.ai.moye.backstage.service.impl;

import com.trs.ai.moye.backstage.config.SparkConfig;
import com.trs.ai.moye.backstage.response.SparkConfigResponse;
import com.trs.ai.moye.backstage.service.SparkConfigService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * spark service
 *
 * <AUTHOR>
 * @since 2024/12/10
 */
@Service
public class SparkConfigServiceImpl implements SparkConfigService {

    @Resource
    private SparkConfig sparkConfig;

    @Override
    public List<SparkConfigResponse> getSparkConfig() {
        return sparkConfig.getSparkConfig();
    }
}
