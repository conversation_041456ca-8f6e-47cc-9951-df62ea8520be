package com.trs.ai.moye.knowledgebase.service.impl;

import static com.trs.ai.moye.knowledgebase.constant.Constants.MSG_KNOWLEDGE_BASE_NOT_EXIST;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.ai.moye.knowledgebase.constant.Constants;
import com.trs.ai.moye.knowledgebase.dao.dynamicrepository.KnowledgeBaseDataMapper;
import com.trs.moye.base.knowledgebase.dao.KnowledgeBaseFieldMapper;
import com.trs.moye.base.knowledgebase.dao.KnowledgeBaseMapper;
import com.trs.moye.base.knowledgebase.entity.KnowledgeBase;
import com.trs.moye.base.knowledgebase.entity.KnowledgeBaseField;
import com.trs.moye.base.knowledgebase.entity.TableInsertDataParam;
import com.trs.ai.moye.knowledgebase.request.BaseDataBatchDeleteRequest;
import com.trs.ai.moye.knowledgebase.request.KnowledgeBaseDataPageListRequest;
import com.trs.ai.moye.knowledgebase.request.KnowledgeBaseDataRequest;
import com.trs.ai.moye.knowledgebase.service.KnowledgeBaseDataService;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.request.SearchParams;
import com.trs.moye.base.common.utils.AssertUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-21 23:24
 */
@Slf4j
@Service
public class KnowledgeBaseDataServiceImpl implements KnowledgeBaseDataService {

    private static final String MSG_NOT_SUPPORT_OPERATE_DATA = "【%s】类型的知识库没有物理表，不支持进行知识库库表数据的操作";

    @Resource
    private KnowledgeBaseMapper knowledgeBaseMapper;

    @Resource
    private KnowledgeBaseFieldMapper knowledgeBaseFieldMapper;

    @Resource
    private KnowledgeBaseDataMapper knowledgeBaseDataMapper;

    @Override
    public int addKnowledgeBaseData(KnowledgeBaseDataRequest request) {
        String tableName = checkProcessParamAndReturnTableName(request);
        TableInsertDataParam param = new TableInsertDataParam(tableName, request.getFieldAndValueList());
        knowledgeBaseDataMapper.insertTableData(param);
        return param.getId();
    }

    private String checkProcessParamAndReturnTableName(KnowledgeBaseDataRequest request) {
        return checkProcessParamAndReturnTableName(null, request);
    }

    private String checkProcessParamAndReturnTableName(Integer dataId, KnowledgeBaseDataRequest request) {
        KnowledgeBase base = getSupportOperateDataKnowledgeBase(request.getBaseId());
        List<KnowledgeBaseField> fieldList = knowledgeBaseFieldMapper.listByBaseId(request.getBaseId());
        // 验证提交的知识库数据
        request.processFieldValues(base.getZhName(), fieldList);
        // 验证唯一字段
        List<KnowledgeBaseField> uniqueFieldList = fieldList.stream().filter(KnowledgeBaseField::isUnique).toList();
        String tableName = base.buildTableName();
        if (ObjectUtils.isNotEmpty(uniqueFieldList)) {
            checkUnique(tableName, dataId, uniqueFieldList, request.toMap());
        }
        return tableName;
    }

    private KnowledgeBase getSupportOperateDataKnowledgeBase(Integer baseId) {
        // 验证知识库
        KnowledgeBase base = knowledgeBaseMapper.selectById(baseId);
        AssertUtils.notEmpty(base, MSG_KNOWLEDGE_BASE_NOT_EXIST, baseId);
        if (!base.getType().isNeedPhysicsTable()) {
            throw new BizException(MSG_NOT_SUPPORT_OPERATE_DATA, base.getType().name());
        }
        return base;
    }

    private void checkUnique(String tableName, @Nullable Integer dataId, List<KnowledgeBaseField> uniqueFieldList,
        Map<String, Object> data) {
        for (KnowledgeBaseField uniqueField : uniqueFieldList) {
            Object fieldValue = data.get(uniqueField.getEnName());
            AssertUtils.notEmpty(fieldValue, "唯一属性【%s】的属性值不能为空", uniqueField.getZhName());
            Map<String, Object> dbData = knowledgeBaseDataMapper.selectOneByColNameAndValue(tableName,
                uniqueField.getEnName(), fieldValue);
            if (Objects.nonNull(dbData) && !dbData.get("id").equals(dataId)) {
                throw new BizException("唯一属性【%s】的属性值【%s】已存在", uniqueField.getZhName(), fieldValue);
            }
        }
    }

    @Override
    public void updateKnowledgeBaseData(Integer dataId, KnowledgeBaseDataRequest request) {
        String tableName = checkProcessParamAndReturnTableName(dataId, request);
        try {
            knowledgeBaseDataMapper.updateTableData(
                tableName,
                dataId,
                request.getFieldAndValueList());
        } catch (Exception e) {
            throw new BizException("修改数据失败!!!", e);
        }
    }

    @Override
    public void deleteKnowledgeBaseData(Integer dataId, Integer baseId) {
        // 验证知识库
        KnowledgeBase base = getSupportOperateDataKnowledgeBase(baseId);
        String tableName = base.buildTableName();
        knowledgeBaseDataMapper.deleteTableData(tableName, dataId);
    }

    @Override
    public void batchDelete(BaseDataBatchDeleteRequest request) {
        KnowledgeBase base = getSupportOperateDataKnowledgeBase(request.getBaseId());
        String tableName = base.buildTableName();
        knowledgeBaseDataMapper.deleteTableDataByIds(tableName, request.getDataIds());
    }

    @Override
    public PageResponse<Map<String, Object>> getKnowledgeBaseDataPageList(KnowledgeBaseDataPageListRequest request) {
        KnowledgeBase base = getSupportOperateDataKnowledgeBase(request.getBaseId());
        SearchParams searchParams = request.getSearchParams();
        if (request.isSearchAllField()) {
            searchParams.setFields(
                knowledgeBaseFieldMapper.listByBaseId(request.getBaseId()).stream()
                    .filter(field -> !Constants.NOT_SUPPORT_FUZZY_SEARCH_FIELD_SET.contains(field.getType()))
                    .map(KnowledgeBaseField::getEnName)
                    .toList());
        }
        String tableName = base.buildTableName();
        Page<Map<String, Object>> page = knowledgeBaseDataMapper.selectTableDataList(
            tableName, request.getSearchParams(),
            request.getSortParams(), request.getPageParams().toPage());
        return PageResponse.of(page);
    }
}
