package com.trs.ai.moye.backstage.service;

import com.trs.ai.moye.backstage.request.AuthCertificateKerberosInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2024/10/18
 */
public interface AuthCertificateKerberosService {

    /**
     * 上传文件并保存kerberos信息
     *
     * @param file krb5文件
     * @return kerberos信息
     */
    String uploadFile2TempDir(MultipartFile file);


    /**
     * 保存kerberos信息
     *
     * @param authCertificateKerberosTempInfo kerberos信息
     * @param roleId                          角色id
     * @return kerberos信息id
     */
    Integer saveKerberosFileFromTempDir(AuthCertificateKerberosInfo authCertificateKerberosTempInfo, Integer roleId);

    /**
     * 删除kerberos信息
     *
     * @param id kerberos信息id
     */
    void deleteById(Integer id);
}
