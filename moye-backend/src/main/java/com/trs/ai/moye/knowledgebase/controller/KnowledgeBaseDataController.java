package com.trs.ai.moye.knowledgebase.controller;

import com.trs.moye.base.common.response.PageResponse;
import com.trs.ai.moye.knowledgebase.request.BaseDataBatchDeleteRequest;
import com.trs.ai.moye.knowledgebase.request.KnowledgeBaseDataPageListRequest;
import com.trs.ai.moye.knowledgebase.request.KnowledgeBaseDataRequest;
import com.trs.ai.moye.knowledgebase.service.KnowledgeBaseDataService;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * V4可能要用 暂时不删除
 *
 * <AUTHOR>
 * @since 2020/6/8 09:59
 */
@RestController()
@RequestMapping("/knowledge-base/data")
@Validated
public class KnowledgeBaseDataController {

    @Resource
    private KnowledgeBaseDataService knowledgeBaseDataService;

    /**
     * 添加知识库数据
     *
     * @param request 请求参数
     * @return 知识库数据id
     */
    @PostMapping
    public int addKnowledgeBaseData(@Validated @RequestBody KnowledgeBaseDataRequest request) {
        return knowledgeBaseDataService.addKnowledgeBaseData(request);
    }

    /**
     * 更新知识库数据
     *
     * @param dataId  知识库数据id
     * @param request 请求参数
     */
    @PutMapping("/{dataId}")
    public void updateKnowledgeBaseData(@PathVariable Integer dataId, @Validated @RequestBody KnowledgeBaseDataRequest request) {
        knowledgeBaseDataService.updateKnowledgeBaseData(dataId, request);
    }

    /**
     * 删除知识库数据
     *
     * @param dataId 知识库数据id
     * @param baseId 知识库id
     */
    @DeleteMapping("/{dataId}")
    public void deleteKnowledgeBaseData(@PathVariable Integer dataId, @RequestParam Integer baseId) {
        knowledgeBaseDataService.deleteKnowledgeBaseData(dataId, baseId);
    }

    /**
     * 批量删除知识库数据
     *
     * @param request 请求参数
     */
    @DeleteMapping("/batch-delete")
    public void batchDelete(@Validated @RequestBody BaseDataBatchDeleteRequest request) {
        knowledgeBaseDataService.batchDelete(request);
    }

    /**
     * 获取知识库数据分页列表
     *
     * @param request 请求参数
     * @return 知识库数据分页列表
     */
    @PostMapping("/page-list")
    public PageResponse<Map<String, Object>> getKnowledgeBaseDataPageList(
        @Validated @RequestBody KnowledgeBaseDataPageListRequest request) {
        return knowledgeBaseDataService.getKnowledgeBaseDataPageList(request);
    }
}
