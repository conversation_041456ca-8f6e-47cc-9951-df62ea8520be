package com.trs.ai.moye.knowledgebase.request.external;

import com.trs.moye.base.common.request.BaseRequestParams;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024-11-25 15:14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExternalPageListRequest extends BaseRequestParams {

    @NotNull(message = "知识库类型不能为空")
    private KnowledgeBaseType type;

    @NotBlank(message = "知识库英文名称不能为空")
    private String enName;
}
