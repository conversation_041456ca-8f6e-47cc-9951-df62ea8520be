package com.trs.ai.moye.storageengine.request;

import com.trs.moye.base.data.source.setting.file.FileTypeConfig;
import com.trs.moye.base.data.source.setting.file.chosen.FileChosenConfig;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-10-08 14:07
 */
@Data
@NoArgsConstructor
public abstract class FileBaseRequest {

    /**
     * 工作目录
     */
    @NotBlank(message = "工作目录不能为空")
    protected String workingDirectory;

    /**
     * 文件类型配置
     */
    @Valid
    @NotNull(message = "文件类型配置不能为空")
    protected FileTypeConfig fileTypeConfig;

    /**
     * 文件选择配置
     */
    @Valid
    @NotNull(message = "文件选择配置不能为空")
    protected FileChosenConfig fileChosenConfig;
}
