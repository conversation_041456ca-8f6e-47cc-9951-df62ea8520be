package com.trs.ai.moye.backstage.response;

import com.trs.ai.moye.backstage.entity.Role;
import com.trs.ai.moye.backstage.enums.RoleType;
import com.trs.ai.moye.common.response.AuditBaseResponse;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/18
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RoleResponse extends AuditBaseResponse<Role> {

    /**
     * 角色名称
     */
    private String name;

    /**
     * 角色类型：1表示超管，2表示管理员，3表示用户定义的角色
     */
    private RoleType type;

    /**
     * 用户的权限列表(给前端显示用)
     */
    private List<String> operations;

    /**
     * 凭证字段
     */
    private List<AuthCertificateKerberosResponse> credentials;

    @Override
    protected void populateProperties(Role entity) {
        this.name = entity.getName();
        this.type = entity.getType();
        this.operations = entity.getOperations();
    }

    /**
     * 根据{@link Role} 构造response
     *
     * @param entity {@link Role}
     */
    public RoleResponse(Role entity) {
        super.populateFrom(entity);
    }

}
