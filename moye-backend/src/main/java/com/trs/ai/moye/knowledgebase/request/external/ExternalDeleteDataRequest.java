package com.trs.ai.moye.knowledgebase.request.external;

import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-11-20 17:55
 */
@Data
@NoArgsConstructor
public class ExternalDeleteDataRequest {

    @NotNull(message = "知识库类型不能为空")
    private KnowledgeBaseType type;

    @NotBlank(message = "实体名称不能为空")
    private String enName;

    @NotEmpty(message = "dataIds不能为空")
    private List<Integer> dataIds;
}
