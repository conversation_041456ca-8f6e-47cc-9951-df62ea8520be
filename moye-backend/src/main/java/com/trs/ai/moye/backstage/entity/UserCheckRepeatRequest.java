package com.trs.ai.moye.backstage.entity;

import com.trs.ai.moye.backstage.request.UserRequest;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户检查重复参数
 *
 * <AUTHOR>
 * @since 2024-09-25 14:54
 */
@Data
@NoArgsConstructor
public class UserCheckRepeatRequest {

    /**
     * 账号
     */
    private String account;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 邮箱
     */
    private String email;

    public UserCheckRepeatRequest(UserRequest request){
        this.account = request.getAccount();
        this.telephone = request.getTelephone();
        this.email = request.getEmail();
    }
}
