package com.trs.ai.moye.permission.service;

import com.trs.ai.moye.backstage.dao.UserMapper;
import javax.annotation.Resource;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;


/**
 * 用户名称动态加载服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/24 11:52
 **/
@Service
public class DynamicUserNameService {

    @Resource
    private UserMapper userMapper;

    /**
     * 通过用户名ID获取名称
     *
     * @param userId 用户ID
     * @return {@link String}
     * <AUTHOR>
     * @since 2024/9/24 13:45
     */
    @Cacheable(value = "user:name", key ="'userId-' + #userId")
    public String getUserName(Integer userId) {
        return userMapper.selectUserNameById(userId);
    }

}
