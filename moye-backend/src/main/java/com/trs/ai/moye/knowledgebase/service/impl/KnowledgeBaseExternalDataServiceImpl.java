package com.trs.ai.moye.knowledgebase.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.knowledgebase.dao.dynamicrepository.KnowledgeBaseDataMapper;
import com.trs.ai.moye.knowledgebase.request.external.ExternalAttrDataRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalConditionRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalDataRankRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalMediaRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalMultiSearchRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalPageListRequest;
import com.trs.ai.moye.knowledgebase.service.KnowledgeBaseExternalDataService;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.knowledgebase.dao.KnowledgeBaseMapper;
import com.trs.moye.base.knowledgebase.entity.EntityColumn;
import com.trs.moye.base.knowledgebase.entity.KnowledgeBase;
import com.trs.moye.base.knowledgebase.entity.TableInsertDataParam;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024-11-25 16:29
 */
@Slf4j
@Service
public class KnowledgeBaseExternalDataServiceImpl implements KnowledgeBaseExternalDataService {

    @Resource
    private KnowledgeBaseMapper knowledgeBaseMapper;

    @Resource
    private KnowledgeBaseDataMapper knowledgeBaseDataMapper;

    /**
     * 获取知识库数据分页列表
     *
     * @param request 请求参数
     * @return 知识库数据
     */
    @Override
    public PageResponse<Map<String, Object>> getPageList(ExternalPageListRequest request) {
        KnowledgeBase base = getKnowledgeBase(request.getType(), request.getEnName());
        Page<Map<String, Object>> page = knowledgeBaseDataMapper.selectTableDataList(
            base.buildTableName(), request.getSearchParams(), request.getSortParams(), request.getPageParams().toPage());
        return PageResponse.of(page);
    }

    @Override
    public PageResponse<Map<String, Object>> getConditionPageList(ExternalConditionRequest request) {
        KnowledgeBase base = getKnowledgeBase(request.getType(), request.getEnName());
        Page<Map<String, Object>> page = knowledgeBaseDataMapper.getConditionPageList(
            base.buildTableName(), request.getConditions(), request.getSortParams(), request.getPageParams().toPage());
        return PageResponse.of(page);
    }

    private KnowledgeBase getKnowledgeBase(KnowledgeBaseType type, String enName) {
        KnowledgeBase base = knowledgeBaseMapper.getByTypeName(type.name(), enName,
            false);
        AssertUtils.notEmpty(base, "类型为【%s】英文名为【%s】的知识库不存在", type, enName);
        if (!base.getType().isNeedPhysicsTable()) {
            throw new BizException("【%s】类型的知识库不支持操作数据");
        }
        return base;
    }

    @Override
    public PageResponse<Map<String, Object>> getMultiSearchPageList(ExternalMultiSearchRequest request) {
        KnowledgeBase base = getKnowledgeBase(request.getType(), request.getEnName());
        Page<Map<String, Object>> page = knowledgeBaseDataMapper.getMultiSearchPageList(
            base.buildTableName(), request.getMultiSearchable(), request.getSortParams(),
            request.getPageParams().toPage());
        return PageResponse.of(page);
    }

    @Override
    public int getDataCount(KnowledgeBaseType type, String enName) {
        KnowledgeBase base = getKnowledgeBase(type, enName);
        return knowledgeBaseDataMapper.selectBaseDataCount(base.buildTableName());
    }

    @Override
    public List<String> getAttrData(ExternalAttrDataRequest request) {
        KnowledgeBase base = getKnowledgeBase(request.getType(), request.getEnName());
        return knowledgeBaseDataMapper.selectAttrData(base.buildTableName(), request.getAttrName(),
            request.getKeyword(),
            request.getSituation(), request.getSortParams());
    }

    @Override
    public List<String> getMediaDataList(ExternalMediaRequest request) {
        KnowledgeBase base = getKnowledgeBase(request.getType(), request.getEnName());
        return knowledgeBaseDataMapper.selectAccountOfMediaFieldValue(base.buildTableName(), request.getSituation(),
            request.getNature(), request.getLevel(), request.getRegion());
    }

    @Override
    public List<String> getDataRank(ExternalDataRankRequest request) {
        KnowledgeBase base = getKnowledgeBase(request.getType(), request.getEnName());
        return knowledgeBaseDataMapper.selectDataRank(base.buildTableName(), request.getSearchField(),
            request.getKeywords(), request.getSortParams());
    }

    /**
     * 删除数据
     *
     * @param type   知识库类型
     * @param enName 知识库英文名
     * @param dataIds 数据ID
     */
    @Override
    public void deleteData(KnowledgeBaseType type, String enName, List<Integer> dataIds) {
        KnowledgeBase base = getKnowledgeBase(type, enName);
        knowledgeBaseDataMapper.deleteTableDataByIds(base.buildTableName(), dataIds);
    }

    @Override
    public long addData(KnowledgeBaseType type, String enName, List<EntityColumn> fieldValueList) {
        KnowledgeBase base = getKnowledgeBase(type, enName);
        TableInsertDataParam param = new TableInsertDataParam(base.buildTableName(), fieldValueList);
        knowledgeBaseDataMapper.insertTableData(param);
        return param.getId();
    }

    /**
     * 更新数据
     *
     * @param type           知识库类型
     * @param enName         知识库英文名
     * @param dataId         数据ID
     * @param fieldValueList 字段值列表
     */
    @Override
    public void updateData(KnowledgeBaseType type, String enName, Integer dataId, List<EntityColumn> fieldValueList) {
        KnowledgeBase base = getKnowledgeBase(type, enName);
        try {
            knowledgeBaseDataMapper.updateTableData(
                base.buildTableName(),
                dataId,
                fieldValueList);
        } catch (Exception e) {
            throw new BizException("修改数据失败!!!", e);
        }
    }
}
