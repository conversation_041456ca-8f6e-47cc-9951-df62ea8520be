package com.trs.ai.moye.backstage.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.moye.base.common.log.api.ApiLogTrace;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2025-03-04 14:48
 */
@DS("clickhouse")
@Mapper
public interface ApiLogTraceMapper extends BaseMapper<ApiLogTrace> {

    /**
     * 根据日志ID查询跟踪信息
     *
     * @param logId 日志ID
     * @return 跟踪信息列表
     */
    List<ApiLogTrace> selectByLogId(@Param("logId") Long logId);
}
