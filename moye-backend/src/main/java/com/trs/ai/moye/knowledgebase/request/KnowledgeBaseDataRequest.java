package com.trs.ai.moye.knowledgebase.request;

import com.trs.moye.base.knowledgebase.entity.EntityColumn;
import com.trs.moye.base.knowledgebase.entity.KnowledgeBaseField;
import com.trs.moye.base.common.annotaion.validation.NotRepeat;
import com.trs.moye.base.common.utils.AssertUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2024-11-20 16:47
 */
@Data
@NoArgsConstructor
public class KnowledgeBaseDataRequest {

    @NotNull(message = "知识库id不能为空")
    private Integer baseId;

    @NotRepeat(message = "存在字段名重复", fields = "name:字段名")
    @Valid
    @NotEmpty(message = "字段和字段值列表不能为空")
    List<EntityColumn> fieldAndValueList;

    /**
     * 处理字段值
     *
     * @param baseName      知识库名称
     * @param baseFieldList 知识库字段列表
     */
    public void processFieldValues(String baseName, List<KnowledgeBaseField> baseFieldList) {
        Map<String, KnowledgeBaseField> fieldMap = baseFieldList.stream()
            .collect(Collectors.toMap(KnowledgeBaseField::getEnName, Function.identity()));
        for (EntityColumn entityColumn : fieldAndValueList) {
            KnowledgeBaseField field = fieldMap.get(entityColumn.getName());
            AssertUtils.notEmpty(field, "【%s】知识库不存在名称为【%s】的属性", baseName, entityColumn.getName());
            entityColumn.processValue(field.getType());
        }
    }

    /**
     * 数据转换为map
     *
     * @return map
     */
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (ObjectUtils.isEmpty(fieldAndValueList)) {
            return map;
        }
        for (EntityColumn entityColumn : fieldAndValueList) {
            map.put(entityColumn.getName(), entityColumn.getValue());
        }
        return map;
    }
}
