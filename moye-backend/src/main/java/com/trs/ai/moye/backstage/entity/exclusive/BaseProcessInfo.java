package com.trs.ai.moye.backstage.entity.exclusive;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-01-14 10:09
 */
@Data
@NoArgsConstructor
public class BaseProcessInfo {

    /**
     * 接入量
     */
    protected Integer accessCount;

    /**
     * 处理时间，单位：毫秒
     */
    protected Long processingTimeMillis;

    /**
     * 处理量
     */
    protected Integer processCount;

    /**
     * 处理出错量
     */
    protected Integer processErrorCount;

    /**
     * 平均处理时间，单位：毫秒
     *
     * @return 平均处理时间，单位：毫秒
     */
    @JsonProperty
    public Integer getAvgProcessTimeMillis() {
        return (processCount == null || processCount == 0) ? 0 : (int) (processingTimeMillis / processCount);
    }
}
