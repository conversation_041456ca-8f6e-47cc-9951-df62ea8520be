package com.trs.ai.moye.backstage.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.ai.moye.data.operator.domain.vo.AbilityDTO;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * http配置发送实体
 *
 * <AUTHOR>
 * @since 2024/10/31 14:14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "notice_send_conf_http",autoResultMap = true)
public class NoticeSendConfHttp extends AuditBaseEntity {

    /**
     * 策略名称
     */
    private String name;


    /**
     * 推送的角色ID列表
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private List<Integer> sendRoleIds;

    /**
     * 推送的用户ID列表  [1,2,3]
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private List<Integer> sendUserIds;

    /**
     * 消息类型ID
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private List<Integer> messageTypeIds;

    /**
     * 业务ID
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private List<Integer> businessIds;

    /**
     * 启用状态：1启用，0不启用
     */
    private Boolean state;


    /**
     * 发送服务的api信息
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private AbilityDTO apiInfo;


    /**
     * 发送参数
     */
    private String sendParameter;


}