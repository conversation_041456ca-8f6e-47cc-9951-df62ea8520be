package com.trs.ai.moye.permission.controller;

import com.trs.moye.base.common.response.ResponseMessage;
import com.trs.ai.moye.permission.request.UserLoginRequest;
import com.trs.ai.moye.permission.response.LoginResponse;
import com.trs.ai.moye.permission.service.CaptchaService;
import com.trs.ai.moye.permission.service.LoginService;
import java.io.IOException;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户登录、登出
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/6/1
 */
@Slf4j
@RestController
public class LoginController {

    @Resource
    private CaptchaService captchaService;

    @Resource
    private LoginService loginService;


    /**
     * 登录
     *
     * @param userLoginRequest 请求参数
     * @param request          {@link HttpServletRequest}
     * @return {@link LoginResponse}
     */
    @PostMapping("/auth/login")
    public LoginResponse login(@RequestBody UserLoginRequest userLoginRequest, HttpServletRequest request) {
        return loginService.login(userLoginRequest, request);
    }

    /**
     * 用户登出 <a href="http://192.168.210.40:3001/project/5419/interface/api/163590">...</a>
     *
     * @param request {@link HttpServletRequest}
     */
    @GetMapping(value = "/auth/logout")
    public void doLogout(HttpServletRequest request) {
        loginService.doLogout(request);
    }

    /**
     * 获得新的验证码图片 <a href="http://192.168.210.40:3001/project/5419/interface/api/163605">...</a>
     *
     * @param request  {@link HttpServletRequest}
     * @param response {@link HttpServletResponse}
     */
    @GetMapping("/auth/code")
    public void setNewAuthCode(HttpServletRequest request, HttpServletResponse response) throws IOException {
        captchaService.authCodeService(request, response);
    }

    /**
     * 无权限时重定向登陆，返回401和错误信息
     *
     * @return void
     * <AUTHOR>
     * @since 2020/10/19 20:42
     */
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @GetMapping("/unauthorized")
    public ResponseMessage unauthorized() {
        return ResponseMessage.unauthorized("登录失败");
    }

    /**
     * 更新密码，以bcrypt加密
     */
    @GetMapping("/auth/bcrypt")
    public void updatePasswordToBcrypt() {
        loginService.updatePasswordToBcrypt();
    }
}
