package com.trs.ai.moye.storageengine.request;

import com.trs.moye.base.data.model.entity.DataModelField;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NebulaAddTagEdgeRequest
 *
 * <AUTHOR>
 * @since 2025/1/20 18:38
 */
@Data
@Valid
@NoArgsConstructor
@AllArgsConstructor
public class NebulaAddTagEdgeRequest {

    /**
     * 连接id
     */
    @NotEmpty
    private List<Integer> connectionIds;

    /**
     * 用data model field形式描述tag/edge
     */
    @NotEmpty
    private List<DataModelField> fields;

}
