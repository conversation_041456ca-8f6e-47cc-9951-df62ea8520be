package com.trs.ai.moye.batchengine.entity;

import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 执行结果
 *
 * <AUTHOR>
 * @since  2024/11/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExecuteResultMap {

    /**
     * 结果
     */
    private List<Map<String, Object>> result;

    /**
     * 结果1,结果2...
     */
    private Integer part;

    /**
     * 总数
     */
    private Long total;

    /**
     * 执行id
     */
    private String executeId;

 }
