package com.trs.ai.moye.backstage.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.backstage.entity.UserRole;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @since 2024-09-25 15:31
 */
@Mapper
public interface UserRoleMapper extends BaseMapper<UserRole> {

    /**
     * 删除用户角色关联信息
     *
     * @param userId 用户id
     * @return int
     */
    int deleteByUserId(Integer userId);
}
