package com.trs.ai.moye.knowledgebase.controller;

import com.trs.ai.moye.knowledgebase.request.external.ExternalAddDataRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalAttrDataRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalConditionRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalDataRankRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalDeleteDataRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalMediaRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalMultiSearchRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalPageListRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalUpdateDataRequest;
import com.trs.ai.moye.knowledgebase.service.KnowledgeBaseExternalDataService;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.request.PageParams;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 知识库对外接口
 *
 * <AUTHOR>
 * @since 2020/6/8 09:59
 */
@RestController
@RequestMapping("/knowledge-base/external")
@Validated
public class KnowledgeBaseExternalController {


    @Resource
    private KnowledgeBaseExternalDataService service;


    /**
     * 获取知识库数据分页列表 模糊搜索
     * <a href=https://yapi-192.trscd.com.cn/project/5419/interface/api/168739>YApi</a>
     *
     * @param request 请求参数
     * @return 知识库数据
     */
    @PostMapping("/data/list")
    public PageResponse<Map<String, Object>> getDataPageList(
        @Validated @RequestBody ExternalPageListRequest request) {
        if (request.getPageParams() == null) {
            request.setPageParams(new PageParams());
        }
        return service.getPageList(request);
    }

    /**
     * 获取知识库数据分页列表 where条件搜索
     * <a href=https://yapi-192.trscd.com.cn/project/5419/interface/api/165907>YApi</a>
     *
     * @param request 请求参数
     * @return 知识库数据
     */
    @PostMapping("/data/list/conditions")
    public PageResponse<Map<String, Object>> getConditionPageList(
        @Validated @RequestBody ExternalConditionRequest request) {
        if (request.getPageParams() == null) {
            request.setPageParams(new PageParams());
        }
        request.checkConditions();
        return service.getConditionPageList(request);
    }

    /**
     * 获取知识库数据分页列表 多条件精确搜索
     * <a href=https://yapi-192.trscd.com.cn/project/5419/interface/api/165913>YApi</a>
     *
     * @param request 请求参数
     * @return 知识库数据
     */
    @PostMapping("/data/list/multi-search")
    public PageResponse<Map<String, Object>> getMultiSearchPageList(
        @Validated @RequestBody ExternalMultiSearchRequest request) {
        if (request.getPageParams() == null) {
            request.setPageParams(new PageParams());
        }
        return service.getMultiSearchPageList(request);
    }

    /**
     * 获取知识库数据量
     *
     * @param type   知识库类型
     * @param enName 知识库中文名
     * @return 数据量
     */
    @GetMapping("/data/count")
    public Integer getDataCount(@NotNull(message = "知识库类型不能为空") KnowledgeBaseType type,
        @NotBlank(message = "知识库英文名不能为空") String enName) {
        return service.getDataCount(type, enName);
    }

    /**
     * 获取媒体数据列表
     *
     * @param request 请求参数
     * @return 媒体数据列表
     */
    @PostMapping("/data/media/list")
    public List<String> getMediaDataList(@Validated @RequestBody ExternalMediaRequest request)
        throws BizException {
        return service.getMediaDataList(request);
    }

    /**
     * 数据排行
     *
     * @param request 请求参数
     * @return 数据排行
     */
    @PostMapping("/data/rank")
    public List<String> getDataRank(@Validated @RequestBody ExternalDataRankRequest request) {
        return service.getDataRank(request);
    }

    /**
     * 获取属性数据
     * <a href=https://yapi-192.trscd.com.cn/project/5419/interface/api/165931>YApi</a>
     *
     * @param request 请求参数
     * @return 属性数据
     */
    @PostMapping("/attr/values")
    public List<String> getAttrData(@Validated @RequestBody ExternalAttrDataRequest request)
        throws BizException {
        return service.getAttrData(request);
    }

    /**
     * 删除数据
     * <a href=https://yapi-192.trscd.com.cn/project/5419/interface/api/168742>YApi</a>
     *
     * @param request 请求参数
     */
    @DeleteMapping("/data")
    public void deleteData(@Validated @RequestBody ExternalDeleteDataRequest request) {
        service.deleteData(request.getType(), request.getEnName(), request.getDataIds());
    }

    /**
     * 添加数据
     * <a href=https://yapi-192.trscd.com.cn/project/5419/interface/api/168740>YApi</a>
     *
     * @param request 请求参数
     * @return 数据id
     */
    @PostMapping("/data")
    public long addData(@Validated @RequestBody ExternalAddDataRequest request) {
        return service.addData(request.getType(), request.getEnName(), request.getFieldValueList());
    }

    /**
     * 更新数据
     * <a href=https://yapi-192.trscd.com.cn/project/5419/interface/api/168741>YApi</a>
     *
     * @param request 请求参数
     */
    @PutMapping("/data")
    public void updateData(@Validated @RequestBody ExternalUpdateDataRequest request) {
        service.updateData(request.getType(), request.getEnName(), request.getDataId(),
            request.getFieldValueList());
    }
}
