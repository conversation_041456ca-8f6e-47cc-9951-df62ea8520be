package com.trs.ai.moye.backstage.response.exclusive;

import com.trs.ai.moye.backstage.entity.StreamExclusiveNodeConfig;
import com.trs.moye.base.common.response.IdNameResponse;
import java.time.LocalDateTime;
import java.util.LinkedList;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-01-13 20:31
 */
@Data
@NoArgsConstructor
public class ExclusiveNodeConfigResponse {

    private Integer id;

    /**
     * 数据源id
     **/
    private Set<Integer> dataModelIds;

    /**
     * 期望节点数
     **/
    private Integer expectNodeCount;

    /**
     * 优先级
     **/
    private Integer priority;

    /**
     * 分配的节点列表
     */
    private LinkedList<String> actualNodes;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 更新时间
     */
    private Integer updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 并发线程数, nacos上对此有默认值
     */
    private Integer concurrentThreads;

    /**
     * 限流, 单位: 条数据/分钟
     */
    private Integer rateLimit;

    public ExclusiveNodeConfigResponse(StreamExclusiveNodeConfig config) {
        this.id = config.getId();
        this.dataModelIds = config.getDataModels().stream().map(IdNameResponse::getId).collect(Collectors.toSet());
        this.expectNodeCount = config.getExpectNodeCount();
        this.priority = config.getPriority();
        this.updateBy = config.getUpdateBy();
        this.updateTime = config.getUpdateTime();
        this.concurrentThreads = config.getConcurrentThreads();
        this.rateLimit = config.getRateLimit();
    }
}
