package com.trs.ai.moye.storageengine.request;

import com.trs.ai.moye.common.validation.ValidCondition;
import com.trs.ai.moye.data.service.entity.DataServiceStatDims;
import com.trs.ai.moye.data.service.entity.params.EsBoostConfig;
import com.trs.ai.moye.data.service.entity.query.Condition;
import com.trs.ai.moye.storageengine.entity.SortField;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 条件查询参数
 *
 * <AUTHOR>
 * @since 2024/10/18 11:24:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConditionSearchParams extends StorageEngineSearchRequest {

    /**
     * 查询条件逻辑表达式
     */
    @ValidCondition
    private List<Condition> conditions = new ArrayList<>();
    /**
     * 统计维度（统计服务时用）
     */
    private List<DataServiceStatDims> statDims = new ArrayList<>();
    /**
     * 分组字段
     */
    private List<String> groupFields;
    /**
     * 排序字段
     */
    private List<SortField> sortFields;
    /**
     * 返回字段
     */
    private List<String> returnFields;

    /**
     * 最小相似度
     */
    private Integer minSimilarity = 0;

    /**
     * es权重配置
     */
    private List<EsBoostConfig> esBoostConfigs;

    /**
     * 去重字段名称
     */
    private String distinctFieldName;
}
