package com.trs.ai.moye.backstage.utils;

import java.util.List;
import java.util.Map;

/**
 * 消息推送配置工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/11/1 11:35
 **/
public class NoticeCenterUtils {

    private NoticeCenterUtils() {
    }

    /**
     * 获取对应的值
     *
     * @param map    ID名称键值对
     * @param idList ID列表
     * @return {@link String}
     * <AUTHOR>
     * @since 2024/11/1 11:50
     */
    public static String getCorrespondingValue(Map<Integer, String> map, List<Integer> idList) {
        if (map == null || idList == null || idList.isEmpty()) {
            return ""; // 返回一个空字符串或根据需要处理
        }

        StringBuilder sb = new StringBuilder();

        for (Integer id : idList) {
            String value = map.get(id);
            if (value != null) {
                sb.append(value).append("；");
            }
        }

        // 去掉最后一个分号
        if (sb.length() > 0) {
            sb.setLength(sb.length() - 1);
        }

        return sb.toString();
    }

}
