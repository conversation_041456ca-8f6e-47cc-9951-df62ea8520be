package com.trs.ai.moye.backstage.response;


import com.trs.ai.moye.backstage.entity.MessagePushRecord;
import com.trs.ai.moye.backstage.entity.Role;
import com.trs.ai.moye.backstage.entity.User;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.Builder;
import lombok.Data;

/**
 * 消息推送记录
 */
@Data
@Builder
public class MessagePushRecordResponse {

    /**
     * 消息类型名称
     */
    private String messageType;

    /**
     * 消息子类型名称
     */
    private String messageSubtype;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 推送配置id
     */
    private Integer pushConfId;

    /**
     * 消息推送类型名称
     */
    private String pushType;

    /**
     * 消息发送时间
     */
    private LocalDateTime pushTime;

    /**
     * 推送地址
     */
    private String pushAddress;

    /**
     * 推送对象-部门
     */
    private List<String> pushDepts;

    /**
     * 推送对象-角色
     */
    private List<String> pushRoles;

    /**
     * 推送对象-用户
     */
    private List<String> pushUsers;

    /**
     * 是否推送成功
     */
    private Boolean isSuccess;

    /**
     * 失败原因
     */
    private String errorMsg;

    /**
     * 记录入库时间
     */
    private LocalDateTime storageTime;

    /**
     * 替换部门、角色、用户 的 id 为 名称
     *
     * @param messagePushRecord 原始数据
     * @param roles             角色
     * @param users             用户
     * @return 替换后的数据
     */
    public static MessagePushRecordResponse from(MessagePushRecord messagePushRecord,
        Map<Integer, Role> roles, Map<Integer, User> users) {
        MessagePushRecordResponseBuilder builder = new MessagePushRecordResponseBuilder()
            .messageType(messagePushRecord.getMessageType())
            .messageSubtype(messagePushRecord.getMessageSubtype())
            .title(messagePushRecord.getTitle())
            .content(messagePushRecord.getContent())
            .pushConfId(messagePushRecord.getPushConfId())
            .pushType(messagePushRecord.getPushType())
            .pushTime(messagePushRecord.getPushTime())
            .pushAddress(messagePushRecord.getPushAddress())
            .isSuccess(messagePushRecord.getIsSuccess())
            .errorMsg(messagePushRecord.getErrorMsg())
            .storageTime(messagePushRecord.getStorageTime())
            .pushDepts(List.of("'部门'暂未实现"))
            .pushRoles(Collections.emptyList()).pushUsers(Collections.emptyList());
        if (Objects.nonNull(messagePushRecord.getPushRoles()) && !messagePushRecord.getPushRoles().isEmpty()) {
            List<String> pushRoleNames = messagePushRecord.getPushRoles().stream()
                .map(e -> {
                    Role role = roles.get(e);
                    return role != null ? role.getName() : "";
                })
                .toList();
            builder.pushRoles(pushRoleNames);
        }
        if (Objects.nonNull(messagePushRecord.getPushUsers()) && !messagePushRecord.getPushUsers().isEmpty()) {
            List<String> pushUserNames = messagePushRecord.getPushUsers().stream()
                .map(e -> {
                    User user = users.get(e);
                    return user != null ? user.getName() : "";
                })
                .toList();
            builder.pushUsers(pushUserNames);
        }
        return builder.build();
    }
}
