package com.trs.ai.moye.backstage.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.trs.moye.base.common.enums.ResultType;
import com.trs.moye.base.common.log.api.ApiLogTrace;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import com.trs.moye.base.common.utils.DateTimeUtils;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2025-03-05 17:01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiLogTraceResponse {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss.SSS");

    private String id;

    /**
     * 日志id
     */
    private String logId;

    /**
     * 节点
     */
    private String node;

    /**
     * 应用名称
     */
    private String applicationName;

    /**
     * 创建时间戳
     */
    private String createTime;

    /**
     * 执行时长，单位ms
     */
    private Long executeDuration;

    /**
     * 执行结果
     */
    private ResultType executeResult;

    /**
     * 输入详情
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, Object> inputDetails;


    /**
     * 输出详情
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, Object> outputDetails;

    /**
     * 总耗时，单位ms
     */
    private Long totalDuration;

    /**
     * 执行耗时占比
     *
     * @return String
     */
    @JsonProperty
    public String getExecuteDurationRatio() {
        return ObjectUtils.isEmpty(totalDuration) ? "0%" : (executeDuration * 100 / totalDuration) + "%";
    }

    public ApiLogTraceResponse(Long totalDuration, ApiLogTrace apiLogTrace) {
        this.id = String.valueOf(apiLogTrace.getId());
        this.logId = String.valueOf(apiLogTrace.getLogId());
        this.node = apiLogTrace.getNode();
        this.applicationName = apiLogTrace.getApplicationName();
        this.createTime = DateTimeUtils.parse(apiLogTrace.getCreateTimestamp()).toLocalTime().format(FORMATTER);
        this.executeDuration = apiLogTrace.getExecuteDuration();
        this.executeResult = apiLogTrace.getExecuteResult();
        this.inputDetails = apiLogTrace.getInputDetails();
        this.outputDetails = apiLogTrace.getOutputDetails();
        this.totalDuration = totalDuration;
    }


}
