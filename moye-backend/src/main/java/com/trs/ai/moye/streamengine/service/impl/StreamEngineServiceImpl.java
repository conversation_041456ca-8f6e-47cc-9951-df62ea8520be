package com.trs.ai.moye.streamengine.service.impl;

import com.trs.ai.moye.streamengine.feign.StreamEngineFeign;
import com.trs.ai.moye.streamengine.service.StreamEngineService;
import com.trs.moye.base.common.exception.BizException;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 流处理引擎服务
 *
 * <AUTHOR>
 * @since 2025/06/27 15:30
 **/
@Service
@Slf4j
public class StreamEngineServiceImpl implements StreamEngineService {

    @Resource
    private StreamEngineFeign streamEngineFeign;

    /**
     * 执行代码任务
     *
     * <AUTHOR>
     * @since 2025/06/27 15:30
     */
    @Override
    public void allocateServerNode() {
        try {
            streamEngineFeign.allocateServerNode();
        } catch (Exception e) {
            log.error("触发引擎节点分配失败!", e);
            throw new BizException("触发引擎节点分配失败!", e);
        }
    }

}
