package com.trs.ai.moye.backstage.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.trs.moye.base.common.response.IdNameResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * V4消息中心消息类型
 *
 * <AUTHOR>
 * @since 2024/8/28
 */
@Getter
@AllArgsConstructor
public enum NoticeType {

    /**
     * 性能异常
     */
    DATA_EXCEPTION(1, "性能异常"),
    /**
     * 任务异常
     */
    TASK_EXCEPTION(2, "任务异常"),
    /**
     * 其他
     */
    OTHER(3, "其他"),

    /**
     * 服务异常
     */
    SERVER_EXCEPTION(4, "服务异常");

    @EnumValue
    private final Integer code;

    private final String label;


    /**
     * 根据code获取枚举对象
     *
     * @param code code
     * @return {@link NoticeType}
     * <AUTHOR>
     * @since 2024/10/30 15:53
     */
    @JsonCreator
    public static NoticeType fromCode(Integer code) {
        for (NoticeType value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("No enum constant for code: " + code);
    }

    /**
     * 获取枚举名称
     *
     * @param code code
     * @return {@link String }
     * <AUTHOR>
     * @since 2024/10/30 15:52
     */
    public static String getNameByCode(Integer code) {
        for (NoticeType value : values()) {
            if (value.code.equals(code)) {
                return value.label;
            }
        }
        throw new IllegalArgumentException("No enum constant for code: " + code);
    }


    /**
     * 转换对象列表
     *
     * @return {@link IdNameResponse}
     * <AUTHOR>
     * @since 2024/10/30 15:52
     */
    public static List<IdNameResponse> toObjectList() {
        List<IdNameResponse> list = new ArrayList<>();
        for (NoticeType value : values()) {
            // 服务异常不在界面上显示
            if (value.equals(SERVER_EXCEPTION)) {
                continue;
            }
            list.add(new IdNameResponse(value.getCode(), value.getLabel()));
        }
        // 将list中name为其它的元素放到最后
        list.sort((o1, o2) -> {
            if (o1.getName().equals(NoticeType.OTHER.getLabel())) {
                return 1;
            }
            if (o2.getName().equals(NoticeType.OTHER.getLabel())) {
                return -1;
            }
            return 0;
        });
        return list;
    }


    /**
     * 转换成map
     *
     * @return {@link Map}
     * <AUTHOR>
     * @since 2024/10/30 15:52
     */
    public static Map<Integer, String> toMap() {
        return Arrays.stream(NoticeType.values()).collect(Collectors.toMap(NoticeType::getCode, NoticeType::getLabel));
    }
}
