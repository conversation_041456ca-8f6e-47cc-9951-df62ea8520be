package com.trs.ai.moye.backstage.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.backstage.entity.Department;
import com.trs.ai.moye.backstage.entity.DepartmentTree;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 使用数据范围表，查询数据可供哪个部门使用
 *
 * <AUTHOR>
 * @since 2020/10/14 17:09
 */
@Mapper
public interface DepartmentMapper extends BaseMapper<Department> {


    /**
     * 判断名称是否存在
     *
     * @param name 名称
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/9/25 10:36
     */
    Boolean existsByName(@Param("name") String name);


    /**
     * 查询部门树
     *
     * @return {@link DepartmentTree}
     * <AUTHOR>
     * @since 2024/9/25 11:34
     */
    List<DepartmentTree> selectDepartmentTree();

    /**
     * 排除ID外的名称查找
     *
     * @param name 名称
     * @param id   ID
     * @return {@link Department}
     * <AUTHOR>
     * @since 2024/9/25 14:37
     */
    List<Department> findByNameExceptId(@Param("name") String name, @Param("id") Integer id);
}

