package com.trs.ai.moye.backstage.service.impl;

import com.trs.ai.moye.backstage.dao.ApiLogMapper;
import com.trs.ai.moye.backstage.dao.ApiLogTraceMapper;
import com.trs.ai.moye.backstage.request.ApiLogPageListRequest;
import com.trs.ai.moye.backstage.response.ApiLogTraceResponse;
import com.trs.ai.moye.backstage.service.ApiLogService;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.log.api.ApiLog;
import com.trs.moye.base.common.utils.AssertUtils;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025-03-04 14:39
 */
@Slf4j
@Service
public class ApiLogServiceImpl implements ApiLogService {

    @Resource
    private ApiLogMapper apiLogMapper;

    @Resource
    private ApiLogTraceMapper apiLogTraceMapper;

    @Override
    public PageResponse<ApiLog> pageList(ApiLogPageListRequest request) {
        return PageResponse.of(
            apiLogMapper.pageList(request.getExternalLogId()
                , request.getResponseStatus()
                , request.getConnectionType()
                , request.getTimeRange().getMinTimeStr()
                , request.getTimeRange().getMaxTimeStr()
                , request.getSearchParams()
                , request.getPageParams().toPage()));
    }

    @Override
    public List<ApiLogTraceResponse> getTracerList(Long id) {
        ApiLog apiLog = apiLogMapper.selectById(id);
        AssertUtils.notEmpty(apiLog, "主键为【%s】的api日志不存在");
        return apiLogTraceMapper.selectByLogId(id).stream()
            .map(tracer -> new ApiLogTraceResponse(apiLog.getResponseDuration(), tracer)).toList();
    }
}
