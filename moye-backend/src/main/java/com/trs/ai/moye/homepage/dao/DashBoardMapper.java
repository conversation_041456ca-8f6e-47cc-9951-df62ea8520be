package com.trs.ai.moye.homepage.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.trs.ai.moye.homepage.entity.DashBoardData;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2025/1/22
 **/

@Mapper
@DS("clickhouse")
public interface DashBoardMapper {

    /**
     * 获取最新的数据
     *
     * @return 首页展示数据
     */
    List<DashBoardData> getDashBoardDataLast();

    /**
     * 获取执行的算子总数
     *
     * @return 执行的算子总数
     */
    Long getExecutedOperatorTotalCount();

    /**
     * 算子执行增量数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 算子执行增量数据
     */
    Long getExecutedOperatorIncrementCount(@Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 插入数据
     *
     * @param dashBoardData 数据
     */
    void insertData(@Param("list") List<DashBoardData> dashBoardData);


    /**
     * 获取数据模型数量
     *
     * @param category 分类
     * @param metric   指标
     * @return 数据模型数量
     */
    Long getDataModelCount(@Param("category") String category, @Param("metric") String metric);

}
