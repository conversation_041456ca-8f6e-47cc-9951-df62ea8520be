package com.trs.ai.moye.backstage.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.backstage.dao.AuthCertificateKerberosMapper;
import com.trs.ai.moye.backstage.dao.RoleMapper;
import com.trs.ai.moye.backstage.dao.UserMapper;
import com.trs.ai.moye.backstage.entity.AuthCertificateKerberos;
import com.trs.ai.moye.backstage.entity.Role;
import com.trs.ai.moye.backstage.entity.User;
import com.trs.ai.moye.backstage.request.AuthCertificateKerberosInfo;
import com.trs.ai.moye.backstage.request.RoleRequest;
import com.trs.ai.moye.backstage.response.AuthCertificateKerberosResponse;
import com.trs.ai.moye.backstage.response.CertificateResponse;
import com.trs.ai.moye.backstage.response.RoleResponse;
import com.trs.ai.moye.backstage.service.AuthCertificateKerberosService;
import com.trs.ai.moye.backstage.service.RoleService;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.ai.moye.permission.service.CurrentUserService;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.request.BaseRequestParams;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2024/10/18
 **/
@Service
public class RoleServiceImpl implements RoleService {


    @Resource
    private RoleMapper roleMapper;
    @Resource
    private AuthCertificateKerberosService authCertificateKerberosService;
    @Resource
    private AuthCertificateKerberosMapper authCertificateKerberosMapper;
    @Resource
    private CurrentUserService currentUserService;
    @Resource
    private UserMapper userMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addRole(RoleRequest request) {
        checkRoleNameValid(request.getName(), request.getId());
        Role role = Role.fromRequest(request);
        roleMapper.insert(role);
        List<Integer> credentials = processCredentials(request.getCredentials(), role.getId());
        role.setCredentials(credentials);
        roleMapper.updateById(role);
    }

    private List<Integer> processCredentials(List<AuthCertificateKerberosInfo> credentials, Integer roleId) {
        return credentials.stream()
            .map(tempInfo -> authCertificateKerberosService.saveKerberosFileFromTempDir(tempInfo, roleId)).toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRole(RoleRequest request) {
        checkRoleNameValid(request.getName(), request.getId());
        Role role = Role.fromRequest(request);
        List<Integer> credentials = processCredentials(request.getCredentials(), role.getId());
        role.setCredentials(credentials);
        roleMapper.updateById(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRole(Integer id) {
        List<User> userList = userMapper.getUsersByRoleId(id);
        if (!userList.isEmpty()) {
            throw new BizException("该角色已经被【%s】个用户使用：%s", userList.size(),
                String.join("、", userList.stream().map(User::getAccount).toList()));
        }
        roleMapper.selectByIdRole(id).getCredentials().forEach(authCertificateKerberosService::deleteById);
        roleMapper.deleteById(id);
    }

    @Override
    public PageResponse<RoleResponse> pageList(BaseRequestParams request) {
        Page<Role> rolePage = roleMapper.selectPageByRoleName(request.getPageParams().toPage(),
            request.getKeyWord().toString(), request.getSortParams());
        return PageResponse.of(rolePage).convertItemsType(this::convertFunction);
    }

    private List<RoleResponse> convertFunction(List<Role> roles) {
        return roles.stream().map(RoleResponse::new).toList();
    }

    @Override
    public Boolean isRoleNameDuplicate(String name, Integer id) {
        Integer count = roleMapper.countByNameAndExcludeId(name, id);
        return count > 0;
    }

    @Override
    public void checkRoleNameValid(String name, Integer id) {
        if (Boolean.TRUE.equals(isRoleNameDuplicate(name, id))) {
            throw new BizException("角色名称重复");
        }
    }


    @Override
    public RoleResponse getDetail(Integer id) {
        Role role = roleMapper.selectByIdRole(id);
        if (role != null) {
            RoleResponse roleResponse = new RoleResponse(role);
            roleResponse.setCredentials(getAuthCertificateKerberosResponseList(role.getCredentials()));
            return roleResponse;
        }
        return null;
    }

    List<AuthCertificateKerberosResponse> getAuthCertificateKerberosResponseList(List<Integer> credentials) {
        if (credentials == null || credentials.isEmpty()) {
            return List.of();
        }
        return authCertificateKerberosMapper.selectBatchIds(credentials).stream()
            .map(AuthCertificateKerberosResponse::new).toList();
    }

    @Override
    public List<CertificateResponse> selectCredentialsList() {
        Integer userId = currentUserService.getUserId();
        User user = userMapper.selectById(userId);
        List<Integer> roleIds = user.getRoleIds();
        List<Role> roles = roleMapper.selectByIdCollection(roleIds);
        List<Integer> credentialIds = roles.stream().map(Role::getCredentials).filter(Objects::nonNull)
            .flatMap(List::stream).toList();
        List<AuthCertificateKerberos> authCertificateKerberos = null;
        if (!credentialIds.isEmpty()) {
            authCertificateKerberos = authCertificateKerberosMapper.selectBatchIds(credentialIds);
        }
        if (Objects.nonNull(authCertificateKerberos) && !authCertificateKerberos.isEmpty()) {
            return authCertificateKerberos.stream().map(CertificateResponse::from).toList();
        }
        return List.of();
    }
}
