package com.trs.ai.moye.backstage.service.impl;

import com.alibaba.fastjson.JSON;
import com.trs.ai.moye.backstage.dao.StreamExclusiveNodeConfigMapper;
import com.trs.ai.moye.backstage.dao.UserMapper;
import com.trs.ai.moye.backstage.entity.StreamExclusiveNodeConfig;
import com.trs.ai.moye.backstage.entity.User;
import com.trs.ai.moye.backstage.entity.exclusive.DataModelNodeDetail;
import com.trs.ai.moye.backstage.entity.exclusive.EngineNodeAllocationDetail;
import com.trs.ai.moye.backstage.entity.exclusive.EngineNodeProcessInfo;
import com.trs.ai.moye.backstage.entity.exclusive.ExclusiveConfigDetail;
import com.trs.ai.moye.backstage.request.ExclusiveDataModelListRequest;
import com.trs.ai.moye.backstage.request.ExclusiveNodeConfigRequest;
import com.trs.ai.moye.backstage.response.exclusive.ExclusiveBaseResponse;
import com.trs.ai.moye.backstage.response.exclusive.ExclusiveDataModelListResponse;
import com.trs.ai.moye.backstage.response.exclusive.ExclusiveDataModelResponse;
import com.trs.ai.moye.backstage.response.exclusive.ExclusiveNodeConfigListResponse;
import com.trs.ai.moye.backstage.response.exclusive.ExclusiveNodeConfigResponse;
import com.trs.ai.moye.backstage.service.StreamExclusiveNodeConfigService;
import com.trs.ai.moye.common.http.HttpClient;
import com.trs.ai.moye.data.model.dao.DataModelDisplayMapper;
import com.trs.ai.moye.data.operator.properties.EngineProperties;
import com.trs.ai.moye.etcd.util.EtcdUtil;
import com.trs.ai.moye.monitor.dao.DataProcessRecordMapper;
import com.trs.ai.moye.monitor.dao.DataProcessTraceMapper;
import com.trs.ai.moye.monitor.entity.NodeAccessCountInfo;
import com.trs.ai.moye.monitor.entity.NodeProcessCountInfo;
import com.trs.ai.moye.streamengine.service.StreamEngineService;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.help.AddUpdateDeleteSeparator;
import com.trs.moye.base.common.help.RepeatChecker;
import com.trs.moye.base.common.response.IdNameResponse;
import com.trs.moye.base.common.utils.ValidationUtils;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2025-01-14 14:28
 */
@Slf4j
@Service
public class StreamExclusiveNodeConfigServiceImpl implements StreamExclusiveNodeConfigService {

    private static final String ETCD_NODE_ALLOCATION_DETAIL = "node-control/node-allocation-detail";

    @Value("${stream.engine.etcd.prefix:/tyengine/}")
    private String streamEngineEtcdPrefix;

    @Resource
    private StreamExclusiveNodeConfigMapper mapper;

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private DataModelDisplayMapper dataModelDisplayMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private EtcdUtil etcdUtil;

    @Resource
    private DataProcessRecordMapper dataProcessRecordMapper;

    @Resource
    private DataProcessTraceMapper dataProcessTraceMapper;

    @Resource
    private EngineProperties engineProperties;

    @Resource
    private HttpClient httpClient;

    @Resource
    private StreamEngineService streamEngineService;

    @Resource
    private SaveHelper saveHelper;

    @Override
    public List<IdNameResponse> selectableDataModelList() {
        List<IdNameResponse> idNameResponses = new ArrayList<>(
            List.of(new IdNameResponse(StreamExclusiveNodeConfig.COMMON_DATA_MODEL_ID
                , StreamExclusiveNodeConfig.COMMON_DATA_MODEL_NAME)));
        idNameResponses.addAll(dataModelMapper.selectStreamProcessTask().stream().map(
            obj -> new IdNameResponse(obj.getId(), obj.getZhName())).toList());
        return idNameResponses;
    }

    @Override
    public ExclusiveNodeConfigListResponse exclusiveConfigList() {
        List<StreamExclusiveNodeConfig> configList = selectExclusiveNodeConfig();
        ExclusiveNodeConfigListResponse response = new ExclusiveNodeConfigListResponse();
        if (ObjectUtils.isEmpty(configList)) {
            response.setExclusiveConfigList(new ArrayList<>());
            return response;
        }
        configList.sort(Comparator.comparing(StreamExclusiveNodeConfig::getPriority));
        List<ExclusiveNodeConfigResponse> configResponseList = configList.stream()
            .map(ExclusiveNodeConfigResponse::new).toList();
        response.setExclusiveConfigList(configResponseList);
        // 查询etcd中节点分配详情
        EngineNodeAllocationDetail nodeAllocationDetail = getEtcdAllocationDetail();
        // 设置实际节点信息
        setActualNodes(nodeAllocationDetail, configResponseList);
        // 设置专属节点和通用节点总数
        setExclusiveCommonNodeTotalCount(nodeAllocationDetail, response);
        // 设置更新人姓名
        setUpdateUserName(configResponseList);
        return response;
    }

    private List<StreamExclusiveNodeConfig> selectExclusiveNodeConfig() {
        List<StreamExclusiveNodeConfig> configList = mapper.selectAll();
        if (configList.isEmpty()) {
            configList.add(StreamExclusiveNodeConfig.buildDefaultCommonNodeConfig());
        }
        return configList;
    }

    private void setActualNodes(EngineNodeAllocationDetail nodeAllocationDetail,
        List<ExclusiveNodeConfigResponse> configResponseList) {
        if (nodeAllocationDetail == null) {
            for (ExclusiveNodeConfigResponse config : configResponseList) {
                config.setActualNodes(new LinkedList<>());
            }
        } else {
            nodeAllocationDetail.getExclusiveConfigDetailList().forEach(detail -> detail.setDataModelIds(
                detail.getDataModels().stream().map(IdNameResponse::getId).collect(Collectors.toSet())));
            Map<Set<Integer>, ExclusiveConfigDetail> exclusiveConfigAllocationDetailMap = nodeAllocationDetail.
                getExclusiveConfigDetailList().stream().collect(
                    Collectors.toMap(ExclusiveConfigDetail::getDataModelIds, Function.identity()));
            for (ExclusiveNodeConfigResponse config : configResponseList) {
                ExclusiveConfigDetail exclusiveConfigAllocationDetail = exclusiveConfigAllocationDetailMap.get(
                    config.getDataModelIds());
                config.setActualNodes(exclusiveConfigAllocationDetail == null ? new LinkedList<>() :
                    exclusiveConfigAllocationDetail.getActualNodes());
            }
        }
    }

    private void setExclusiveCommonNodeTotalCount(EngineNodeAllocationDetail nodeAllocationDetail
        , ExclusiveBaseResponse response) {
        if (nodeAllocationDetail == null) {
            return;
        }
        response.setActualExclusiveNodeCount(nodeAllocationDetail.getSummary().getActualExclusiveNodeCount());
        response.setActualCommonNodeCount(nodeAllocationDetail.getSummary().getActualCommonNodeCount());
    }

    private void setUpdateUserName(List<ExclusiveNodeConfigResponse> configResponseList) {
        Map<Integer, User> userMap = userMapper.selectAllAsMap();
        for (ExclusiveNodeConfigResponse config : configResponseList) {
            User user = userMap.get(config.getUpdateBy());
            config.setUpdateUserName(user == null ? "" : user.getName());
        }
    }

    private EngineNodeAllocationDetail getEtcdAllocationDetail() {
        String value = etcdUtil.get(getFullEtcdNodeAllocationDetailKey());
        if (ObjectUtils.isEmpty(value)) {
            return null;
        }
        EngineNodeAllocationDetail nodeAllocationDetail = JSON.parseObject(value,
            EngineNodeAllocationDetail.class);
        ValidationUtils.validate(nodeAllocationDetail);
        return nodeAllocationDetail;
    }

    /**
     * 获取完整的etcd注册目录
     *
     * @return 完整的etcd注册目录
     */
    public String getFullEtcdNodeAllocationDetailKey() {
        return streamEngineEtcdPrefix + ETCD_NODE_ALLOCATION_DETAIL;
    }

    @Override
    public ExclusiveDataModelListResponse exclusiveMonitorList(ExclusiveDataModelListRequest request) {
        List<StreamExclusiveNodeConfig> configList = mapper.selectAll();
        ExclusiveDataModelListResponse response = new ExclusiveDataModelListResponse();
        if (ObjectUtils.isEmpty(configList)) {
            response.setDataModelList(new ArrayList<>());
            return response;
        }
        // 构造数据建模响应列表
        List<ExclusiveDataModelResponse> modelResponseList = buildModelResponseList(configList);
        // 过滤符合条件的数据建模
        response.setDataModelList(modelResponseList.stream().filter(request.buildFilter()).toList());
        // 查询etcd节点分配详情
        EngineNodeAllocationDetail nodeAllocationDetail = getEtcdAllocationDetail();
        // 设置节点处理量信息信息
        String latelyTime = request.buildLatelyTime();
        setNodeProcessCountInfo(nodeAllocationDetail, modelResponseList, latelyTime);
        // 设置建模接入量信息
        setModelAccessInfoMap(modelResponseList, latelyTime);
        // 设置专属节点和通用节点总量
        setExclusiveCommonNodeTotalCount(nodeAllocationDetail, response);
        return response;
    }

    private List<ExclusiveDataModelResponse> buildModelResponseList(List<StreamExclusiveNodeConfig> configList) {
        List<ExclusiveDataModelResponse> modelResponseList = new ArrayList<>(32);
        for (StreamExclusiveNodeConfig config : configList) {
            for (IdNameResponse dataModel : config.getDataModels()) {
                ExclusiveDataModelResponse modelResponse = new ExclusiveDataModelResponse();
                modelResponse.setId(dataModel.getId());
                modelResponse.setName(dataModel.getName());
                modelResponse.setExpectNodeCount(config.getExpectNodeCount());
                modelResponseList.add(modelResponse);
            }
        }
        return modelResponseList;
    }

    private void setNodeProcessCountInfo(EngineNodeAllocationDetail nodeAllocationDetail,
        List<ExclusiveDataModelResponse> modelResponseList, String latelyTime) {
        Map<String, NodeProcessCountInfo> processInfoMap = selectNodeProcessInfoMap(latelyTime);
        Map<Integer, DataModelNodeDetail> modelNodeDetailMap = nodeAllocationDetail == null ? new HashMap<>() :
            nodeAllocationDetail.buildDataModelNodeDetailMap();
        for (ExclusiveDataModelResponse config : modelResponseList) {
            config.setEngineNodeList(new ArrayList<>());
            DataModelNodeDetail modelNodeDetail = modelNodeDetailMap.get(config.getId());
            if (Objects.isNull(modelNodeDetail) || ObjectUtils.isEmpty(modelNodeDetail.getActualNodes())) {
                continue;
            }
            for (String node : modelNodeDetail.getActualNodes()) {
                String key = NodeProcessCountInfo.buildUniqueIdentify(config.getId(), node);
                NodeProcessCountInfo processCountInfo = processInfoMap.get(key);
                EngineNodeProcessInfo engineNodeProcessInfo = processCountInfo == null ? new EngineNodeProcessInfo(node)
                    : new EngineNodeProcessInfo(processCountInfo);
                config.getEngineNodeList().add(engineNodeProcessInfo);
            }
        }
        // 合并引擎节点处理信息
        modelResponseList.forEach(ExclusiveDataModelResponse::mergeEngineNodeProcessInfo);
    }

    private Map<String, NodeProcessCountInfo> selectNodeProcessInfoMap(String latelyTime) {
        List<NodeProcessCountInfo> nodeProcessCountInfoList = dataProcessRecordMapper.selectNodeProcessCountList(
            latelyTime);
        return nodeProcessCountInfoList.stream().
            filter(info -> ObjectUtils.isNotEmpty(info.getNode()) || ObjectUtils.isNotEmpty(info.getDataModelId()))
            .collect(Collectors.toMap(NodeProcessCountInfo::getUniqueIdentify, Function.identity()));
    }

    private void setModelAccessInfoMap(List<ExclusiveDataModelResponse> modelResponseList, String latelyTime) {
        Map<Integer, NodeAccessCountInfo> accessCountInfoMap = selectModelAccessInfoMap(latelyTime);
        for (ExclusiveDataModelResponse config : modelResponseList) {
            NodeAccessCountInfo accessCountInfo = accessCountInfoMap.get(config.getId());
            if (accessCountInfo != null) {
                config.setAccessCount(accessCountInfo.getAccessCount());
            }
        }
    }

    private Map<Integer, NodeAccessCountInfo> selectModelAccessInfoMap(String latelyTime) {
        List<NodeAccessCountInfo> nodeAccessCountInfoList = dataProcessTraceMapper.selectNodeAccessCountList(
            latelyTime);
        return nodeAccessCountInfoList.stream().filter(info -> ObjectUtils.isNotEmpty(info.getDataModelId()))
            .collect(Collectors.toMap(NodeAccessCountInfo::getDataModelId, Function.identity()));
    }

    @Override
    public void saveExclusiveNodeConfigs(List<ExclusiveNodeConfigRequest> requests) {
        Map<Integer, String> dataModelNameMap = selectDataModelNameMap();
        // 保存配置信息和调用流处理引擎不能再同一事务里，否则流处理引擎感知不到数据库的变化。
        // 事务逻辑提取到saveHelper.saveExclusiveNodeConfigs()方法中
        saveHelper.saveExclusiveNodeConfigs(requests, dataModelNameMap);
        streamEngineService.allocateServerNode();
    }

    private Map<Integer, String> selectDataModelNameMap() {
        Map<Integer, String> map = dataModelDisplayMapper.selectDataModelDict().stream()
            .collect(Collectors.toMap(IdNameResponse::getId, IdNameResponse::getName));
        map.put(StreamExclusiveNodeConfig.COMMON_DATA_MODEL_ID, "通用节点配置");
        return map;
    }

    /**
     * 保存专属配置工具类
     */
    @Component
    public static class SaveHelper {

        @Resource
        private StreamExclusiveNodeConfigMapper mapper;

        /**
         * 保存专属配置
         *
         * @param requests         专属配置请求列表
         * @param dataModelNameMap 数据建模名称映射表
         */
        @Transactional(rollbackFor = Exception.class)
        public void saveExclusiveNodeConfigs(List<ExclusiveNodeConfigRequest> requests,
            Map<Integer, String> dataModelNameMap) {
            checkSaveParam(requests, dataModelNameMap);
            List<StreamExclusiveNodeConfig> oldExclusiveConfigList = mapper.selectAll();
            AddUpdateDeleteSeparator<ExclusiveNodeConfigRequest, Integer> separator = AddUpdateDeleteSeparator.create(
                oldExclusiveConfigList, AuditBaseEntity::getId, requests, ExclusiveNodeConfigRequest::getId);
            // 添加数据
            if (ObjectUtils.isNotEmpty(separator.getAddDataList())) {
                List<StreamExclusiveNodeConfig> configList = separator.getAddDataList().stream().map(
                    request -> buildStreamExclusiveNodeConfig(request, dataModelNameMap)).toList();
                mapper.insert(configList);
            }
            // 更新数据
            if (ObjectUtils.isNotEmpty(separator.getUpdateDataList())) {
                List<StreamExclusiveNodeConfig> configList = separator.getUpdateDataList().stream().map(
                    request -> buildStreamExclusiveNodeConfig(request, dataModelNameMap)).toList();
                mapper.updateById(configList);
            }
            // 删除数据
            if (ObjectUtils.isNotEmpty(separator.getDeleteKeyList())) {
                mapper.deleteByIds(separator.getDeleteKeyList());
            }
        }

        private void checkSaveParam(List<ExclusiveNodeConfigRequest> requests, Map<Integer, String> dataModelNameMap) {
            // 检查通用节点
            if (!requests.get(0).isCommonNodeConfig()) {
                throw new BizException("第1个配置必须是通用节点配置");
            }
            Integer expectNodeCount = requests.get(0).getExpectNodeCount();
            if (expectNodeCount < 1) {
                throw new BizException("通用节点配置的期望节点数必须大于0");
            }
            // 检查优先级是否递增
            for (int i = 1; i < requests.size(); i++) {
                ExclusiveNodeConfigRequest lastConfig = requests.get(i - 1);
                ExclusiveNodeConfigRequest thisConfig = requests.get(i);
                if (lastConfig.getPriority() > thisConfig.getPriority()) {
                    throw new BizException("优先级必须递增：第【%s】个配置的优先级大于第【%s】个配置", i - 1, i);
                }
            }
            // 检查数据建模是否重复
            RepeatChecker<Integer> checker = new RepeatChecker<>();
            for (ExclusiveNodeConfigRequest request : requests) {
                checker.batchCount(request.getDataModelIds(), Function.identity());
            }
            List<Entry<Integer, Integer>> repeatEntries = checker.getRepeatEntries();
            if (ObjectUtils.isNotEmpty(repeatEntries)) {
                StringBuilder sb = new StringBuilder();
                for (Entry<Integer, Integer> entry : repeatEntries) {
                    sb.append("【").append(dataModelNameMap.get(entry.getKey())).append("】数据建模重复【")
                        .append(entry.getValue()).append("】次\n");
                }
                throw new BizException("数据建模重复：\n%s", sb.toString());
            }
        }

        private StreamExclusiveNodeConfig buildStreamExclusiveNodeConfig(ExclusiveNodeConfigRequest request,
            Map<Integer, String> dataModelNameMap) {
            StreamExclusiveNodeConfig config = new StreamExclusiveNodeConfig();
            config.setId(request.getId());
            config.setDataModels(request.getDataModelIds().stream()
                .map(dataModelId -> new IdNameResponse(dataModelId, dataModelNameMap.get(dataModelId))).collect(
                    Collectors.toSet()));
            config.setExpectNodeCount(request.getExpectNodeCount());
            config.setPriority(request.getPriority());
            config.setConcurrentThreads(request.getConcurrentThreads());
            config.setRateLimit(request.getRateLimit());
            return config;
        }
    }
}
