package com.trs.ai.moye.knowledgebase.service;

import com.trs.ai.moye.common.entity.UsageInfoResponse;
import com.trs.ai.moye.common.response.DictResponse;
import com.trs.ai.moye.knowledgebase.request.KnowledgeBaseRequest;
import com.trs.ai.moye.knowledgebase.request.KnowledgeBaseUpdateRequest;
import com.trs.ai.moye.knowledgebase.response.KnowledgeBaseListResponse;
import com.trs.moye.base.common.request.BaseRequestParams;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.knowledgebase.entity.ImportEntityDataResult;
import com.trs.moye.base.knowledgebase.entity.KnowledgeBase;
import com.trs.moye.base.knowledgebase.entity.KnowledgeBaseField;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import com.trs.moye.base.knowledgebase.request.KnowledgeBaseFieldRequest;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * 实体业务接口
 *
 * <AUTHOR>
 * @since 2020/6/8 14:19
 */
public interface KnowledgeBaseService {

    /**
     * 添加知识库
     *
     * @param request 请求参数
     * @return 知识库id
     */
    int addKnowledgeBase(KnowledgeBaseRequest request);

    /**
     * 检查知识库名称是否可用 存在
     *
     * @param type     知识库类型
     * @param name     知识库名称
     * @param isZhName 是否为中文名称
     * @return 是否存在
     */
    boolean existKnowledgeBaseName(KnowledgeBaseType type, String name, boolean isZhName);

    /**
     * 修改知识库
     *
     * @param baseId  知识库id
     * @param request 请求参数
     */
    void updateKnowledgeBase(Integer baseId, KnowledgeBaseUpdateRequest request);

    /**
     * 获取知识库使用信息
     *
     * @param baseId 知识库id
     * @return 知识库使用信息
     */
    UsageInfoResponse getKnowledgeBaseUsageInfo(Integer baseId);

    /**
     * 删除知识库
     *
     * @param baseId 知识库id
     */
    void deleteKnowledgeBase(Integer baseId);

    /**
     * 知识库列表
     *
     * @param type 知识库类型
     * @return 知识库列表
     */
    List<KnowledgeBaseListResponse> getKnowledgeBaseList(KnowledgeBaseType type);

    /**
     * 知识库列表
     *
     * @return 知识库列表
     */
    List<KnowledgeBaseListResponse> getKnowledgeBaseList();

    /**
     * 修改字段
     *
     * @param baseId  知识库id
     * @param request 请求参数
     * @return 字段id
     */
    int addField(Integer baseId, KnowledgeBaseFieldRequest request);

    /**
     * 更新标签库打标能力
     *
     * @param base 标签库
     * <AUTHOR>
     * @since 2025/04/08 18:34:25
     */
    void updateTagLibMarkAbility(KnowledgeBase base);

    /**
     * 检查字段名称是否存在
     *
     * @param baseId   知识库id
     * @param name     字段名称
     * @param isZhName 是否为中文名称
     * @return 是否存在
     */
    boolean existFieldName(Integer baseId, String name, boolean isZhName);

    /**
     * 修改字段
     *
     * @param fieldId 字段id
     * @param request 请求参数
     */
    void updateField(Integer fieldId, KnowledgeBaseFieldRequest request);

    /**
     * 删除字段
     *
     * @param fieldId 字段id
     */
    void deleteField(Integer fieldId);

    /**
     * 更新字段显示顺序
     *
     * @param baseId   知识库ID
     * @param fieldIds 字段ID列表
     */
    void updateFieldShowOrder(Integer baseId, List<Integer> fieldIds);

    /**
     * 获取字段分页列表
     *
     * @param baseId  知识库id
     * @param request 请求参数
     * @return 字段列表
     */
    PageResponse<KnowledgeBaseField> getFieldPageList(Integer baseId, BaseRequestParams request);

    /**
     * 获取字段列表
     *
     * @param baseId 知识库id
     * @return 字段列表
     */
    List<KnowledgeBaseField> getFieldList(Integer baseId);

    /**
     * 获取知识库字典列表
     *
     * @param baseTypes 知识库类型列表，baseTypes含有哪个类型就返回哪个类型的所有知识库
     * @return 字典列表
     */
    List<DictResponse> getKnowledgeBaseDictList(Collection<KnowledgeBaseType> baseTypes);

    /**
     * 获取支持模糊搜索的字段
     *
     * @param baseId 知识库ID
     * @return 支持模糊搜索的字段
     */
    List<KnowledgeBaseField> getSupportFuzzySearchFieldList(Integer baseId);

    /**
     * 导入知识库数据
     *
     * @param baseId 知识库id
     * @param file   文件
     * @return 导入结果
     */
    ImportEntityDataResult importKnowledgeBaseDataList(Integer baseId, MultipartFile file) throws IOException;

    /**
     * 导出知识库数据
     *
     * @param baseId   知识库id
     * @param response http响应
     */
    void exportKnowledgeBaseDataList(Integer baseId, HttpServletResponse response) throws IOException;

    /**
     * 数据备份
     *
     * @param baseId 知识库id
     */
    void restoreKnowledgeBaseDataBackup(Integer baseId);


    /**
     * 知识库Excel模版下载
     *
     * @param baseId   知识库ID
     * @param response 响应
     * <AUTHOR>
     * @since 2024/11/25 11:26
     */
    void excelTemplateDownload(Integer baseId, HttpServletResponse response) throws IOException;
}
