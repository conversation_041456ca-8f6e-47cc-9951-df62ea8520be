package com.trs.ai.moye.knowledgebase.dao.dynamicrepository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.moye.base.knowledgebase.entity.EntityColumn;
import com.trs.moye.base.knowledgebase.entity.SqlColumn;
import com.trs.moye.base.knowledgebase.entity.TableInsertDataParam;
import com.trs.moye.base.common.request.SearchParams;
import com.trs.moye.base.common.request.SortParams;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Created by li.hao on 2020/6/11.
 */
@DS("moye_dynamic_repository")
@Mapper
public interface KnowledgeBaseDataMapper {

    /**
     * 删除表
     *
     * @param tableName 表名
     */
    void dropTable(@Param("tableName") String tableName);

    /**
     * 创建table表
     *
     * @param tableName 表名
     * @param columns   字段列表
     */
    void createTable(@Param("tableName") String tableName, @Param("columns") List<SqlColumn> columns);

    /**
     * 修改实体表名称
     *
     * @param oldTableName 旧表名
     * @param newTableName 新表名
     */
    void changeTableName(@Param("oldTableName") String oldTableName, @Param("newTableName") String newTableName);

    /**
     * 添加库表字段
     *
     * @param tableName 表名
     * @param columns   字段列表
     */
    void addTableColumn(@Param("tableName") String tableName, @Param("columns") List<SqlColumn> columns);

    /**
     * 修改库表的字段
     *
     * @param tableName 表名
     * @param oldZhName 旧中文名
     * @param column    字段信息
     */
    void changeTableColumn(@Param("tableName") String tableName, @Param("oldZhName") String oldZhName,
        @Param("column") SqlColumn column);

    /**
     * 删除实体表中的属性列
     *
     * @param entityTableName  实体表名
     * @param attributeZhNames 需要删除的列名
     */
    void deleteTableColumns(@Param("entityTableName") String entityTableName,
        @Param("attributeZhNames") List<String> attributeZhNames);

    /**
     * 向库表插入数据
     *
     * @param param 参数
     * @return int
     */
    int insertTableData(@Param("param") TableInsertDataParam param);

    /**
     * 修改数据
     *
     * @param tableName 表名
     * @param dataId    数据id
     * @param columns   字段列表
     */
    void updateTableData(@Param("tableName") String tableName, @Param("dataId") Integer dataId,
        @Param("columns") List<EntityColumn> columns);

    /**
     * 删除表数据
     *
     * @param tableName 表名
     * @param dataId    数据id
     */
    void deleteTableData(@Param("tableName") String tableName, @Param("dataId") Integer dataId);

    /**
     * 实体库、标签库列表查询。
     *
     * @param tableName  表名
     * @param searchable 搜索条件
     * @param sortable   排序
     * @param page       分页信息
     * @return 数据分页信息
     */
    Page<Map<String, Object>> selectTableDataList(@Param("tableName") String tableName,
        @Param("searchable") SearchParams searchable, @Param("sortable") SortParams sortable,
        Page<Map<String, Object>> page);

    /**
     * 根据数据编号查询数据信息
     *
     * @param tableName 表名
     * @param ids       数据id列表
     * @return 数据列表
     */
    List<Map<String, Object>> selectTableDataByIds(@Param("tableName") String tableName,
        @Param("ids") List<Integer> ids);

    /**
     * 根据表查询数据信息
     *
     * @param tableName 表名
     * @return 数据列表
     */
    List<Map<String, Object>> selectTableData(@Param("tableName") String tableName);

    /**
     * 根据表名、字段名、数据编号查询结果
     *
     * @param tableName   表名
     * @param columnName  字段名
     * @param columnValue 字段值
     * @return 数据
     */
    Map<String, Object> selectOneByColNameAndValue(@Param("tableName") String tableName,
        @Param("columnName") String columnName, @Param("columnValue") Object columnValue);

    /**
     * 判断表名是否存在
     *
     * @param tableName 表名
     * @return 是否存在
     */
    boolean existTable(@Param("tableName") String tableName);

    /**
     * 通过id列表批量删除表数据
     *
     * @param tableName 表名
     * @param ids       数据id列表
     */
    void deleteTableDataByIds(@Param("tableName") String tableName, @Param("ids") List<Integer> ids);

    /**
     * 实体库、标签库列表查询。注意！自定义过滤条件为高危操作，有sql注入的可能！
     *
     * @param tableName  表名
     * @param conditions 自定义过滤条件
     * @param sortable   排序
     * @param page       分页信息
     * @return 分页信息
     * <AUTHOR>
     * @since 2022/12/5 18:11
     */
    Page<Map<String, Object>> getConditionPageList(
        @Param("tableName") String tableName,
        @Param("conditions") String conditions,
        @Param("sortable") SortParams sortable,
        Page<Map<String, Object>> page
    );

    /**
     * 多搜索条件查询
     *
     * @param tableName  表名
     * @param searchable 搜索条件
     * @param sortable   排序条件
     * @param page       分页信息
     * @return 分页信息
     */
    Page<Map<String, Object>> getMultiSearchPageList(
        @Param("tableName") String tableName,
        @Param("searchable") List<SearchParams> searchable,
        @Param("sortable") SortParams sortable, Page<Map<String, Object>> page);

    /**
     * 查询数据条数
     *
     * @param tableName 表名
     * @return 条数
     */
    Integer selectBaseDataCount(@Param("tableName") String tableName);

    /**
     * 搜索属性所有属性（去重，支持检索）
     *
     * @param tableName 表名
     * @param attrName  属性名
     * @param keyword   查询的关键字
     * @param situation 舆论场
     * @param sortable  排序字段
     * @return 属性值列表
     */
    List<String> selectAttrData(
        @Param("tableName") String tableName,
        @Param("attrName") String attrName,
        @Param("keyword") String keyword,
        @Param("situation") String situation,
        @Param("sortable") SortParams sortable);

    /**
     * 查询媒体库的站点和账号信息
     *
     * @param tableName 表名
     * @param situation 舆论场
     * @param nature    性质
     * @param level     级别
     * @param region    地域
     * @return 账号或媒体字段值列表
     */
    List<String> selectAccountOfMediaFieldValue(
        @Param("tableName") String tableName,
        @Param("situation") String situation,
        @Param("nature") String nature,
        @Param("level") String level,
        @Param("region") String region);

    /**
     * 查询关键字的排行
     *
     * @param tableName   表名
     * @param searchField 搜索字段
     * @param keywords    搜索关键字
     * @param sortable    排序参数
     * @return 关键字列表
     */
    List<String> selectDataRank(
        @Param("tableName") String tableName,
        @Param("searchField") String searchField,
        @Param("keywords") List<String> keywords,
        @Param("sortable") SortParams sortable);


    /**
     * 插入数据
     *
     * @param resultList 结果列表
     * @param titles     表头属性
     * @param tableName  表名
     * <AUTHOR>
     * @since 2024/11/26 14:57
     */
    void insertEntityCommon(@Param("resultList") List<List<Object>> resultList,
        @Param("titles") LinkedList<String> titles,
        @Param("tableName") String tableName);

    /**
     * 创建备份表或还原，包括结构和数据
     *
     * @param tableName        原表名
     * @param backupsTableName 备份表名
     * <AUTHOR>
     * @since 2021/3/3 14:41
     */
    void copyTable(
        @Param("tableName") String tableName,
        @Param("backupsTableName") String backupsTableName);

}
