package com.trs.ai.moye.backstage.help;

import com.trs.ai.moye.common.web.filter.RequestUtils;
import com.trs.ai.moye.log.KafkaLogService;
import com.trs.moye.base.common.annotaion.enums.SignField;
import com.trs.moye.base.common.annotaion.processor.fieldsign.FieldSignProcessor;
import com.trs.moye.base.common.annotaion.processor.fieldsign.SignFieldReader;
import com.trs.moye.base.common.log.api.ApiLogTrace;
import com.trs.moye.base.common.log.api.ApiLogTracerUtils;
import com.trs.moye.base.common.entity.FieldSignParseParam;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.help.container.DefaultSyncContainer;
import com.trs.moye.base.common.utils.JsonUtils;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025-03-04 17:58
 */
@Order(1000)
@Slf4j
@Aspect
@Component
public class ApiLogTracerSignAspect {

    private static final Supplier<Object> DEFAULT_SIGN_FIELD_SUPPLIER = () -> {
        throw new BizException("无参方法无法解析日志id");
    };

    private final DefaultSyncContainer<ProceedingJoinPoint, Method, SignFieldReader> signFieldReaderContainer =
        new DefaultSyncContainer<>(this::getInterceptMethod, this::parseSessionIdReader);

    @Resource
    private KafkaLogService kafkaLogService;

    @Value("${spring.application.name}")
    private String applicationName;

    @PostConstruct
    private void init(){
        ApiLogTracerUtils.setApplicationName(applicationName);
    }

    /**
     * 定义切点为带有 @ApiLogSign 注解的方法
     */
    @Pointcut("@annotation(com.trs.moye.base.common.annotaion.ApiLogTracerSign)")
    public void apiLogTracerSignPointcut() {
    }

    /**
     * 使用环绕通知处理切点
     *
     * @param joinPoint 切点连接点
     * @throws Throwable 异常信息
     * @return 返回结果
     */
    @Around("apiLogTracerSignPointcut()")
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            Long logId = getLodId();
            if (Objects.nonNull(logId)) {
                ApiLogTracerUtils.initLocalTracer(logId);
            }
            return joinPoint.proceed();
        } finally {
            List<ApiLogTrace> traceList = ApiLogTracerUtils.getTraceList();
            ApiLogTracerUtils.remove();
            sendLog(traceList);
        }
    }

    private void sendLog(List<ApiLogTrace> traces) {
        try {
            kafkaLogService.sendApiLogTracers(traces);
        } catch (Exception e) {
            log.error("发送API-tracer日志失败，日志信息：{}", JsonUtils.toJsonString(traces), e);
        }
    }

    private Long getLodId(){
        String logIdStr = RequestUtils.getRequestHeader(ApiLogTracerUtils.LOG_ID_HEADER);
        return logIdStr == null || logIdStr.isEmpty() ? null : Long.parseLong(logIdStr);
    }

    private Long parseLogId(ProceedingJoinPoint joinPoint) {
        SignFieldReader signFieldReader = signFieldReaderContainer.getOrCreateElement(joinPoint);
        Object signFieldValue = signFieldReader.reader(joinPoint.getArgs());
        if (ObjectUtils.isEmpty(signFieldValue)) {
            return null;
        }
        if (signFieldValue instanceof Long longValue) {
            return longValue;
        } else {
            try {
                return Long.parseLong(signFieldValue.toString());
            } catch (NumberFormatException e) {
                Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
                throw new IllegalArgumentException(
                    String.format(
                        "【%s】类【%s】方法日志id解析失败，应为Long类型", method.getDeclaringClass().getName(), method.getName()));
            }
        }
    }

    private Method getInterceptMethod(ProceedingJoinPoint joinPoint) {
        return ((MethodSignature) joinPoint.getSignature()).getMethod();
    }

    private SignFieldReader parseSessionIdReader(ProceedingJoinPoint joinPoint) {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        Object[] args = joinPoint.getArgs();
        FieldSignParseParam param = new FieldSignParseParam();
        param.setMethod(method);
        param.setArgs(args);
        param.setSignField(SignField.LOG_ID);
        param.setDefaultSignFieldValueSupplier(DEFAULT_SIGN_FIELD_SUPPLIER);
        return FieldSignProcessor.parseSignFieldReader(param);
    }


}

