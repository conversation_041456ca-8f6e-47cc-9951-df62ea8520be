package com.trs.ai.moye.backstage.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.backstage.entity.NoticeReadRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 消息-用户关联
 *
 * <AUTHOR>
 * @since 2024/8/29
 */
@Mapper
public interface NoticeReadRelationMapper extends BaseMapper<NoticeReadRelation> {

    /**
     * 根据消息id和用户id查询已读状态
     *
     * @param noticeId 消息id
     * @param userId   用户id
     * @return {@link NoticeReadRelation}
     */
    @Select("select * from notice_read_relation where notice_id = #{noticeId} and user_id = #{userId}")
    NoticeReadRelation selectByNoticeIdAndUserId(@Param("noticeId") Integer noticeId, @Param("userId") Integer userId);

    /**
     * 已读全部消息
     *
     * @param userId 用户id
     */
    @Update("update notice_read_relation set is_read = 1 where user_id = #{userId}")
    void readAll(@Param("userId") Integer userId);
}
