package com.trs.ai.moye.backstage.response.exclusive;

import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-01-13 20:31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class ExclusiveDataModelListResponse extends ExclusiveBaseResponse {

    private List<ExclusiveDataModelResponse> dataModelList;

}
