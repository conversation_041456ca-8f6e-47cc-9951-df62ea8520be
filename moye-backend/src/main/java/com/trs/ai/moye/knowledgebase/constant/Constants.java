package com.trs.ai.moye.knowledgebase.constant;

import com.trs.moye.base.common.enums.FieldType;
import java.util.Set;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 系统常量
 *
 * <AUTHOR>
 * @since 2020/8/27 17:37
 */
public class Constants {

    private Constants() {
    }


    /**
     * 实体管理：备份数据库表后缀
     */
    public static final String BACKUP_TABLE_SUFFIX = "_backup";

    public static final String MSG_KNOWLEDGE_BASE_NOT_EXIST = "主键为【%s】的知识库不存在";

    public static final String MSG_KNOWLEDGE_BASE_EXIST_EN_NAME = "英文名为【%s】的知识库已存在";

    public static final String MSG_KNOWLEDGE_BASE_EXIST_ZH_NAME = "中文名为【%s】的知识库已存在";

    public static final String MSG_KNOWLEDGE_BASE_FIELD_NOT_EXIST = "主键为【%s】的字段不存在";

    public static final String MSG_KNOWLEDGE_BASE_FIELD_EXIST_EN_NAME = "【%s】知识库已存在英文名为【%s】的字段";

    public static final String MSG_KNOWLEDGE_BASE_FIELD_EXIST_ZH_NAME = "【%s】知识库已存在中文名为【%s】的字段";

    public static final Set<FieldType> NOT_SUPPORT_FUZZY_SEARCH_FIELD_SET = Set.of(FieldType.DATE, FieldType.DATETIME);

    public static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(
        1,
        1,
        0L,
        TimeUnit.MINUTES,
        new LinkedBlockingQueue<>());
}
