package com.trs.ai.moye.backstage.controller;

import com.trs.ai.moye.backstage.dao.RoleMapper;
import com.trs.ai.moye.backstage.request.RoleRequest;
import com.trs.ai.moye.backstage.response.CertificateResponse;
import com.trs.ai.moye.backstage.response.RoleResponse;
import com.trs.ai.moye.backstage.response.RoleSimpleResponse;
import com.trs.ai.moye.backstage.service.RoleService;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.request.BaseRequestParams;
import com.trs.moye.base.common.group.Insert;
import com.trs.moye.base.common.group.Update;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 角色管理
 *
 * <AUTHOR>
 * @since 2024/9/25 15:17
 **/
@RestController
@RequestMapping("/role")
public class RoleController {


    @Resource
    private RoleService roleService;
    @Resource
    private RoleMapper roleMapper;

    /**
     * 新增角色
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/163995">新增角色</a>
     *
     * @param role 角色
     */
    @PostMapping()
    public void addRole(@RequestBody @Validated(Insert.class) RoleRequest role) {
        roleService.addRole(role);
    }

    /**
     * 更新角色
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/164020">更新角色</a>
     *
     * @param role 角色
     */
    @PutMapping()
    public void updateRole(@RequestBody @Validated(Update.class) RoleRequest role) {
        roleService.updateRole(role);
    }

    /**
     * 删除角色
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/164030>删除角色</a>
     *
     * @param id 角色id
     */
    @DeleteMapping("/{id}")
    public void deleteRole(@PathVariable Integer id) {
        roleService.deleteRole(id);
    }

    /**
     * 获取角色分页数据
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/164010">获取角色分页数据</a>
     *
     * @param request 请求参数
     * @return 角色列表
     */
    @PostMapping("/page-list")
    public PageResponse<RoleResponse> pageList(@RequestBody @Validated BaseRequestParams request) {
        return roleService.pageList(request);
    }

    /**
     * 获取角色列表
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/163975">获取角色列表</a>
     *
     * @return 角色列表
     */
    @GetMapping("/list")
    public List<RoleSimpleResponse> list() {
        return roleMapper.selectSimpleInfo();
    }

    /**
     * 根据id获取详情
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/164015">根据id获取详情</a>
     *
     * @param id 角色id
     * @return 角色详情
     */
    @GetMapping("/{id}")
    public RoleResponse detail(@PathVariable Integer id) {
        return roleService.getDetail(id);
    }

    /**
     * 检查角色名称是否重复
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/165090">检查角色名称是否重复</a>
     *
     * @param request 请求参数
     * @return 是否重复
     */
    @PostMapping("name-repeat")
    public boolean checkNameRepeat(@RequestBody @Validated RoleRequest request) {
        return roleService.isRoleNameDuplicate(request.getName(), request.getId());
    }

    /**
     * 获取凭证列表
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/165385">获取凭证列表</a>
     *
     * @return 凭证列表
     */
    @GetMapping("/credentials")
    public List<CertificateResponse> selectCredentialsList() {
        return roleService.selectCredentialsList();
    }
}
