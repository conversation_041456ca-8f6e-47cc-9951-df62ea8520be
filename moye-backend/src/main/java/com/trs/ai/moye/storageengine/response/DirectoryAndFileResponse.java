package com.trs.ai.moye.storageengine.response;

import com.trs.ai.moye.storageengine.response.alltable.FileTableResponse;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 目录和文件响应对象
 *
 * <AUTHOR>
 * @since 2024-10-09 18:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DirectoryAndFileResponse {

    /**
     * 目录列表
     */
    private List<FileTableResponse> directories;

    /**
     * 文件列表
     */
    private List<FileTableResponse> files;
}
