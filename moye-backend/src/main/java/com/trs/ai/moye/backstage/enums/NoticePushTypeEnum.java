package com.trs.ai.moye.backstage.enums;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息中心消息推送方式的类型枚举
 */
@Getter
@AllArgsConstructor
public enum NoticePushTypeEnum {


    WEBSOCKET(1, "站内推送"), HTTP(2, "API推送"), MQ(3, "消息队列");

    private final Integer code;
    private final String name;


    /**
     * 获取名称
     *
     * @param code id
     * @return {@link String}
     * <AUTHOR>
     * @since 2024/11/4 18:27
     */
    public static String getNameByCode(Integer code) {
        for (NoticePushTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return null;
    }


    /**
     * 内部类，用于描述枚举字段信息
     *
     * @param code 代码
     * @param name 名称
     * <AUTHOR>
     * @since 2024/11/4 18:11
     */
    public record NoticePushType(Integer code, String name) {


        /**
         * 获取类型
         *
         * @return {@link NoticePushType}
         * <AUTHOR>
         * @since 2024/11/4 18:10
         */
        public static List<NoticePushType> getAllNoticePushTypes() {
            List<NoticePushType> enumValues = new ArrayList<>();
            for (NoticePushTypeEnum value : NoticePushTypeEnum.values()) {
                enumValues.add(new NoticePushType(value.getCode(), value.getName()));
            }
            return enumValues;
        }
    }
}
