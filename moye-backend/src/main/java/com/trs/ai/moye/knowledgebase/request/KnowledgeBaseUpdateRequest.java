package com.trs.ai.moye.knowledgebase.request;

import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-20 21:40
 */
@Data
@NoArgsConstructor
public class KnowledgeBaseUpdateRequest {

    /**
     * 实体中文名称
     */
    @NotBlank(message = "知识库中文名称不能为空")
    private String zhName;

    /**
     * 实体英文名称
     */
    @NotBlank(message = "知识库英文名称不能为空")
    private String enName;

    @Length(max = 1000, message = "描述信息长度不能超过1000个字符")
    private String desc;

    /**
     * 实体标签 用于分组
     */
    private List<String> tags;
}
