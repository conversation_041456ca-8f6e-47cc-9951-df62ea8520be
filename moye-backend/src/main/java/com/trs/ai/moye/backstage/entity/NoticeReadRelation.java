package com.trs.ai.moye.backstage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.ai.moye.backstage.response.NoticeResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/8/29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("notice_read_relation")
public class NoticeReadRelation {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 消息id
     */
    private Integer noticeId;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 是否已读 true-已读
     */
    private Boolean isRead;


    public NoticeReadRelation(NoticeResponse response, Integer userId) {
        this.noticeId = response.getId();
        this.userId = userId;
        this.isRead = true;
    }

    public NoticeReadRelation(Integer noticeId, Integer userId) {
        this.noticeId = noticeId;
        this.userId = userId;
        this.isRead = true;
    }

}
