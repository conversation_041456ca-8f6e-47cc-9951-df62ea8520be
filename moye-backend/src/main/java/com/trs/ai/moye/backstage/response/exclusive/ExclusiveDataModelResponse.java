package com.trs.ai.moye.backstage.response.exclusive;

import com.trs.ai.moye.backstage.entity.exclusive.BaseProcessInfo;
import com.trs.ai.moye.backstage.entity.exclusive.EngineNodeProcessInfo;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-01-13 20:31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class ExclusiveDataModelResponse extends BaseProcessInfo {

    /**
     * 数据建模id
     */
    private Integer id;

    /**
     * 数据建模名称
     */
    private String name;

    /**
     * 期望节点数
     */
    private Integer expectNodeCount;

    /**
     * 引擎节点列表
     */
    private List<EngineNodeProcessInfo> engineNodeList;

    /**
     * 合并引擎节点处理信息
     */
    public void mergeEngineNodeProcessInfo(){
        if (engineNodeList == null){
            this.accessCount = this.accessCount == null ? 0 : this.accessCount;
            this.processCount = 0;
            this.processErrorCount = 0;
            this.processingTimeMillis = 0L;
        }
        else {
            this.accessCount = this.accessCount == null ? 0 : this.accessCount;
            this.processCount = engineNodeList.stream().mapToInt(EngineNodeProcessInfo::getProcessCount).sum();
            this.processErrorCount = engineNodeList.stream().mapToInt(EngineNodeProcessInfo::getProcessErrorCount).sum();
            this.processingTimeMillis = engineNodeList.stream().mapToLong(EngineNodeProcessInfo::getProcessingTimeMillis).sum();
        }
    }
}
