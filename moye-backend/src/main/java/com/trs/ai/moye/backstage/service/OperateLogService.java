package com.trs.ai.moye.backstage.service;

import com.trs.ai.moye.backstage.request.OperateLogPageListRequest;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.log.operate.OperateLog;

/**
 * <AUTHOR>
 * @since 2025-03-06 15:39
 */
public interface OperateLogService {

    /**
     * 分页列表
     *
     * @param request 请求参数
     * @return 配置
     */
    PageResponse<OperateLog> pageList(OperateLogPageListRequest request);
}
