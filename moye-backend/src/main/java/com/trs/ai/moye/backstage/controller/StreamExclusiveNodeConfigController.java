package com.trs.ai.moye.backstage.controller;

import com.trs.ai.moye.backstage.request.ExclusiveDataModelListRequest;
import com.trs.ai.moye.backstage.request.ExclusiveNodeConfigRequest;
import com.trs.ai.moye.backstage.response.exclusive.ExclusiveDataModelListResponse;
import com.trs.ai.moye.backstage.response.exclusive.ExclusiveNodeConfigListResponse;
import com.trs.ai.moye.backstage.service.StreamExclusiveNodeConfigService;
import com.trs.moye.base.common.response.IdNameResponse;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 专属节点配置控制器
 */
@Validated
@RestController
@RequestMapping("/exclusive-node-config")
public class StreamExclusiveNodeConfigController {

    @Resource
    private StreamExclusiveNodeConfigService service;

    /**
     * 可选择的数据建模列表
     *
     * @return 数据模型列表
     */
    @GetMapping("/selectable/model-list")
    public List<IdNameResponse> selectableDataModelList() {
        return service.selectableDataModelList();
    }

    /**
     * 专属配置列表
     *
     * @return 数据模型列表
     */
    @GetMapping("/exclusive-config-list")
    public ExclusiveNodeConfigListResponse exclusiveConfigList() {
        return service.exclusiveConfigList();
    }

    /**
     * 专属监控列表
     *
     * @param request 请求参数
     * @return 数据模型列表
     */
    @PostMapping("/exclusive-monitor-list")
    public ExclusiveDataModelListResponse exclusiveMonitorList(@Validated @RequestBody ExclusiveDataModelListRequest request) {
        return service.exclusiveMonitorList(request);
    }

    /**
     * 保存专属节点配置
     *
     * @param requests 专属节点配置列表
     */
    @PostMapping("/save")
    public void saveExclusiveNodeConfigs(@NotEmpty @Validated @RequestBody List<ExclusiveNodeConfigRequest> requests) {
        service.saveExclusiveNodeConfigs(requests);
    }
}
