package com.trs.ai.moye.backstage.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.backstage.entity.UserLog;
import java.time.LocalDateTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * user_log表数据访问接口
 *
 * <AUTHOR>
 */
@Mapper
public interface UserLogMapper extends BaseMapper<UserLog> {

    /**
     * 删除用户日志
     *
     * @param userId 用户id
     * @return int
     */
    int deleteByUserId(Integer userId);

    /**
     * 根据用户账号和登录时间查询记录
     *
     * @param account 账号
     * @param loginTime 登录时间
     * @return {@link UserLog}
     */
    UserLog selectByAccountAndLoginTime(@Param("account") String account, @Param("loginTime")LocalDateTime loginTime);
}