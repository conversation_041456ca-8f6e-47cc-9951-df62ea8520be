package com.trs.ai.moye.backstage.entity.ui;

import com.trs.ai.moye.backstage.enums.AppConfigType;
import com.trs.moye.base.common.annotaion.JsonBean;
import com.trs.moye.base.common.annotaion.PolymorphismSign;
import com.trs.moye.base.common.deserializer.PolymorphismDeserializer;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 前端配置参数
 *
 * <AUTHOR>
 * @since 2024-12-05 14:03
 */
@JsonBean(deserializer = PolymorphismDeserializer.class)
@Data
@NoArgsConstructor
public abstract class AppConfigParam {

    @NotNull(message = "配置类型不能为空")
    @PolymorphismSign
    private AppConfigType type;
}
