package com.trs.ai.moye.backstage.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import com.trs.ai.moye.backstage.enums.UserType;
import com.trs.ai.moye.backstage.request.UserRequest;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * 用户
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/9/29 14:42
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@TableName(value = "users", autoResultMap = true)
@AllArgsConstructor
public class User extends AuditBaseEntity implements Serializable {

    /**
     * 用户名
     */
    private String name;

    /**
     * 用户类型
     */
    private UserType type;

    /**
     * 账号
     */
    private String account;

    /**
     * 密码
     */
    private String password;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 部门id
     */
    private Integer departmentId;

    /**
     * 用户id列表
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private List<Integer> roleIds;

    /**
     * 启用状态
     */
    private boolean isEnable;

    /**
     * 登录失败次数
     */
    private Integer loginFailedNum = 0;

    /**
     * 修改密码时间
     */
    private LocalDateTime pwdUpdateTime;

    public User(UserRequest request) {
        this.name = request.getName();
        this.account = request.getAccount();
        this.password = request.getPassword();
        this.departmentId = request.getDepartmentId();
        this.telephone = request.getTelephone();
        this.email = request.getEmail();
        this.type = request.getType();
        this.roleIds = request.getRoleIds();
    }

    /**
     * 创建UserDetails
     *
     * @return {@link UserDetails}
     */
    public UserDetails createUserDetails() {
        return new org.springframework.security.core.userdetails.User(account, password, new ArrayList<>());
    }
}
