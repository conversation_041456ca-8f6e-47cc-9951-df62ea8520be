package com.trs.ai.moye.storageengine.entity;

import com.trs.ai.moye.data.service.entity.DataServiceStatDims;
import com.trs.ai.moye.data.service.entity.query.Condition;
import java.util.List;
import lombok.Data;

/**
 * 搜索参数，storageEngine中findByList，preview等接口使用
 *
 * <AUTHOR>
 */
@Data
public class SearchParams {

    private String groupField;
    private List<SortField> sortFields;
    private String operationType;
    private List<String> returnFields;
    private List<Condition> conditions;
    private List<DataServiceStatDims> statDims;
}
