package com.trs.ai.moye.backstage.service;

import com.trs.ai.moye.backstage.request.NoticeListRequest;
import com.trs.ai.moye.backstage.response.ApiNoticeResponse;
import com.trs.ai.moye.backstage.response.InsideNoticeResponse;
import com.trs.ai.moye.backstage.response.MessagePushRecordResponse;
import com.trs.ai.moye.backstage.response.NoticeResponse;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.ai.moye.data.model.request.MessagePushRecordRequest;
import java.util.List;

/**
 * 消息推送中心服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/30 14:46
 **/
public interface NoticeCenterService {


    /**
     * 获取站内消息列表
     *
     * @param request 前端请求
     * @return {@link InsideNoticeResponse}
     * <AUTHOR>
     * @since 2024/10/31 14:50
     */
    PageResponse<InsideNoticeResponse> getInsideNoticeList(NoticeListRequest request);


    /**
     * 获取API消息推送策略列表
     *
     * @param request 前端请求
     * @return {@link ApiNoticeResponse}
     * <AUTHOR>
     * @since 2024/11/1 11:06
     */
    PageResponse<ApiNoticeResponse> getApiMsgSendStrategyList(NoticeListRequest request);


    /**
     * 获取消息列表
     *
     * @param request 前端请求
     * @return {@link NoticeResponse}
     * <AUTHOR>
     * @since 2024/11/4 11:26
     */
    PageResponse<NoticeResponse> getNoticeList(NoticeListRequest request);


    /**
     * 获取推送记录日志
     *
     * @param request 前端请求
     * @return {@link MessagePushRecordResponse }
     * <AUTHOR>
     * @since 2024/11/4 16:16
     */
    PageResponse<MessagePushRecordResponse> listPushRecords(MessagePushRecordRequest request);

    /**
     * 用户消息列表查询
     *
     * @param request 参数
     * @return 消息列表
     */
    PageResponse<NoticeResponse> userList(NoticeListRequest request);

    /**
     * 已读全部消息
     */
    void readAllNotice();

    /**
     * 已读单条消息
     *
     * @param id 消息id
     */
    void readSingleNotice(Integer id);

    /**
     * 查询全部未读消息
     *
     * @return 全部未读消息
     */
    List<NoticeResponse> getAllUnread();
}
