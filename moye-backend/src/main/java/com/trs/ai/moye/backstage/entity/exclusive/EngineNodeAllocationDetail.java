package com.trs.ai.moye.backstage.entity.exclusive;

import com.trs.moye.base.common.response.IdNameResponse;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 引擎节点分配详情
 *
 * <AUTHOR>
 * @since 2025-01-10 13:48
 */
@Data
@NoArgsConstructor
public class EngineNodeAllocationDetail {

    /**
     * 执行分配策略的节点
     */
    @NotBlank(message = "执行分配策略的节点不能为空")
    private String executeNode;

    /**
     * 执行分配策略开始时间
     */
    @NotNull(message = "执行分配策略开始时间不能为空")
    private LocalDateTime executeStartTime;

    /**
     * 执行分配策略结束时间
     */
    @NotNull(message = "执行分配策略结束时间不能为空")
    private LocalDateTime executeEndTime;

    /**
     * 摘要信息：专属节点分配结果的摘要信息
     */
    @NotNull(message = "摘要信息不能为空")
    private Summary summary;

    /**
     * 实际节点列表：触发节点分配时的实际节点列表
     */
    @NotNull(message = "实际节点列表不能为空")
    private Set<String> allActualNodes;

    /**
     * 引擎配置列表：触发节点分配时的引擎配置及实际分配给其的实际节点列表
     */
    @Valid
    @NotEmpty(message = "引擎配置列表不能为空")
    private List<ExclusiveConfigDetail> exclusiveConfigDetailList;

    /**
     * 构建数据模型节点详情映射表
     *
     * @return 数据模型节点详情Map,key为数据模型ID，value为DataModelNodeDetail对象
     */
    public Map<Integer, DataModelNodeDetail> buildDataModelNodeDetailMap() {
        Map<Integer, DataModelNodeDetail> dataModelNodeDetailMap = new LinkedHashMap<>();
        if (exclusiveConfigDetailList == null) {
            return dataModelNodeDetailMap;
        }
        for (ExclusiveConfigDetail detail : exclusiveConfigDetailList) {
            Set<IdNameResponse> dataModels = detail.getDataModels();
            for (IdNameResponse dataModel : dataModels) {
                DataModelNodeDetail dataModelNodeDetail = new DataModelNodeDetail();
                dataModelNodeDetail.setId(dataModel.getId());
                dataModelNodeDetail.setName(dataModel.getName());
                dataModelNodeDetail.setExpectNodeCount(detail.getExpectNodeCount());
                dataModelNodeDetail.setActualNodes(detail.getActualNodes());
                dataModelNodeDetailMap.put(dataModelNodeDetail.getId(), dataModelNodeDetail);
            }
        }
        return dataModelNodeDetailMap;
    }
}

