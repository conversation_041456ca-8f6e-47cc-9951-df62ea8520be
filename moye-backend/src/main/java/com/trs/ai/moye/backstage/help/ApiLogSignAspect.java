package com.trs.ai.moye.backstage.help;

import com.trs.ai.moye.common.web.filter.RequestUtils;
import com.trs.ai.moye.log.KafkaLogService;
import com.trs.moye.base.common.annotaion.ApiLogSign;
import com.trs.moye.base.common.annotaion.enums.SignField;
import com.trs.moye.base.common.annotaion.processor.fieldsign.FieldSignProcessor;
import com.trs.moye.base.common.annotaion.processor.fieldsign.SignFieldReader;
import com.trs.moye.base.common.log.api.ApiLog;
import com.trs.moye.base.common.log.api.ApiLogTrace;
import com.trs.moye.base.common.log.api.ApiLogTracerUtils;
import com.trs.moye.base.common.log.api.ApiLogUtils;
import com.trs.moye.base.common.entity.FieldSignParseParam;
import com.trs.moye.base.common.enums.ResultType;
import com.trs.moye.base.common.help.container.DefaultSyncContainer;
import com.trs.moye.base.common.utils.ExceptionUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.common.utils.SnowflakeIdUtil;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025-03-04 17:58
 */
@Order(2000)
@Slf4j
@Aspect
@Component
public class ApiLogSignAspect {

    private static final Supplier<Object> DEFAULT_SESSION_ID_SUPPLIER = () -> SnowflakeIdUtil.newId() + "";

    private final DefaultSyncContainer<ProceedingJoinPoint, Method, SignFieldReader> sessionIdReaderContainer =
        new DefaultSyncContainer<>(this::getInterceptMethod, this::parseSessionIdReader);

    @Resource
    private KafkaLogService kafkaLogService;

    @Value("${spring.application.name}")
    private String applicationName;

    /**
     * 定义切点为带有 @ApiLogSign 注解的方法
     */
    @Pointcut("@annotation(com.trs.moye.base.common.annotaion.ApiLogSign)")
    public void apiLogSignPointcut() {
    }

    /**
     * 使用环绕通知处理切点
     *
     * @param joinPoint 切点对象
     * @return 处理结果或抛出异常
     * @throws Throwable 异常信息
     */
    @Around("apiLogSignPointcut()")
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        if (ApiLogTracerUtils.containLocalTracer()) {
            return joinPoint.proceed();
        }
        ApiLog apiLog = createApiLog(joinPoint);
        try {
            ApiLogUtils.set(apiLog);
            ApiLogTracerUtils.initLocalTracer(apiLog.getId());
            Object result = joinPoint.proceed();
            supplementSuccessEndInfo(result);
            return result;
        } catch (Exception e) {
            // 3. 异常处理：记录异常日志
            supplementErrorEndInfo(e);
            throw e;
        } finally {
            ApiLogUtils.remove();
            sendApiLog(apiLog);
            List<ApiLogTrace> traceList = ApiLogTracerUtils.getTraceList();
            ApiLogTracerUtils.remove();
            sendApiLogTrace(traceList);
        }
    }

    private ApiLog createApiLog(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        ApiLogSign sign = method.getAnnotation(ApiLogSign.class);
        ApiLog apiLog = new ApiLog();
        apiLog.setId(SnowflakeIdUtil.newId());
        apiLog.setApiName(sign.apiName().isEmpty() ? method.getName() : sign.apiName());
        apiLog.setApplicationName(applicationName);
        apiLog.setRequestTime(LocalDateTime.now());
        apiLog.setApiPath(RequestUtils.getUrlPath());
        Object[] args = joinPoint.getArgs();
        apiLog.setRequestParameters(parseRequestParameters(method, args));
        apiLog.setExternalLogId(RequestUtils.getRequestHeader(ApiLogUtils.EXTERNAL_LOG_ID_HEADER));
        apiLog.setResponseDetails(new LinkedHashMap<>());
        return apiLog;
    }

    private void supplementSuccessEndInfo(Object result) {
        ApiLog apiLog = ApiLogUtils.get();
        apiLog.setResponseStatus(ResultType.SUCCESS);
        apiLog.setResponseDuration(Duration.between(apiLog.getRequestTime(), LocalDateTime.now()).toMillis());
        apiLog.getResponseDetails().put("返回结果", result);
    }

    private void supplementErrorEndInfo(Exception e) {
        ApiLog apiLog = ApiLogUtils.get();
        apiLog.setResponseStatus(ResultType.FAILURE);
        apiLog.setResponseDuration(Duration.between(apiLog.getRequestTime(), LocalDateTime.now()).toMillis());
        apiLog.getResponseDetails().put("错误信息", ExceptionUtils.readTraceMessage(e));
    }

    private void sendApiLog(ApiLog apiLog) {
        try {
            kafkaLogService.sendApiLog(apiLog);
        } catch (Exception e) {
            log.error("发送API日志失败，日志信息：{}", JsonUtils.toJsonString(apiLog), e);
        }
    }

    private void sendApiLogTrace(List<ApiLogTrace> traces) {
        try {
            kafkaLogService.sendApiLogTracers(traces);
        } catch (Exception e) {
            log.error("发送API-tracer日志失败，日志信息：{}", JsonUtils.toJsonString(traces), e);
        }
    }


    private Map<String, Object> parseRequestParameters(Method method, Object[] args) {
        Map<String, Object> requestParameters = new HashMap<>();
        if (ObjectUtils.isEmpty(args)) {
            return requestParameters;
        }
        Parameter[] parameters = method.getParameters();
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            requestParameters.put(parameter.getName(), args[i]);
        }
        return requestParameters;
    }

    private String parseSessionId(ProceedingJoinPoint joinPoint) {
        SignFieldReader sessionIdReader = sessionIdReaderContainer.getOrCreateElement(joinPoint);
        Object sessionId = sessionIdReader.reader(joinPoint.getArgs());
        return ObjectUtils.isEmpty(sessionId) ? (String) DEFAULT_SESSION_ID_SUPPLIER.get() : sessionId.toString();
    }

    private Method getInterceptMethod(ProceedingJoinPoint joinPoint) {
        return ((MethodSignature) joinPoint.getSignature()).getMethod();
    }

    private SignFieldReader parseSessionIdReader(ProceedingJoinPoint joinPoint) {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        Object[] args = joinPoint.getArgs();
        FieldSignParseParam param = new FieldSignParseParam();
        param.setMethod(method);
        param.setArgs(args);
        param.setDefaultSignFieldValueSupplier(DEFAULT_SESSION_ID_SUPPLIER);
        param.setSignField(SignField.SESSION_ID);
        return FieldSignProcessor.parseSignFieldReader(param);
    }


}

