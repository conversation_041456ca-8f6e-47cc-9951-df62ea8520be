package com.trs.ai.moye.backstage.entity.ui;

import com.trs.ai.moye.backstage.enums.HomePageShowType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/4/25
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class HomePageAppConfigParam extends AppConfigParam {

    /**
     * 主题目录id
     */
    private Integer subjectCategoryId;
    /**
     * 主题目录名称
     */
    private String subjectCategoryName;

    // 主题id
    private Integer subjectId;
    // 主题名称
    private String subjectName;

    private HomePageShowType homePageShowType;
}
