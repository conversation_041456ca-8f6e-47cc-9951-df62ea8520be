package com.trs.ai.moye.data.connection.service;

import com.trs.ai.moye.data.connection.request.ConnectionListRequest;
import com.trs.ai.moye.data.connection.response.DataConnectionCardResponse;
import com.trs.ai.moye.data.connection.response.DataConnectionStatisticsResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 数据存储服务接口类
 * @since 2024/9/18 14:06
 **/
public interface DataStorageConnectionService {


    /**
     * 数据存储连接列表查询
     * 支持按数据源大类进行过滤
     *
     * @param request 前端请求，包含可选的数据源类别过滤参数
     * @return com.trs.ai.moye.data.connection.response.DataConnectionResponse
     * <AUTHOR>
     * @since 2024/9/18 14:53
     */
    List<DataConnectionCardResponse> getCardList(ConnectionListRequest request);


    /**
     * 名称校验
     *
     * @param name 前端请求
     * @return java.lang.Boolean
     * <AUTHOR>
     * @since 2024/9/18 15:11
     */
    Boolean checkName(String name);


    /**
     * 通过连接ID来获取使用的次数
     *
     * @param id 连接ID
     * @return java.lang.Integer
     * <AUTHOR>
     * @since 2024/9/18 15:30
     */
    Integer getConnectionUsedCount(Integer id);

    /**
     * 获取统计信息
     *
     * @return 统计信息
     */
    DataConnectionStatisticsResponse getStatistics();
}
