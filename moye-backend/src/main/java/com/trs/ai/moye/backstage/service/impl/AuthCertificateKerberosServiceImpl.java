package com.trs.ai.moye.backstage.service.impl;


import com.trs.ai.moye.backstage.config.KerberosConfig;
import com.trs.ai.moye.backstage.dao.AuthCertificateKerberosMapper;
import com.trs.ai.moye.backstage.entity.AuthCertificateKerberos;
import com.trs.ai.moye.backstage.request.AuthCertificateKerberosInfo;
import com.trs.ai.moye.backstage.service.AuthCertificateKerberosService;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.FileUtils;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2024/10/18
 */
@Slf4j
@Service
public class AuthCertificateKerberosServiceImpl implements AuthCertificateKerberosService {

    @Resource
    private KerberosConfig kerberosConfig;

    @Resource
    private AuthCertificateKerberosMapper authCertificateKerberosMapper;

    @Override
    public String uploadFile2TempDir(MultipartFile file) {
        Path path = resolveDirPath(null);
        return resolveAndStorageFile(file, path);
    }

    @Override
    public Integer saveKerberosFileFromTempDir(AuthCertificateKerberosInfo authCertificateKerberosTempInfo,
        Integer roleId) {
        if (authCertificateKerberosTempInfo.getTempKeytabPath()
            .equals(authCertificateKerberosTempInfo.getTempKrb5Path())) {
            throw new BizException("keytab临时文件路径和krb5临时文件路径不能相同!");
        }
        if (Objects.isNull(authCertificateKerberosTempInfo.getId())) {
            return processAddCertificate(authCertificateKerberosTempInfo, roleId);
        } else {
            return processUpdateCertificate(authCertificateKerberosTempInfo, roleId);
        }
    }

    private Integer processUpdateCertificate(AuthCertificateKerberosInfo authCertificateKerberosTempInfo,
        Integer roleId) {
        AuthCertificateKerberos entityInfo = authCertificateKerberosMapper.selectById(
            authCertificateKerberosTempInfo.getId());
        if (Objects.isNull(entityInfo)) {
            throw new BizException("kerberos信息不存在");
        }
        String sourceKrb5Path = entityInfo.getKrb5Path();
        if (!sourceKrb5Path.equals(authCertificateKerberosTempInfo.getTempKrb5Path())) {
            entityInfo.setKrb5Path(moveAndDeleteTempDir(authCertificateKerberosTempInfo.getTempKrb5Path(), roleId));
            deleteOldDir(sourceKrb5Path);
        }
        String sourceKeytabPath = entityInfo.getKeytabPath();
        if (!sourceKeytabPath.equals(authCertificateKerberosTempInfo.getTempKeytabPath())) {
            String newPath = moveAndDeleteTempDir(authCertificateKerberosTempInfo.getTempKeytabPath(), roleId);
            entityInfo.setKeytabPath(newPath);
            deleteOldDir(sourceKeytabPath);
        }
        authCertificateKerberosMapper.updateById(entityInfo);
        return entityInfo.getId();
    }

    private static void deleteOldDir(String sourceKeytabPath) {
        try {
            FileUtils.deleteDirectory(FileUtils.getFileDirectory(sourceKeytabPath));
        } catch (IOException e) {
            throw new BizException(e);
        }
    }

    private String moveAndDeleteTempDir(String oldPath, Integer roleId) {
        String dirPath = FileUtils.getFileDirectory(oldPath);
        String newPath = moveFileFromTempDir(oldPath, roleId);
        deleteOldFile(dirPath);
        return newPath;
    }

    private static void deleteOldFile(String sourceKeytabPath) {
        // 删除旧文件
        try {
            FileUtils.deleteDirectory(sourceKeytabPath);
        } catch (IOException e) {
            log.error("删除文件失败{}", e.getMessage());
            throw new BizException(e);
        }
    }

    private Integer processAddCertificate(AuthCertificateKerberosInfo authCertificateKerberosTempInfo, Integer roleId) {
        String krb5Path = moveAndDeleteTempDir(authCertificateKerberosTempInfo.getTempKrb5Path(), roleId);
        String keytabPath = moveAndDeleteTempDir(authCertificateKerberosTempInfo.getTempKeytabPath(), roleId);
        AuthCertificateKerberos authCertificateKerberos = buildAuthCertificateKerberos(
            authCertificateKerberosTempInfo,
            krb5Path, keytabPath);
        authCertificateKerberosMapper.insert(authCertificateKerberos);
        return authCertificateKerberos.getId();
    }

    private String moveFileFromTempDir(String sourceFilePath, Integer roleId) {
        Path dirPath = resolveDirPath(roleId);
        try {
            return FileUtils.moveFileToDirectory(
                Paths.get(sourceFilePath), dirPath);
        } catch (IOException e) {
            try {
                FileUtils.deleteDirectory(dirPath.toString());
            } catch (IOException ex) {
                throw new BizException(String.format("删除文件失败:%s", e.getMessage()));
            }
            log.error("移动文件失败", e);
            throw new BizException(String.format("移动文件失败:%s", e.getMessage()));
        }
    }


    private static AuthCertificateKerberos buildAuthCertificateKerberos(
        AuthCertificateKerberosInfo authCertificateKerberosTempInfo, String krb5Path, String keytabPath) {
        AuthCertificateKerberos authCertificateKerberos = new AuthCertificateKerberos();
        authCertificateKerberos.setId(authCertificateKerberosTempInfo.getId());
        authCertificateKerberos.setPrincipal(authCertificateKerberosTempInfo.getPrincipal());
        authCertificateKerberos.setKrb5Path(krb5Path);
        authCertificateKerberos.setKeytabPath(keytabPath);
        return authCertificateKerberos;
    }

    @Override
    public void deleteById(Integer id) {
        AuthCertificateKerberos authCertificateKerberos = authCertificateKerberosMapper.selectById(id);
        if (Objects.isNull(authCertificateKerberos)) {
            throw new BizException("kerberos信息不存在");
        }
        authCertificateKerberosMapper.deleteById(id);
        try {
            FileUtils.deleteFile(authCertificateKerberos.getKrb5Path());
            FileUtils.deleteFile(authCertificateKerberos.getKeytabPath());
        } catch (IOException e) {
            log.error("删除文件失败", e);
            throw new BizException(e);
        }
    }

    private Path resolveDirPath(Integer roleId) {
        String storagePath = kerberosConfig.getStoragePath();
        Path dirPath;
        long currentTimeMillis = System.currentTimeMillis();
        if (Objects.isNull(roleId)) {
            dirPath = Paths.get(storagePath, String.valueOf(currentTimeMillis));
        } else {
            dirPath = Paths.get(storagePath, String.valueOf(roleId), String.valueOf(currentTimeMillis));
        }

        FileUtils.createDir(dirPath);
        return dirPath;
    }

    private String resolveAndStorageFile(MultipartFile file, Path dirpath) {
        if (file.isEmpty()) {
            throw new BizException("文件为空");
        }

        String originalFilename = file.getOriginalFilename();
        if (Objects.isNull(originalFilename)) {
            throw new BizException("文件名为空");
        }

        Path filePath = dirpath.resolve(originalFilename);
        try {
            FileUtils.saveFile(filePath, file.getInputStream());
        } catch (IOException e) {
            log.error("创建文件夹失败", e);
            throw new BizException("创建文件夹失败");
        }
        return filePath.toString();
    }
}




