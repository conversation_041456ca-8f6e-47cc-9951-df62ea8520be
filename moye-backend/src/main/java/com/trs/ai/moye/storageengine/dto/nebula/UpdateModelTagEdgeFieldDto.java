package com.trs.ai.moye.storageengine.dto.nebula;

import com.trs.moye.base.data.standard.entity.UpdateTagEdgeField;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-01-20 10:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class UpdateModelTagEdgeFieldDto extends BaseModelTagEdgeFieldDto {

    @Valid
    @NotEmpty(message = "字段列表不能为空")
    private List<UpdateTagEdgeField> fields;

    public UpdateModelTagEdgeFieldDto(String enName, List<UpdateTagEdgeField> fields) {
        setEnName(enName);
        this.fields = fields;
    }

}
