package com.trs.ai.moye.backstage.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.moye.base.common.enums.ResultType;
import com.trs.moye.base.common.log.api.ApiLog;
import com.trs.moye.base.common.request.SearchParams;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2025-03-04 14:47
 */
@DS("clickhouse")
@Mapper
public interface ApiLogMapper extends BaseMapper<ApiLog> {

    /**
     * 分页查询日志
     *
     * @param externalLogId  会话ID
     * @param responseStatus 响应状态
     * @param connectionType 连接类型
     * @param startTime      开始时间
     * @param endTime        开始时间
     * @param searchParams   搜索参数
     * @param page           分页参数
     * @return 分页结果
     */
    Page<ApiLog> pageList(@Param("externalLogId") String externalLogId,
        @Param("responseStatus") ResultType responseStatus,
        @Param("connectionType") ConnectionType connectionType,
        @Param("startTime") String startTime,
        @Param("endTime") String endTime,
        @Param("searchParams") SearchParams searchParams,
        Page<ApiLog> page);

    /**
     * 根据主键查询日志
     *
     * @param logId 主键
     * @return ApiLog
     */
    ApiLog selectByLogId(@Param("logId") Long logId);
}
