package com.trs.ai.moye.storageengine.request;

import com.trs.moye.base.data.storage.nebula.ChangeFieldNameDto;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NebulaDropTagEdgeRequest
 *
 * <AUTHOR>
 * @since 2025/1/20 18:38
 */
@Data
@Valid
@NoArgsConstructor
@AllArgsConstructor
public class NebulaUpdateTagEdgeRequest {

    /**
     * 连接id
     */
    @NotEmpty
    private List<Integer> connectionIds;

    /**
     * tag/edge
     */
    @NotEmpty
    private List<ChangeFieldNameDto> fields;

}
