package com.trs.ai.moye.knowledgebase.request.external;

import com.trs.moye.base.common.request.SearchParams;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-11-19 17:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class ExternalMultiSearchRequest extends ExternalPageListRequest {

    /**
     * 关键字搜索参数,多组条件
     */
    private List<SearchParams> multiSearchable;
}
