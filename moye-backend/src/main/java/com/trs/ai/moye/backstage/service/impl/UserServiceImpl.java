package com.trs.ai.moye.backstage.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.backstage.dao.DepartmentMapper;
import com.trs.ai.moye.backstage.dao.RoleMapper;
import com.trs.ai.moye.backstage.dao.UserMapper;
import com.trs.ai.moye.backstage.entity.Department;
import com.trs.ai.moye.backstage.entity.Role;
import com.trs.ai.moye.backstage.entity.User;
import com.trs.ai.moye.backstage.entity.UserCheckRepeatRequest;
import com.trs.ai.moye.backstage.request.UserListRequest;
import com.trs.ai.moye.backstage.request.UserRequest;
import com.trs.ai.moye.backstage.response.UserDetailResponse;
import com.trs.ai.moye.backstage.response.UserInfoResponse;
import com.trs.ai.moye.backstage.response.UserInfoResponse.PasswordInfo;
import com.trs.ai.moye.backstage.service.UserService;
import com.trs.moye.base.common.response.IdNameResponse;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.ai.moye.permission.properties.SecurityProperties;
import com.trs.ai.moye.permission.service.AuthHelper;
import com.trs.ai.moye.permission.service.BcryptService;
import com.trs.ai.moye.permission.service.TokenStore;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.AesUtils;
import com.trs.moye.base.common.utils.AssertUtils;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {

    public static final String MSG_USER_NOT_EXIST = "主键为【%s】的用户不存在";
    @Resource
    private UserMapper userMapper;
    @Resource
    private DepartmentMapper departmentMapper;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private SecurityProperties securityProperties;

    @Resource
    private TokenStore tokenStore;

    @Resource
    private BcryptService bcryptService;


    /**
     * 获取当前登录用户的所有信息 ，权限名字等
     *
     * @param user {@link User}
     * @return com.trs.ai.my.entity.response.UserInfoResponse
     * <AUTHOR>
     * @since 2020/9/29 18:06
     */
    @Override
    public UserInfoResponse userInfo(User user) {
        if (Objects.isNull(user)) {
            return new UserInfoResponse();
        }
        UserInfoResponse userInfoResponse = new UserInfoResponse();
        // 用户基本信息
        userInfoResponse.setId(user.getId());
        userInfoResponse.setName(user.getName());
        userInfoResponse.setType(user.getType());
        Department department = departmentMapper.selectById(user.getDepartmentId());
        userInfoResponse.setDepartmentName(department.getName());
        processRoleInfos(user, userInfoResponse);

        // 是否提示修改密码
        Duration between = Duration.between(user.getPwdUpdateTime(), LocalDateTime.now());
        PasswordInfo passwordInfo =
            new PasswordInfo(between.toDays() > securityProperties.getWarnUpdatePwdDays(), between.toDays());
        userInfoResponse.setPasswordInfo(passwordInfo);
        return userInfoResponse;
    }

    private void processRoleInfos(User user, UserInfoResponse userInfoResponse) {
        List<Integer> roleIds = user.getRoleIds();
        if (!roleIds.isEmpty()) {
            List<Role> roles = roleMapper.selectByIds(roleIds);
            if (!roles.isEmpty()) {
                List<String> roleNames = roles.stream().map(Role::getName).toList();
                // 将role的operations合并为一个list
                List<String> mergedOperations = roles.stream()
                    .map(Role::getOperations)
                    .flatMap(List::stream)
                    .distinct()
                    .toList();
                userInfoResponse.setOperations(mergedOperations);
                userInfoResponse.setRoleNames(roleNames);
            }
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public int addUser(UserRequest request) {
        UserCheckRepeatRequest repeatRequest = new UserCheckRepeatRequest(request);
        User dbData = userMapper.getRepeatUser(repeatRequest);
        if (dbData != null) {
            checkRepeat(repeatRequest, dbData);
        }
        User user = new User(request);
        user.setPassword(bcryptService.getBcryptPassword(AesUtils.decrypt(request.getPassword())));
        // 默认账号状态为启用（正常）
        user.setEnable(true);
        user.setLoginFailedNum(0);
        user.setPwdUpdateTime(LocalDateTime.now());
        userMapper.insert(user);
        return user.getId();
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    @CacheEvict(value = "user:name", key = "'userId-' + #id")
    public void updateUser(Integer id, UserRequest request) {
        User oldUser = userMapper.selectById(id);
        AssertUtils.notEmpty(oldUser, MSG_USER_NOT_EXIST, id);
        UserCheckRepeatRequest repeatRequest = new UserCheckRepeatRequest(request);
        User dbData = userMapper.getRepeatUser(repeatRequest);
        if (dbData != null && !dbData.getId().equals(id)) {
            checkRepeat(repeatRequest, dbData);
        }
        User user = new User(request);
        user.setId(id);
        user.setEnable(oldUser.isEnable());
        user.setLoginFailedNum(oldUser.getLoginFailedNum());
        user.setPwdUpdateTime(oldUser.getPwdUpdateTime());
        userMapper.updateById(user);
        //修改用户后登录失效，需要重新登录
        clearToken(user.getAccount());
        deleteUserCache(user.getAccount());
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    @CacheEvict(value = "user:name", key = "'userId-' + #id")
    public void deleteUser(Integer id) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new BizException("用户%d不存在！", id);
        }
        userMapper.deleteById(id);
        clearToken(user.getAccount());
        deleteUserCache(user.getAccount());
    }

    @Override
    public UserDetailResponse userDetail(Integer id) {
        User user = userMapper.selectById(id);
        AssertUtils.notEmpty(user, "主键为【%s】的账号不存在", id);
        return buildUserDetailResponse(user);
    }

    private UserDetailResponse buildUserDetailResponse(User user) {
        UserDetailResponse response = new UserDetailResponse(user);
        Department department = departmentMapper.selectById(user.getDepartmentId());
        AssertUtils.notEmpty(department, "用户【%s】的部门主键是【%s】，但是没有主键为【%s】部门"
            , user.getName()
            , user.getDepartmentId()
            , user.getDepartmentId());
        response.setDepartment(new IdNameResponse(department.getId(), department.getName()));
        response.setRoles(
            roleMapper.selectByIdCollection(user.getRoleIds()).stream()
                .map(role -> new IdNameResponse(role.getId(), role.getName()))
                .toList());
        return response;
    }

    @Override
    public PageResponse<UserDetailResponse> userPageList(UserListRequest request) {
        Page<User> page = userMapper.userPageList(request.getDepartmentId(), request.getSearchParams(),
            request.getSortParams(), request.getPageParams().toPage());
        return PageResponse.of(page).toNewPageResult(this::buildUserDetailResponse);
    }

    @Override
    public boolean checkUserRepeat(Integer id, UserCheckRepeatRequest request) {
        User user = userMapper.getRepeatUser(request);
        return user != null && !user.getId().equals(id);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void enableUser(Integer id) {
        User user = userMapper.selectById(id);
        AssertUtils.notEmpty(user, MSG_USER_NOT_EXIST, id);
        if (user.isEnable()) {
            throw new BizException("【%s】用户已经是启用状态，无需再次启用", user.getAccount());
        }
        user.setLoginFailedNum(0);
        user.setEnable(true);
        userMapper.updateById(user);
        deleteUserCache(user.getAccount());
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void disableUser(Integer id) {
        User user = userMapper.selectById(id);
        AssertUtils.notEmpty(user, MSG_USER_NOT_EXIST, id);
        if (!user.isEnable()) {
            throw new BizException("【%s】用户已经是禁用状态，无需再次禁用", user.getAccount());
        }
        User currentUser = AuthHelper.getCurrentUser();
        if (currentUser.getId().equals(id)) {
            throw new BizException(String.format("禁止禁用自己的账户: %s", currentUser.getAccount()));
        }
        user.setEnable(false);
        userMapper.updateById(user);
        clearToken(user.getAccount());
        deleteUserCache(user.getAccount());
    }

    private void clearToken(String account) {
        tokenStore.removeAllTokenByUser(account);
    }

    @Override
    @CacheEvict(value = "user:info", key = "#account")
    public void deleteUserCache(String account) {
        log.info("delete user cache. account:{}", account);
    }

    @Override
    public void resetPassword(Integer id, String password) {
        User user = userMapper.selectById(id);
        AssertUtils.notEmpty(user, MSG_USER_NOT_EXIST, id);
        // 修改密码和密码更新时间
        user.setPassword(bcryptService.getBcryptPassword(AesUtils.decrypt(password)));
        user.setPwdUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);
        clearToken(user.getAccount());
        deleteUserCache(user.getAccount());
    }

    private void checkRepeat(UserCheckRepeatRequest repeatRequest, User dbData) {
        String account = repeatRequest.getAccount();
        if (ObjectUtils.isNotEmpty(account) && account.equals(dbData.getAccount())) {
            throw new BizException("【%s】账号已存在，对应【%s】用户", account, dbData.getName());
        }
        String telephone = repeatRequest.getTelephone();
        if (ObjectUtils.isNotEmpty(telephone) && telephone.equals(dbData.getTelephone())) {
            throw new BizException("【%s】手机号已存在，对应【%s】用户", telephone, dbData.getName());
        }
        String email = repeatRequest.getEmail();
        if (ObjectUtils.isNotEmpty(email) && email.equals(dbData.getEmail())) {
            throw new BizException("【%s】邮箱已存在，对应【%s】用户", email, dbData.getName());
        }
    }

    @Override
    @Cacheable(value = "user:info", key = "#account", condition = "#account != null")
    public User getUserByAccountCacheable(String account) {
        return userMapper.findByAccount(account);
    }

    @Override
    @CachePut(value = "user:info", key = "#account")
    public User getUserByAccountUpdateCache(String account) {
        return userMapper.findByAccount(account);
    }
}
