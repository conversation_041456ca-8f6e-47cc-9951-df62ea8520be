package com.trs.ai.moye.permission.config;

import com.trs.ai.moye.backstage.dao.UserMapper;
import com.trs.ai.moye.backstage.entity.User;
import com.trs.ai.moye.common.exception.AuthenticationException;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * 初始化Authentication相关bean
 *
 * <AUTHOR>
 */
@Configuration
public class AuthenticationConfig {

    private final UserMapper userMapper;

    public AuthenticationConfig(UserMapper userMapper) {
        this.userMapper = userMapper;
    }

    /**
     * UserDetailsService
     *
     * @return {@link UserDetailsService}
     */
    @Bean
    UserDetailsService userDetailsService() {
        return username -> {
            User user = userMapper.findByAccount(username);
            if (user != null) {
                return user.createUserDetails();
            } else {
                throw new AuthenticationException("user not found!");
            }
        };
    }

    /**
     * BCryptPasswordEncoder
     *
     * @return {@link BCryptPasswordEncoder}
     */
    @Bean
    BCryptPasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * AuthenticationManager
     *
     * @param config {@link AuthenticationConfiguration}
     * @return {@link AuthenticationManager}
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    /**
     * AuthenticationProvider
     *
     * @return {@link AuthenticationProvider}
     */
    @Bean
    AuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService());
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }
}