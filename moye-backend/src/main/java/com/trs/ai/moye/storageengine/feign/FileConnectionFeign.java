package com.trs.ai.moye.storageengine.feign;

import com.trs.ai.moye.common.config.OpenFeignConfig;
import com.trs.ai.moye.data.connection.request.TestConnectionRequest;
import com.trs.ai.moye.storageengine.request.FileColumnRequest;
import com.trs.ai.moye.storageengine.request.FileTableRequest;
import com.trs.ai.moye.storageengine.response.ConnectionTestDetailResponse;
import com.trs.ai.moye.storageengine.response.DirectoryAndFileResponse;
import com.trs.moye.base.data.model.field.mapping.FieldMappingResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 存储引擎数据库连接feign
 */
@FeignClient(name = "moye-storage-engine", path = "/storage/connection/file", contextId = "file", configuration = OpenFeignConfig.class)
public interface FileConnectionFeign {


    /**
     * 测试连接
     *
     * @param request 数据库信息
     * @return 测试结果
     */
    @PostMapping("/test")
    boolean testConnection(@RequestBody TestConnectionRequest request);

    /**
     * 测试连接
     * 若失败, 返回失败原因
     *
     * @param request 数据库信息
     * @return 测试结果
     */
    @PostMapping("/test-with-detail")
    ConnectionTestDetailResponse testConnectionWithDetail(@RequestBody TestConnectionRequest request);

    /**
     * 获取全部表
     *
     * @param connectionId 连接id
     * @param request      请求参数
     * @return 表信息
     */
    @PostMapping("/{connectionId}/table-list")
    DirectoryAndFileResponse getTableList(@PathVariable("connectionId") Integer connectionId,
        @RequestBody FileTableRequest request);

    /**
     * 获取表字段
     *
     * @param connectionId 连接id
     * @param request      请求参数
     * @return 表信息
     */
    @PostMapping("/{connectionId}/table-fields")
    FieldMappingResponse getTableFields(@PathVariable("connectionId") Integer connectionId,
        @RequestBody FileColumnRequest request);
}
