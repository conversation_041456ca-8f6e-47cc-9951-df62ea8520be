package com.trs.ai.moye.backstage.controller;

import com.trs.ai.moye.backstage.dao.DepartmentMapper;
import com.trs.ai.moye.backstage.entity.Department;
import com.trs.ai.moye.backstage.request.DepartmentAddRequest;
import com.trs.ai.moye.backstage.request.DepartmentUpdateRequest;
import com.trs.ai.moye.backstage.response.DepartmentTreeResponse;
import com.trs.ai.moye.backstage.service.DepartmentService;
import com.trs.moye.base.common.exception.BizException;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 部门管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/25 10:03
 **/
@RestController
@RequestMapping("/department")
@Validated
public class DepartmentController {

    @Resource
    private DepartmentMapper departmentMapper;

    @Resource
    private DepartmentService departmentService;


    /**
     * 查询部门树 <a href="http://192.168.210.40:3001/project/5419/interface/api/163870">...</a>
     *
     * @return {@link DepartmentTreeResponse }
     * <AUTHOR>
     * @since 2024/9/25 10:07
     */
    @GetMapping("/tree")
    public List<DepartmentTreeResponse> getAllDepartment() {
        return departmentMapper.selectDepartmentTree().stream().map(DepartmentTreeResponse::new).toList();
    }


    /**
     * 名称校验 <a href="http://192.168.210.40:3001/project/5419/interface/api/163880">...</a>
     *
     * @param name 部门名称
     * @return {@link Boolean} true 重复 false 不重复
     * <AUTHOR>
     * @since 2024/9/25 10:09
     */
    @GetMapping("/check-name")
    public Boolean checkName(@RequestParam String name) {
        return departmentMapper.existsByName(name);
    }


    /**
     * 部门添加 <a href="http://192.168.210.40:3001/project/5419/interface/api/163890">...</a>
     *
     * @param request 部门信息
     * @return {@link Boolean } true 添加成功 false 添加失败
     * <AUTHOR>
     * @since 2024/9/25 10:10
     */
    @PostMapping()
    public Boolean addDepartment(@Valid @RequestBody DepartmentAddRequest request) {
        return departmentService.add(request) > 0;
    }


    /**
     * 部门编辑 <a href="http://192.168.210.40:3001/project/5419/interface/api/163895">...</a>
     *
     * @param request 前端请求参数
     * @return {@link Boolean } true 编辑成功 false 编辑失败
     * <AUTHOR>
     * @since 2024/9/25 10:12
     */
    @PutMapping()
    public Boolean updateDepartment(@Valid @RequestBody DepartmentUpdateRequest request) {
        if (!departmentMapper.findByNameExceptId(request.getName(), request.getId()).isEmpty()) {
            throw new BizException("部门名称重复！");
        } else {
            return departmentMapper.updateById(Department.formUpdate(request)) > 0;
        }
    }


    /**
     * s删除部门 <a href="http://192.168.210.40:3001/project/5419/interface/api/163905">...</a>
     *
     * @param id 部门id
     * @return {@link Boolean } true 删除成功 false 删除失败
     * <AUTHOR>
     * @since 2024/9/25 10:13
     */
    @DeleteMapping("/{id}")
    public Boolean deleteDepartment(@PathVariable Integer id) {
        return departmentService.delete(id);
    }


    /**
     * 判断是否允许删除  <a href="http://192.168.210.40:3001/project/5419/interface/api/164560">...</a>
     *
     * @param id 部门ID
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/9/25 15:43
     */

    @GetMapping("/delete-able/{id}")
    public Boolean deleteAble(@PathVariable Integer id) {
        return departmentService.deleteAble(id);
    }

}
