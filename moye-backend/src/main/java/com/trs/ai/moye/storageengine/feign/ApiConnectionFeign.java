package com.trs.ai.moye.storageengine.feign;

import com.trs.ai.moye.common.config.OpenFeignConfig;
import com.trs.ai.moye.data.connection.request.TestConnectionRequest;
import com.trs.ai.moye.storageengine.response.ConnectionTestDetailResponse;
import com.trs.moye.base.data.model.field.mapping.ColumnResponse;
import com.trs.moye.base.data.source.setting.http.HttpDataSourceSettings;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 存储引擎数据库连接feign
 */
@FeignClient(name = "moye-storage-engine", path = "/storage/connection/api", contextId = "api", configuration = OpenFeignConfig.class)
public interface ApiConnectionFeign {

    /**
     * 测试连接
     *
     * @param request 数据库信息
     * @return 测试结果
     */
    @PostMapping("/test")
    boolean testConnection(@RequestBody TestConnectionRequest request);

    /**
     * 测试连接
     * 若失败, 返回失败原因
     *
     * @param request 连接信息
     * @return 测试结果
     */
    @PostMapping("/test-with-detail")
    ConnectionTestDetailResponse testConnectionWithDetail(@RequestBody TestConnectionRequest request);


    /**
     * 通过提交表单的参数获取http请求接口字段
     *
     * @param httpInfo http连接信息
     * @return 字段信息
     */
    @PostMapping("/field/tree")
    Map<String, Object> httpField(@RequestBody HttpDataSourceSettings httpInfo);

    /**
     * 获取该连接接口的字段
     *
     * @param connectionId 数据源连接id
     * @param dataModelId  数据建模id
     * @return 该接口下得字段信息
     */
    @GetMapping("/{connectionId}/{dataModelId}/api-fields")
    List<ColumnResponse> getTableFields(@PathVariable("connectionId") Integer connectionId,
        @PathVariable("dataModelId") Integer dataModelId);
}
