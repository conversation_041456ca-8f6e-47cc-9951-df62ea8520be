package com.trs.ai.moye.backstage.request;

import com.trs.moye.base.common.group.Insert;
import com.trs.moye.base.common.group.Update;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用于角色的新增和更新操作
 *
 * <AUTHOR>
 * @since 2024/10/18
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoleRequest {

    @Null(message = "新增时id为空!", groups = {Insert.class})
    @NotNull(message = "id不能为空!", groups = {Update.class})
    private Integer id;

    /**
     * 角色名称
     */
    @NotNull(message = "角色名称不能为空!")
    @Size(min = 1, max = 50, message = "角色名称长度在1-50之间!")
    private String name;

    /**
     * 用户的权限列表(给前端显示用)
     */
    private List<String> operations;

    /**
     * 凭证字段
     */
    private List<AuthCertificateKerberosInfo> credentials;

}
