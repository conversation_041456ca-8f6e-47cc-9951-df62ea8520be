package com.trs.ai.moye.monitor.service;

import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.ai.moye.monitor.request.DataModelMonitorRequest;
import com.trs.ai.moye.monitor.request.MonitorEventPeriodRequest;
import com.trs.ai.moye.monitor.response.HomePageResponse;
import com.trs.ai.moye.monitor.response.MonitorEventPeriodResponse;
import com.trs.ai.moye.monitor.response.OdsMonitorConfigVersionResponse;
import com.trs.ai.moye.monitor.response.OdsMonitorStatisticsResponse;
import com.trs.moye.base.common.request.BaseRequestParams;
import com.trs.moye.base.monitor.entity.MonitorOdsCutoff;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-30 14:19
 */
public interface MonitorOdsCutoffService {

    /**
     * 首页查询
     *
     * @param request 来源分类（大类）
     * @param layer   层级
     * @return 最新断流列表
     */
    HomePageResponse dataModelMonitorHomePage(DataModelMonitorRequest request, ModelLayer layer);

    /**
     * 统计页面列表
     *
     * @param request 请求参数
     * @param layer   层级
     * @return 统计信息
     */
    PageResponse<OdsMonitorStatisticsResponse> odsMonitorStatisticsList(DataModelMonitorRequest request,
        ModelLayer layer);

    /**
     * 元数据监控详情列表
     *
     * @param dataModelId 元数据id
     * @param request     请求对象
     * @return 断流数据
     */
    PageResponse<MonitorOdsCutoff> odsMonitorDetailTable(Integer dataModelId, BaseRequestParams request);

    /**
     * 查询断流版本信息
     *
     * @param dataModelId 元数据id
     * @param request     请求对象
     * @return OdsMonitorConfigRecord
     */
    List<OdsMonitorConfigVersionResponse> odsMonitorDetailVersions(Integer dataModelId, BaseRequestParams request);

    /**
     * 数据模型监控详情——周期列表
     *
     * @param dataModelId 数据建模id
     * @param request     分页参数
     * @return 分页数据
     */
    PageResponse<MonitorEventPeriodResponse> odsMonitorDetailPeriods(Integer dataModelId, MonitorEventPeriodRequest request);
}
