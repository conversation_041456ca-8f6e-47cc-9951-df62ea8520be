package com.trs.ai.moye.backstage.response;

import com.trs.ai.moye.backstage.enums.UserType;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 用户信息,每个用户拥有几个角色
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/9/27 15:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserInfoResponse {

    /**
     * 用户ID
     */
    private int id;

    /**
     * 用户名
     */
    private String name;

    /**
     * 用户部门名称
     */
    private String departmentName;

    /**
     * 数据范围
     */
    private List<DataScopeResponse> dataScope;

    /**
     * 操作权限
     */
    private List<String> operations;


    /**
     * 密码信息
     */
    private PasswordInfo passwordInfo;

    /**
     * 用户类型  1：个人用户；2：企业用户
     */
    private UserType type;

    /**
     * 角色名称
     */
    private List<String> roleNames;

    /**
     * 密码信息
     */
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PasswordInfo {

        /**
         * 是否需要修改密码；更新：ture;不更新:false
         */
        private Boolean isUpdatePassword;
        /**
         * 没有修改的天数，小于1就是0
         */
        private Long day;
    }
}