package com.trs.ai.moye.backstage.response;

import com.trs.ai.moye.backstage.entity.User;
import com.trs.ai.moye.backstage.enums.UserType;
import com.trs.moye.base.common.response.IdNameResponse;
import java.util.List;
import lombok.Data;

/**
 * 用户响应对象
 *
 * <AUTHOR>
 * @since 2024-09-25 16:16
 */
@Data
public class UserDetailResponse {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 用户名
     */
    private String name;

    /**
     * 账号
     */
    private String account;

    /**
     * 启用状态
     */
    private boolean isEnable;

    /**
     * 用户类型
     */
    private UserType type;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 部门
     */
    private IdNameResponse department;

    /**
     * 角色
     */
    private List<IdNameResponse> roles;

    public UserDetailResponse(User user) {
        this.id = user.getId();
        this.name = user.getName();
        this.type = user.getType();
        this.account = user.getAccount();
        this.telephone = user.getTelephone();
        this.email = user.getEmail();
        this.isEnable = user.isEnable();
    }
}
