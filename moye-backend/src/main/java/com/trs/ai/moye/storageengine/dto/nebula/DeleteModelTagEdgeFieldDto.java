package com.trs.ai.moye.storageengine.dto.nebula;

import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-01-20 10:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class DeleteModelTagEdgeFieldDto extends BaseModelTagEdgeFieldDto {

    @Valid
    @NotEmpty(message = "字段列表不能为空")
    private List<String> fields;

    public DeleteModelTagEdgeFieldDto(String enName, List<String> fields) {
        setEnName(enName);
        this.fields = fields;
    }
}
