package com.trs.ai.moye.backstage.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.ai.moye.backstage.request.DepartmentAddRequest;
import com.trs.ai.moye.backstage.request.DepartmentUpdateRequest;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import java.util.Objects;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 部门
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/9/28 11:43
 */
@Data
@TableName(value = "departments")
public class Department extends AuditBaseEntity {


    /**
     * 父级树地址
     */
    @NotNull(message = "父级编号不能为空！")
    private Integer pid;

    /**
     * 部门名称
     */
    @NotBlank(message = "部门名称不能为空！")
    private String name;

    /**
     * 封装添加实体
     *
     * @param request 前端请求参数
     * @return {@link Department}
     * <AUTHOR>
     * @since 2024/9/25 10:43
     */
    public static Department fromAdd(DepartmentAddRequest request) {
        Department department = new Department();
        department.setName(request.getName());
        department.setPid(request.getPid());
        return department;

    }

    /**
     * 封装更新实体
     *
     * @param request 前端请求
     * @return {@link Department}
     * <AUTHOR>
     * @since 2024/9/25 14:28
     */
    public static Department formUpdate(DepartmentUpdateRequest request) {
        Department department = new Department();
        department.setId(request.getId());
        department.setName(request.getName());
        return department;
    }

    /**
     * 是否为根节点，根节点pid为0
     *
     * @return java.lang.Boolean
     * <AUTHOR>
     * @since 2020/9/28 14:36
     */
    private Boolean isRoot() {
        return pid == 0;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        Department that = (Department) obj;
        return Objects.equals(id, that.id);
    }
}
