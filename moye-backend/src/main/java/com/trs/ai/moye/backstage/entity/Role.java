package com.trs.ai.moye.backstage.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.ai.moye.backstage.enums.RoleType;
import com.trs.ai.moye.backstage.request.RoleRequest;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.ai.moye.common.typehandler.IntegerListTypeHandler;
import com.trs.ai.moye.common.typehandler.StringListTypeHandler;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 角色
 *
 * <AUTHOR>
 * @since 2020/6/3 14:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "role")
@EqualsAndHashCode(callSuper = true)
public class Role extends AuditBaseEntity implements Serializable {


    private String name;

    private RoleType type = RoleType.CUSTOM;

    /**
     * 用户的权限列表(给前端显示用)
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> operations = new ArrayList<>();

    /**
     * 凭证字段
     */
    @TableField(typeHandler = IntegerListTypeHandler.class)
    private List<Integer> credentials;


    /**
     * 将请求参数转换为实体
     *
     * @param request 请求参数
     * @return 实体
     */
    public static Role fromRequest(RoleRequest request) {
        Role role = new Role();
        role.setId(request.getId());
        role.setName(request.getName());
        role.setOperations(request.getOperations());
        return role;
    }

}
