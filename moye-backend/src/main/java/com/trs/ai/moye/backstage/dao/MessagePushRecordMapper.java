package com.trs.ai.moye.backstage.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.backstage.entity.MessagePushRecord;
import com.trs.ai.moye.data.model.request.MessagePushRecordRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 消息推送记录mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/11/4 16:31
 **/
@Mapper
@DS("clickhouse")
public interface MessagePushRecordMapper {

    /**
     * 详情列表
     *
     * @param request 请求对象
     * @param page    分页
     * @return 波动数据
     */
    Page<MessagePushRecord> getMessagePushRecordsByPage(@Param("request") MessagePushRecordRequest request, Page<MessagePushRecord> page);

}
