package com.trs.ai.moye.backstage.controller;

import com.trs.ai.moye.backstage.request.OperateLogPageListRequest;
import com.trs.ai.moye.backstage.service.OperateLogService;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.log.operate.OperateLog;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025-03-04 13:46
 */
@RestController
@RequestMapping("/operate-log")
@Validated
public class OperateLogController {

    @Resource
    private OperateLogService service;


    /**
     * 分页列表
     *
     * @param request 请求参数
     * @return 配置
     */
    @PostMapping("/page-list")
    public PageResponse<OperateLog> pageList(@Validated @RequestBody OperateLogPageListRequest request) {
        return service.pageList(request);
    }
}
