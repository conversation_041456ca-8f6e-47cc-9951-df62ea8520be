package com.trs.ai.moye.backstage.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.moye.base.common.log.api.ApiLog;
import com.trs.moye.base.common.log.operate.OperateLog;
import com.trs.moye.base.common.log.operate.OperateType;
import com.trs.moye.base.common.enums.ResultType;
import com.trs.moye.base.common.request.SearchParams;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2025-03-04 14:47
 */
@DS("clickhouse")
@Mapper
public interface OperateLogMapper extends BaseMapper<OperateLog> {

    /**
     * 分页查询日志
     *
     * @param operateType   操作类型
     * @param operateResult 操作结果
     * @param startTime     开始时间
     * @param endTime       开始时间
     * @param searchParams  搜索参数
     * @param page          分页参数
     * @return 分页结果
     */
    Page<OperateLog> pageList(@Param("operateType") OperateType operateType,
        @Param("operateResult") ResultType operateResult,
        @Param("startTime") String startTime,
        @Param("endTime") String endTime,
        @Param("searchParams") SearchParams searchParams,
        Page<ApiLog> page);
}
