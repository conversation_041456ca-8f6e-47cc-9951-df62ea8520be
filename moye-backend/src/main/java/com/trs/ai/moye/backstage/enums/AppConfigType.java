package com.trs.ai.moye.backstage.enums;

import com.trs.ai.moye.backstage.entity.ui.AppConfigParam;
import com.trs.ai.moye.backstage.entity.ui.HomePageAppConfigParam;
import com.trs.ai.moye.backstage.entity.ui.ModelLayerAppConfigParam;
import com.trs.ai.moye.backstage.entity.ui.SystemInfoAppConfigParam;
import com.trs.moye.base.common.annotaion.PolymorphismMapping;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-12-05 13:47
 */
@Getter
@AllArgsConstructor
public enum AppConfigType {

    SYSTEM_INFO("系统信息"),
    MODEL_LAYER("数据建模层级"),
    HOME_PAGE("首页配置");

    /**
     * 标签：备注用
     */
    private final String label;

    /**
     * 反序列化目标类
     *
     * @return ui配置信息
     */
    @PolymorphismMapping
    public Class<? extends AppConfigParam> deserializeTargetClass() {
        if (this == MODEL_LAYER) {
            return ModelLayerAppConfigParam.class;
        } else if (this == HOME_PAGE) {
            return HomePageAppConfigParam.class;
        } else if (this == SYSTEM_INFO) {
            return SystemInfoAppConfigParam.class;
        } else {
            throw new IllegalArgumentException("Unsupported AppConfigType: " + this);
        }
    }
}
