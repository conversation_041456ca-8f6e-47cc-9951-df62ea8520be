package com.trs.ai.moye.permission.service;


import com.trs.ai.moye.backstage.entity.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 当前用户服务类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/2/5 9:58 下午
 **/

@Slf4j
@Service
@RequiredArgsConstructor
public class CurrentUserService {


    /**
     * 获取用户ID
     *
     * @return 用户id
     **/
    public Integer getUserId() {
        return AuthHelper.getCurrentUserId();
    }

    /**
     * 获取用户
     *
     * @return 用户信息
     **/
    public User getUser() {
        return AuthHelper.getCurrentUser();
    }

}

