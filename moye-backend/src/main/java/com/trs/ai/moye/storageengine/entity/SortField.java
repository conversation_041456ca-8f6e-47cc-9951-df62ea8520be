package com.trs.ai.moye.storageengine.entity;

import com.trs.ai.moye.common.enums.SortOrder;
import com.trs.ai.moye.data.service.entity.DataServiceSortField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 排序字段
 *
 * <AUTHOR>

 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SortField {

    /**
     * 字段
     */
    private String field;
    /**
     * 排序
     */
    private SortOrder order;

    public SortField(DataServiceSortField dataServiceSortField) {
        this.field = dataServiceSortField.getEnName();
        this.order = dataServiceSortField.getOrder();
    }
}
