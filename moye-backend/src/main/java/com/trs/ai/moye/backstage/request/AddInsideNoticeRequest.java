package com.trs.ai.moye.backstage.request;

import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * 站内消息推送新增请求
 *
 * <AUTHOR>
 * @since 2024/10/30 17:30
 */

@Validated
@Data
public class AddInsideNoticeRequest {


    @NotBlank(message = "策略名称不能为空")
    private String name;
    /**
     * 接收角色id集合
     */
    private List<Integer> notifyRoleIds;
    /**
     * 接收用户id集合
     */
    private List<Integer> notifyUserIds;
    /**
     * 接收消息类型id集合
     */
    @NotEmpty
    private List<Integer> messageTypeIds;
    /**
     * 数据范围id集合
     */
    @NotEmpty
    private List<Integer> businessScopeIds;
    /**
     * 是否启用 0:禁用 1:启用
     */
    private boolean enable;

}