package com.trs.ai.moye.backstage.request;

import com.trs.ai.moye.backstage.enums.UserType;
import com.trs.moye.base.common.group.Insert;
import com.trs.moye.base.common.group.Update;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

/**
 * 用户新增或修改请求对象
 *
 * <AUTHOR>
 * @since 2024/9/25 14:00
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class UserRequest {

    @NotBlank(message = "名称不能为空！", groups = {Insert.class, Update.class})
    private String name;

    @Length(min = 2, max = 32, message = "账号请输入2~32位字符，仅可输入汉字、数字、字母、下划线", groups = Insert.class)
    @Pattern(
        regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9_]+$",
        message = "该账号包含了非法字符，请重新输入！", groups = Insert.class)
    @NotBlank(message = "账号不能为空！", groups = Insert.class)
    private String account;

    @Pattern(
        regexp = "^[A-Za-z0-9+/]+={0,2}$",
        message = "该密码包含非法字符，请重新输入！", groups = Insert.class)
    @NotBlank(message = "密码不能为空！", groups = Insert.class)
    private String password;

    @NotNull(message = "部门编号不能为空！", groups = {Insert.class, Update.class})
    private Integer departmentId;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户类型  1：个人用户；2：企业用户
     */
    private UserType type;

    /**
     * 角色
     */
    private List<Integer> roleIds;

}
