package com.trs.ai.moye.storageengine.response;

import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * storage-engine 查询接口的response
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StorageSearchResponse {

    /**
     * 查询结果
     */
    private List<Map<String, Object>> items;
    /**
     * 总数
     */
    private Long total;
}
