package com.trs.ai.moye.backstage.service.impl;

import com.trs.ai.moye.backstage.dao.AppConfigMapper;
import com.trs.ai.moye.backstage.entity.AppConfig;
import com.trs.ai.moye.backstage.entity.ui.AppConfigParam;
import com.trs.ai.moye.backstage.enums.AppConfigType;
import com.trs.ai.moye.backstage.service.AppConfigService;
import com.trs.moye.base.common.utils.AssertUtils;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024-12-05 14:41
 */
@Service
public class AppConfigServiceImpl implements AppConfigService {

    @Resource
    private AppConfigMapper appConfigMapper;

    @Override
    public AppConfigParam getAppConfigParam(AppConfigType type) {
        return getAppConfig(type).getConfig();
    }

    private AppConfig getAppConfig(AppConfigType type) {
        AppConfig appConfig = appConfigMapper.selectById(type);
        AssertUtils.notEmpty(appConfig, "类型为【%s】的应用配置不存在", type);
        return appConfig;
    }

    @Override
    @CacheEvict(
        value = "moye:model-layer-names",
        allEntries = true,
        condition = "#config.type.name() == 'MODEL_LAYER'"
    )
    public void updateAppConfig(AppConfigParam config) {
        AppConfig appConfig = getAppConfig(config.getType());
        appConfig.setConfig(config);
        appConfigMapper.updateById(appConfig);
    }

    @Override
    public List<AppConfigParam> getAllAppConfig() {
        return appConfigMapper.selectList(null).stream().map(AppConfig::getConfig).toList();
    }
}
