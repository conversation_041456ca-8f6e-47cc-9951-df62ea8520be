package com.trs.ai.moye.backstage.controller;

import com.trs.ai.moye.backstage.entity.UserCheckRepeatRequest;
import com.trs.ai.moye.backstage.request.UserListRequest;
import com.trs.ai.moye.backstage.request.UserRequest;
import com.trs.ai.moye.backstage.request.UserResetPasswordRequest;
import com.trs.ai.moye.backstage.response.UserDetailResponse;
import com.trs.ai.moye.backstage.response.UserInfoResponse;
import com.trs.ai.moye.backstage.service.UserService;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.ai.moye.permission.service.AuthHelper;
import com.trs.moye.base.common.annotaion.OperateLogSign;
import com.trs.moye.base.common.entity.ModuleEnum;
import com.trs.moye.base.common.group.Insert;
import com.trs.moye.base.common.group.Update;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户管理相关接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/9/29 17:09
 */
@RestController
@RequestMapping("/user")
public class UserController {

    @Resource
    private UserService userService;


    /**
     * 用户登录之后，返回其相应信息和导航栏数据
     *
     * @return UserInfoResponse
     * <AUTHOR>
     * @since 2020/6/19
     */
    @GetMapping("/info")
    public UserInfoResponse userInfo() {
        return userService.userInfo(AuthHelper.getCurrentUser());
    }

    /**
     * 添加用户 <a href="http://192.168.210.40:3001/project/5419/interface/api/163915">...</a>
     *
     * @param request 请求参数
     * @return {@link int} 用户id
     * <AUTHOR>
     * @since 2024/9/25 11:43
     */
    @OperateLogSign(module = ModuleEnum.USER_MANAGE, apiName = "添加用户")
    @PostMapping
    public int addUser(@RequestBody @Validated(Insert.class) UserRequest request) {
        return userService.addUser(request);
    }

    /***
     * 更新用户 <a href="http://192.168.210.40:3001/project/5419/interface/api/163955">...</a>
     *
     * @param    id 用户id
     * @param    request   请求参数
     * <AUTHOR>
     * @since 2024/9/25 15:51
     */
    @OperateLogSign(module = ModuleEnum.USER_MANAGE, apiName = "修改用户")
    @PutMapping("/{id}")
    public void updateUser(@PathVariable Integer id, @RequestBody @Validated(Update.class) UserRequest request) {
        userService.updateUser(id, request);
    }

    /**
     * 删除用户 <a href="http://192.168.210.40:3001/project/5419/interface/api/163960">...</a>
     *
     * @param id 用户主键
     */
    @OperateLogSign(module = ModuleEnum.USER_MANAGE, apiName = "删除用户")
    @DeleteMapping("/{id}")
    public void deleteUser(@PathVariable Integer id) {
        userService.deleteUser(id);
    }

    /**
     * 检查用户重复 <a href="http://192.168.210.40:3001/project/5419/interface/api/163920">...</a>
     *
     * @param id      用户id
     * @param request 请求参数
     * @return 重复返回true，否则返回false
     */
    @PostMapping("/check-repeat")
    public boolean checkUserRepeat(@RequestParam(required = false) Integer id,
        @RequestBody UserCheckRepeatRequest request) {
        return userService.checkUserRepeat(id, request);
    }

    /**
     * 用户详情 <a href="http://192.168.210.40:3001/project/5419/interface/api/163940">...</a>
     *
     * @param id 用户主键
     * @return 用户详情信息
     */
    @GetMapping("/{id}")
    public UserDetailResponse userDetail(@PathVariable Integer id) {
        return userService.userDetail(id);
    }

    /**
     * 启用用户 <a href="http://192.168.210.40:3001/project/5419/interface/api/163945">...</a>
     *
     * @param id 用户id
     */
    @OperateLogSign(module = ModuleEnum.USER_MANAGE, apiName = "启用用户")
    @PutMapping("/{id}/enable")
    public void enableUser(@PathVariable Integer id) {
        userService.enableUser(id);
    }

    /**
     * 禁用用户 <a href="http://192.168.210.40:3001/project/5419/interface/api/164365">...</a>
     *
     * @param id 用户is
     */
    @OperateLogSign(module = ModuleEnum.USER_MANAGE, apiName = "禁用用户")
    @PutMapping("/{id}/disable")
    public void disableUser(@PathVariable Integer id) {
        userService.disableUser(id);
    }

    /**
     * 用户列表 <a href="http://192.168.210.40:3001/project/5419/interface/api/163965">...</a>
     *
     * @param request 请求参数
     * @return 用户分页信息
     */
    @PostMapping("/page-list")
    public PageResponse<UserDetailResponse> userPageList(@RequestBody UserListRequest request) {
        return userService.userPageList(request);
    }

    /**
     * 重置密码 <a href="http://192.168.210.40:3001/project/5419/interface/api/164565">...</a>
     *
     * @param id      用户id
     * @param request 请求参数
     */
    @OperateLogSign(module = ModuleEnum.USER_MANAGE, apiName = "重置密码")
    @PutMapping("/{id}/reset-password")
    public void resetPassword(@PathVariable Integer id, @RequestBody UserResetPasswordRequest request) {
        userService.resetPassword(id, request.getPassword());
    }
}
