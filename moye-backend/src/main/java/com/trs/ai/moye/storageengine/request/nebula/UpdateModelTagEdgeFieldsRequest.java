package com.trs.ai.moye.storageengine.request.nebula;

import com.trs.ai.moye.storageengine.dto.nebula.UpdateModelTagEdgeFieldDto;
import com.trs.moye.base.data.standard.entity.UpdateTagEdgeField;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MultiValuedMap;

/**
 * <AUTHOR>
 * @since 2025-01-20 10:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class UpdateModelTagEdgeFieldsRequest extends BaseModelTagEdgeFieldsRequest {


    /**
     * 要添加的tag
     */
    private List<UpdateModelTagEdgeFieldDto> tag;

    /**
     * 要添加的edge
     */
    private List<UpdateModelTagEdgeFieldDto> edge;

    public UpdateModelTagEdgeFieldsRequest(Integer modelId
        , MultiValuedMap<String, UpdateTagEdgeField> updateTagFieldMap,
        MultiValuedMap<String, UpdateTagEdgeField> updateEdgeFieldMap) {
        setDataModelId(modelId);
        this.tag = buildModelTagEdgeFieldDtoList(updateTagFieldMap);
        this.edge = buildModelTagEdgeFieldDtoList(updateEdgeFieldMap);
    }

    private List<UpdateModelTagEdgeFieldDto> buildModelTagEdgeFieldDtoList(
        MultiValuedMap<String, UpdateTagEdgeField> updateTagFieldMap) {
        Set<String> keySet = updateTagFieldMap.keySet();
        List<UpdateModelTagEdgeFieldDto> dtoList = new ArrayList<>(keySet.size());
        for (String key : keySet) {
            dtoList.add(new UpdateModelTagEdgeFieldDto(key, new ArrayList<>(updateTagFieldMap.get(key))));
        }
        return dtoList;
    }
}
