package com.trs.ai.moye.backstage.controller;

import com.trs.ai.moye.backstage.entity.ui.AppConfigParam;
import com.trs.ai.moye.backstage.enums.AppConfigType;
import com.trs.ai.moye.backstage.service.AppConfigService;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024-12-05 14:50
 */
@RestController
@RequestMapping("/app-config")
@Validated
public class AppConfigController {

    @Resource
    private AppConfigService appConfigService;

    /**
     * 根据类型获取配置
     *
     * @param type 类型
     * @return 配置
     */
    @GetMapping("/by-type")
    public AppConfigParam getAppConfig(@NotNull(message = "配置类型不能为空") @RequestParam AppConfigType type) {
        return appConfigService.getAppConfigParam(type);
    }

    /**
     * 修改配置
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/166057>系统配置-修改</a>
     *
     * @param config 配置
     */
    @PutMapping
    public void updateAppConfig(@Validated @RequestBody AppConfigParam config) {
        appConfigService.updateAppConfig(config);
    }

    /**
     * 获取所有配置
     *
     * @return {@link List }<{@link AppConfigParam }>
     */
    @GetMapping
    public List<AppConfigParam> getAllAppConfig() {
        return appConfigService.getAllAppConfig();
    }
}
