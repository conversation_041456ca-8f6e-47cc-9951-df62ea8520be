package com.trs.ai.moye.backstage.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.ai.moye.backstage.enums.NoticeSendConfInsideCreateType;
import com.trs.ai.moye.backstage.enums.NoticeType;
import com.trs.ai.moye.backstage.request.AddInsideNoticeRequest;
import com.trs.ai.moye.backstage.request.UpdateInsideNoticeRequest;
import com.trs.moye.base.data.model.entity.BusinessCategory;
import com.trs.ai.moye.permission.service.AuthHelper;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import com.trs.moye.base.data.model.entity.DataModel;
import java.util.Collections;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 内部消息实体类
 *
 * <AUTHOR>
 * @since 2024/10/31 14:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "notice_send_conf_inside", autoResultMap = true)
public class NoticeSendConfInside extends AuditBaseEntity {

    /**
     * 名称
     */
    private String name;

    /**
     * 接收角色id集合
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private List<Integer> sendRoleIds;
    /**
     * 接收用户id集合
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private List<Integer> sendUserIds;
    /**
     * 接收消息类型id集合
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private List<Integer> messageTypeIds;
    /**
     * 数据范围id集合
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private List<Integer> businessIds;
    /**
     * 是否启用 0:禁用 1:启用
     */
    private Boolean state;


    /**
     * 数据建模ID
     */
    private Integer dataModelId;

    /**
     * 建模的创建方式，系统自动创建还是用户手动配置
     */
    private NoticeSendConfInsideCreateType createModel;


    /**
     * 转换为MessageSendConfInside对象、页面上手动添加
     *
     * @param addInsideNoticeRequest 前端请求
     * @return {@link NoticeSendConfInside}
     * <AUTHOR>
     * @since 2024/10/30 18:12
     */
    public static NoticeSendConfInside formHandAdd(AddInsideNoticeRequest addInsideNoticeRequest) {
        NoticeSendConfInside noticeSendConfInside = new NoticeSendConfInside();
        noticeSendConfInside.setName(addInsideNoticeRequest.getName());
        noticeSendConfInside.setSendRoleIds(addInsideNoticeRequest.getNotifyRoleIds());
        noticeSendConfInside.setSendUserIds(addInsideNoticeRequest.getNotifyUserIds());
        noticeSendConfInside.setMessageTypeIds(addInsideNoticeRequest.getMessageTypeIds());
        noticeSendConfInside.setBusinessIds(addInsideNoticeRequest.getBusinessScopeIds());
        noticeSendConfInside.setState(addInsideNoticeRequest.isEnable());
        noticeSendConfInside.setCreateModel(NoticeSendConfInsideCreateType.HAND);
        return noticeSendConfInside;
    }

    /**
     * 转换为MessageSendConfInside对象、系统添加添加
     *
     * @param dataModel        建模
     * @param businessCategory 层级目录
     * @return {@link NoticeSendConfInside}
     * <AUTHOR>
     * @since 2024/10/30 18:12
     */
    public static NoticeSendConfInside formSystemAdd(DataModel dataModel, BusinessCategory businessCategory) {
        NoticeSendConfInside noticeSendConfInside = new NoticeSendConfInside();
        String businessName = "";
        if (businessCategory != null) {
            businessName = businessCategory.getZhName();
        }
        String layer = dataModel.getLayer().getLabel();
        String name = businessName + "-" + layer + "(" + dataModel.getZhName() + ")";
        noticeSendConfInside.setName(name);
        noticeSendConfInside.setSendUserIds(Collections.singletonList(AuthHelper.getCurrentUserId()));
        noticeSendConfInside.setSendRoleIds(Collections.singletonList(1));
        noticeSendConfInside.setBusinessIds(Collections.emptyList());
        noticeSendConfInside.setMessageTypeIds(Collections.singletonList(NoticeType.TASK_EXCEPTION.getCode()));
        //默认开启
        noticeSendConfInside.setState(Boolean.TRUE);
        noticeSendConfInside.setDataModelId(dataModel.getId());
        noticeSendConfInside.setCreateModel(NoticeSendConfInsideCreateType.SYSTEM);
        return noticeSendConfInside;
    }


    /**
     * 更新对象转换
     *
     * @param request                 前端请求
     * @param oldNoticeSendConfInside 旧对象
     * @return {@link NoticeSendConfInside}
     * <AUTHOR>
     * @since 2024/10/31 10:58
     */
    public static NoticeSendConfInside formUpdate(UpdateInsideNoticeRequest request,
        NoticeSendConfInside oldNoticeSendConfInside) {
        NoticeSendConfInside noticeSendConfInside = new NoticeSendConfInside();
        noticeSendConfInside.setId(request.getId());
        noticeSendConfInside.setName(request.getName());
        noticeSendConfInside.setSendRoleIds(request.getNotifyRoleIds());
        noticeSendConfInside.setSendUserIds(request.getNotifyUserIds());
        noticeSendConfInside.setMessageTypeIds(request.getMessageTypeIds());
        noticeSendConfInside.setBusinessIds(request.getBusinessScopeIds());
        noticeSendConfInside.setState(request.isEnable());
        noticeSendConfInside.setCreateBy(oldNoticeSendConfInside.getCreateBy());
        noticeSendConfInside.setCreateTime(oldNoticeSendConfInside.getCreateTime());
        return noticeSendConfInside;
    }
}
