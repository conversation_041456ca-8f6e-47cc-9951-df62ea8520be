package com.trs.ai.moye.backstage.request;

import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 管理员重置用户密码时用于接收id和password
 *
 * <AUTHOR>
 * @since 2020/10/9 10:04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserResetPasswordRequest {

    @NotBlank(message = "password不能为空！")
    private String password;
}
