package com.trs.ai.moye.data.model.service;

import com.trs.ai.moye.common.entity.UsageInfoResponse;
import com.trs.ai.moye.common.response.TreeBaseResponse;
import com.trs.ai.moye.data.model.dto.ReverseModelDTO;
import com.trs.ai.moye.data.model.dto.StorageTable;
import com.trs.ai.moye.data.model.entity.MonitorVersionQuery;
import com.trs.ai.moye.data.model.entity.StorageTask;
import com.trs.ai.moye.data.model.request.AllMonitorConfigRequest;
import com.trs.ai.moye.data.model.request.CreateTableRequests;
import com.trs.ai.moye.data.model.request.DataModelUpdateRequest;
import com.trs.ai.moye.data.model.request.DataSourceModelRequest;
import com.trs.ai.moye.data.model.request.FieldExportRequest;
import com.trs.ai.moye.data.model.request.ImmediateExecuteRequest;
import com.trs.ai.moye.data.model.request.ReadErrorMessagesRequest;
import com.trs.ai.moye.data.model.request.ScheduleRecordRequest;
import com.trs.ai.moye.data.model.request.ods.OdsFieldRequest;
import com.trs.ai.moye.data.model.response.BatchDeleteResponse;
import com.trs.ai.moye.data.model.response.CreateTableResponse;
import com.trs.ai.moye.data.model.response.DataModelSourceDetailResponse;
import com.trs.ai.moye.data.model.response.DataModelStatusResponse;
import com.trs.ai.moye.data.model.response.DataSourceModelResponse;
import com.trs.ai.moye.data.model.response.FieldPageListResponse;
import com.trs.ai.moye.data.model.response.ModelBasicInfoResponse;
import com.trs.ai.moye.data.model.response.ModelDataStorageResponse;
import com.trs.ai.moye.data.model.response.ModelMonitorConfigResponse;
import com.trs.ai.moye.data.model.response.MonitorConfigVersionResponse;
import com.trs.ai.moye.storageengine.request.ConditionSearchParams;
import com.trs.ai.moye.storageengine.response.StorageSearchResponse;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.enums.CreateModeEnum;
import com.trs.moye.base.data.model.field.mapping.MoyeFieldResponse;
import com.trs.moye.base.data.storage.nebula.ChangeFieldNameDto;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.servlet.http.HttpServletResponse;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数据建模服务接口类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/26 14:16
 **/
public interface DataModelService {


    /**
     * 数据建模基本信息
     *
     * @param id 建模id
     * @return 建模基本信息
     */
    ModelBasicInfoResponse getModelBasicInfo(Integer id);

    /**
     * 修改建模中文名
     *
     * @param id      建模id
     * @param request 请求参数
     */
    void updateModelZhName(Integer id, DataModelUpdateRequest request);

    /**
     * 数据建模的存储点信息
     *
     * @param id 建模id
     * @return 存储点信息列表
     */
    List<ModelDataStorageResponse> getModelDataStorages(Integer id);

    /**
     * 获取数据建模数据源列表（不构建业务树）
     * 支持过滤掉所有存储点都是未建表状态的数据建模
     *
     * @param request 请求参数，包含可选的过滤未建表数据建模参数
     * @return {@link DataSourceModelResponse}
     * <AUTHOR>
     * @since 2024/10/11 17:28
     */
    List<DataSourceModelResponse> getModelDataSourceList(DataSourceModelRequest request);

    /**
     * 获取数据建模数据源树列表（构建业务树）
     * 支持过滤掉所有存储点都是未建表状态的数据建模
     *
     * @param request 请求参数，包含可选的过滤未建表数据建模参数
     * @return {@link TreeBaseResponse}
     * <AUTHOR>
     * @since 2024/10/11 17:28
     */
    List<TreeBaseResponse> getModelDataSourceTreeList(DataSourceModelRequest request);

    /**
     * 数据建模的监控配置信息
     *
     * @param id 建模id
     * @return 监控配置
     */
    ModelMonitorConfigResponse getModelMonitorConfigInfo(Integer id);


    /**
     * 修改监控配置
     *
     * @param id      建模id
     * @param request 请求参数
     */
    void updateMonitorConfigInfo(Integer id, AllMonitorConfigRequest request);

    /**
     * 监控配置版本分页列表
     *
     * @param id    建模id
     * @param query 查询参数
     * @return 监控配置版本分页列表
     */
    PageResponse<MonitorConfigVersionResponse> monitorTypeVersionPageList(Integer id, MonitorVersionQuery query);

    /**
     * 执行配置
     *
     * @param id 建模id
     * @return 执行配置信息
     */
    DataModelStatusResponse modelStatus(Integer id);

    /**
     * 数据建模字段分页列表
     *
     * @param id 数建模id
     * @return 字段分页信息
     */
    List<FieldPageListResponse> modelFieldPageList(Integer id);

    /**
     * 获取模型已建表
     *
     * @param id id
     * @return {@link Set }<{@link Integer }>
     * <AUTHOR>
     * @since 2025/05/28 11:33:55
     */
    Set<Integer> getModelCreateTableFieldIds(Integer id);

    /**
     * 创建数据存储
     *
     * @param dataModel       建模
     * @param storagePointIds 存储点id
     */
    void createDataStorages(DataModel dataModel, Collection<Integer> storagePointIds);

    /**
     * 创建监控配置
     *
     * @param dataModelId 建模id
     */
    void createDefaultMonitorConfig(Integer dataModelId);

    /**
     * 创建执行配置
     *
     * @param dataModelId    建模id
     * @param dataConnection 连接信息
     * @param modelFields    建模字段
     */
    void createDefaultExecuteConfig(Integer dataModelId, DataConnection dataConnection,
        List<OdsFieldRequest> modelFields);

    /**
     * 移动数据到业务分类
     *
     * @param ids        需要移动的模型ID
     * @param businessId 业务ID
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/9/29 15:50
     */
    boolean moveMetaDataToCategory(Set<Integer> ids, Integer businessId);


    /**
     * 批量删除选中的数据建模接口
     *
     * @param ids 数据建模ID
     * @return {@link BatchDeleteResponse}
     * <AUTHOR>
     * @since 2024/9/29 16:24
     */
    List<BatchDeleteResponse> deleteDataModel(List<Integer> ids);

    /**
     * 单一删除数据模型
     *
     * @param dataModel 数据模型
     * <AUTHOR>
     * @since 2025/01/22 10:56:34
     */
    void singleDeleteDataModel(DataModel dataModel);

    /**
     * 删除建模
     *
     * @param id 建模ID
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/10/10 10:47
     */
    boolean deleteModel(Integer id);


    /**
     * 获取Model使用情况
     *
     * @param id 建模ID
     * @return {@link String}
     * <AUTHOR>
     * @since 2024/10/10 14:27
     */
    UsageInfoResponse getModelUsageInfo(Integer id);

    /**
     * 删除执行配置
     *
     * @param id 建模ID
     */
    void deleteExecuteConfig(Integer id);


    /**
     * 删除帖源库
     *
     * @param id 贴源库id
     */
    void deleteOds(Integer id);

    /**
     * 获取帖源库需要同步的字段
     *
     * @param id 帖源库id
     * @return 需要同步的字段列表
     */
    List<MoyeFieldResponse> getOdsNeedSyncFields(Integer id);

    /**
     * 同步帖源库字段
     *
     * @param id 建模id
     */
    void syncOdsFields(Integer id);


    /**
     * 更新数据建模字段
     *
     * @param id                建模id
     * @param fieldResponseList 字段列表
     * @param dataModel         数据建模
     * @param createMode        创建模式
     * <AUTHOR>
     * @since 2024/11/21 17:11
     */
    void updateDataModelFields(Integer id, List<MoyeFieldResponse> fieldResponseList, DataModel dataModel,
        CreateModeEnum createMode);

    /**
     * 创建表
     *
     * @param dataModelId         数据模型id
     * @param createTableRequests 建表参数
     * @return {@link CreateTableResponse}
     */
    List<CreateTableResponse> createTable(Integer dataModelId, CreateTableRequests createTableRequests);

    /**
     * 立即执行接口实现类
     *
     * @param id      数据建模ID
     * @param request 前端请求
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/10/12 14:06
     */
    Boolean immediateExecute(ImmediateExecuteRequest request, Integer id);


    /**
     * 获取来源详情
     *
     * @param id 建模ID
     * @return {@link DataModelSourceDetailResponse}
     * <AUTHOR>
     * @since 2024/10/14 15:04
     */
    List<DataModelSourceDetailResponse> getDataModelSourceDetail(Integer id);

    /**
     * 获取数据预览分页列表
     *
     * @param dataModelId 数据建模ID
     * @param storageId   存储ID
     * @param request     请求参数
     * @return {@link StorageSearchResponse}
     */
    PageResponse<Map<String, Object>> getDataPreviewPageList(Integer dataModelId, Integer storageId,
        ConditionSearchParams request);


    /**
     * 获取错误失败数据统计
     *
     * @param dataModelId 数据建模ID
     * @return {@link Integer}
     * <AUTHOR>
     * @since 2024/10/21 16:50
     */
    Integer getErrorCount(Integer dataModelId);


    /**
     * 已读错误信息
     *
     * @param id      单条数据的主键ID
     * @param request 前端参数
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/10/21 17:29
     */
    Boolean readErrorMessages(String id, ReadErrorMessagesRequest request);

    /**
     * 逆向建模
     *
     * @param businessCategoryId 分类id
     * @param layer              分层
     * @param connectionId       连接id
     * @param connectionType     连接类型
     * @param reverseModelDTO    逆向建模dto
     * @return {@link Integer }
     * @throws BizException 业务异常类
     * <AUTHOR>
     * @since 2024/10/23 14:17:19
     */
    @Transactional(rollbackFor = Exception.class)
    DataModel reverseModeling(Integer businessCategoryId, ModelLayer layer, Integer connectionId,
        ConnectionType connectionType, ReverseModelDTO reverseModelDTO) throws BizException;

    /**
     * 根据数据建模ID逆向建模
     *
     * @param dataModelId 数据型id
     * @throws BizException 业务异常类
     * <AUTHOR>
     * @since 2024/11/06 10:21:25
     */
    @Transactional(rollbackFor = Exception.class)
    void reverseModelingByModelId(Integer dataModelId) throws BizException;

    /**
     * 导出字段
     *
     * @param id       建模id
     * @param request  请求参数
     * @param response http响应
     */
    void exportFields(Integer id, FieldExportRequest request, HttpServletResponse response);

    /**
     * 根据已有表创建存储
     *
     * @param dataModel    数据建模
     * @param tableName    表名
     * @param connectionId 连接id
     * @return {@link Integer }
     */
    Integer createExistDataStorage(DataModel dataModel, String tableName, Integer connectionId);

    /**
     * 根据已有表创建存储
     *
     * @param dataModel  数据建模
     * @param connection 数据连接
     * @param table      表信息
     * @return 存储点id
     */
    Integer createExistDataStorage(DataModel dataModel, DataConnection connection, StorageTable table);

    /**
     * 添加数据建模字段
     *
     * @param dataModelFields 数据建模字段列表
     * @param dataModel       数据建模
     */
    void addDataModelFields(List<DataModelField> dataModelFields, DataModel dataModel);

    /**
     * 批量指定数据建模删除数据建模字段
     *
     * @param fieldsList   待删除的字段列表, 根据{@link DataModelField#getEnName()}, {@link DataModelField#getType()}删除
     * @param dataModelIds 数据建模id列表
     */
    void deleteDataModelFields(List<DataModelField> fieldsList, List<Integer> dataModelIds);

    /**
     * 修改数据建模字段名称
     *
     * @param fields     待修改的字段列表, 根据{@link ChangeFieldNameDto#getNewName()}, {@link ChangeFieldNameDto#getType()}修改
     * @param dataModels 数据建模列表
     */
    void changeDataModelsFieldsName(List<ChangeFieldNameDto> fields, List<DataModel> dataModels);

    /**
     * 分页查询贴源库调度记录
     *
     * @param dataModelId   数据建模id
     * @param requestParams 请求参数
     * @return {@link StorageTask}
     */
    PageResponse<StorageTask> getScheduleRecordPageList(Integer dataModelId, ScheduleRecordRequest requestParams);


}
