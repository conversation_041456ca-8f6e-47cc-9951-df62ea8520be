package com.trs.ai.moye.storageengine.feign;

import com.trs.ai.moye.common.config.OpenFeignConfig;
import com.trs.ai.moye.data.connection.request.TestConnectionRequest;
import com.trs.ai.moye.data.model.response.StorageEngineResponse;
import com.trs.ai.moye.out.request.NebulaStorageRequest;
import com.trs.ai.moye.storageengine.request.CreateTableRequest;
import com.trs.ai.moye.storageengine.response.ConnectionTestDetailResponse;
import com.trs.ai.moye.storageengine.response.DatabaseMetadataResponse;
import com.trs.ai.moye.storageengine.response.alltable.TableResponse;
import com.trs.moye.base.data.model.field.mapping.FieldMappingResponse;
import com.trs.moye.base.mcp.TableInfo;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 存储引擎数据库连接feign
 */
@FeignClient(name = "moye-storage-engine", path = "/storage/connection/db", contextId = "database", configuration = OpenFeignConfig.class)
public interface DatabaseConnectionFeign {

    /**
     * 测试连接
     *
     * @param request 数据库信息
     * @return 测试结果
     */
    @PostMapping("/test")
    boolean testConnection(@RequestBody TestConnectionRequest request);

    /**
     * 测试连接
     * 若失败, 返回失败原因
     *
     * @param request 数据库信息
     * @return 测试结果
     */
    @PostMapping("/test-with-detail")
    ConnectionTestDetailResponse testConnectionWithDetail(@RequestBody TestConnectionRequest request);

    /**
     * 获取所有库表字段信息
     *
     * @param connectionId 连接id
     * @return 库表字段信息
     */
    @GetMapping("/{connectionId}/metadata")
    DatabaseMetadataResponse getDatabaseMetadata(@PathVariable("connectionId") Integer connectionId);

    /**
     * 获取所有表
     *
     * @param connectionId 连接id
     * @return {@link TableResponse}
     */
    @GetMapping("/{connectionId}/table-list")
    List<TableResponse> getTableList(@PathVariable("connectionId") Integer connectionId);

    /**
     * 判断表是否存在
     *
     * @param connectionId 连接id
     * @param tableName    表名称
     * @return true：存在  false：不存在
     */
    @GetMapping("/{connectionId}/table-exist")
    boolean tableExist(@PathVariable("connectionId") Integer connectionId,
        @RequestParam("tableName") String tableName);

    /**
     * 获取表的字段信息
     *
     * @param connectionId 连接id
     * @param tableName    表名
     * @return {@link FieldMappingResponse}
     */
    @GetMapping("/{connectionId}/table-fields")
    FieldMappingResponse getTableFields(@PathVariable("connectionId") Integer connectionId,
        @RequestParam("tableName") String tableName);

    /**
     * 建表
     *
     * @param connectionId       连接id
     * @param createTableRequest 建表请求
     * @return 成功或失败
     */
    @PostMapping("/{connectionId}/create-table")
    StorageEngineResponse createTable(@PathVariable("connectionId") Integer connectionId,
        @RequestBody CreateTableRequest createTableRequest);

    /**
     * 删除表
     *
     * @param connectionId 连接id
     * @param tableName    表名称
     * @return 成功或失败
     */
    @DeleteMapping("/{connectionId}/drop-table")
    boolean dropTable(@PathVariable("connectionId") Integer connectionId,
        @RequestParam("tableName") String tableName);

    /**
     * nebula数据存储
     *
     * @param request 请求参数
     */
    @PostMapping("/nebula-storage")
    void nebulaStorage(@RequestBody NebulaStorageRequest request);

    /**
     * nebula数据迁移
     *
     * @param sourceConnectionId 源连接id
     * @param targetConnectionId 目标连接id
     * @return boolean
     * <AUTHOR>
     * @since 2024/12/30 10:53:01
     */
    @PostMapping("/nebula-data-transfer")
    boolean nebulaDataTransfer(@RequestParam("sourceConnectionId") Integer sourceConnectionId,
        @RequestParam("targetConnectionId") Integer targetConnectionId);

    /**
     * 存储文件到存储引擎
     *
     * @param tableInfo 表信息，包括表中英文名、字段信息、连接信息、minioPath等
     * @param connectionId 连接id
     * @return 是否保存成功
     */
    @PostMapping(value = "/{connectionId}/load-file-data")
    boolean saveFileToStorage(@PathVariable("connectionId") Integer connectionId, @RequestBody TableInfo tableInfo);
}
