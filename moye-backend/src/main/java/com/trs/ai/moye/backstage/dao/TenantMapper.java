package com.trs.ai.moye.backstage.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.backstage.entity.Tenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 租户
 *
 * <AUTHOR>
 * @since 2022/12/27 13:51
 */
@Mapper
public interface TenantMapper extends BaseMapper<Tenant> {

    /**
     * 查询名称
     *
     * @param id 租户id
     * @return java.lang.String 租户名称
     * <AUTHOR>
     * @since 2022/12/28 16:56
     */
    String selectNameById(@Param("id") Integer id);
}
