package com.trs.ai.moye.backstage.request;

import com.trs.moye.base.common.enums.ResultType;
import com.trs.moye.base.common.request.BasePageRequest;
import com.trs.moye.base.common.request.SearchParams;
import com.trs.moye.base.common.request.TimeRangeParams;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025-03-04 13:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ApiLogPageListRequest extends BasePageRequest {

    private String externalLogId;

    private ConnectionType connectionType;

    /**
     * 响应状态
     */
    private ResultType responseStatus;

    /**
     * 时间范围参数
     */
    protected TimeRangeParams timeRange;

    /**
     * 检索参数
     */
    protected SearchParams searchParams;
}
