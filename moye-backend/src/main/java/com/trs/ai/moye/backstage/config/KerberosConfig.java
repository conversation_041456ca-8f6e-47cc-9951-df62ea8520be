package com.trs.ai.moye.backstage.config;


import java.nio.file.Paths;
import javax.validation.constraints.NotBlank;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;


/**
 * pvc 路径管理
 * <br>
 * 配置持久卷挂载的路径和下级目录
 * <br>
 * 需持久化的文件可放置在对应路径下
 *
 * <AUTHOR>
 */
@Setter
@Validated
@Configuration
@ConfigurationProperties(prefix = "kerberos")
public class KerberosConfig {

    /**
     * 基础持久卷（实施上，该持久卷为RWX，与其他服务共享）
     */
    private DirProperties dir = new DirProperties();

    /**
     * kerberos 凭据文件存放目录
     *
     * @return 目录路径
     */
    public String getStoragePath() {
        return dir.getStoragePath();
    }

    /**
     * 基础持久卷挂载路径 和 下级目录
     */
    @Setter
    static class DirProperties {

        /**
         * 持久卷 在容器内的 挂载路径
         */
        @NotBlank(message = "基础pvc路径为空")
        private String path;
        /**
         * 存放 kerberos 凭证文件 的 目录
         */
        @NotBlank(message = "kerberos凭据目录路径为空")
        private String name;

        protected String getStoragePath() {
            return Paths.get(path, name).toString();
        }
    }


}
