package com.trs.ai.moye.backstage.service;

import com.trs.ai.moye.backstage.request.ExclusiveDataModelListRequest;
import com.trs.ai.moye.backstage.request.ExclusiveNodeConfigRequest;
import com.trs.ai.moye.backstage.response.exclusive.ExclusiveDataModelListResponse;
import com.trs.ai.moye.backstage.response.exclusive.ExclusiveNodeConfigListResponse;
import com.trs.moye.base.common.response.IdNameResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-13 20:30
 */
public interface StreamExclusiveNodeConfigService {

    /**
     * 可选择的数据建模列表
     *
     * @return 数据模型列表
     */
    List<IdNameResponse> selectableDataModelList();

    /**
     * 专属配置列表
     *
     * @return 数据模型列表
     */
    ExclusiveNodeConfigListResponse exclusiveConfigList();

    /**
     * 专属监控列表
     *
     * @param request 请求参数
     * @return 数据模型列表
     */
    ExclusiveDataModelListResponse exclusiveMonitorList(ExclusiveDataModelListRequest request);

    /**
     * 保存专属节点配置
     *
     * @param requests 专属节点配置列表
     */
    void saveExclusiveNodeConfigs(List<ExclusiveNodeConfigRequest> requests);
}
