package com.trs.ai.moye.permission.service;

import static com.trs.moye.base.common.constants.RoleConstants.SUPER_ADMIN_ROLE_ID;

import com.trs.ai.moye.backstage.entity.User;
import com.trs.ai.moye.common.exception.AuthenticationException;
import java.util.List;
import javax.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * 当前用户服务类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/2/5 9:58 下午
 **/
@Slf4j
public class AuthHelper {

    /**
     * 当前用户租户id：重构中的实体库标签库需要这个租户id，重构完成后移除掉这个属性
     */
    private static final int CURRENT_USER_TENANT_ID = 510101;

    private AuthHelper() {

    }

    /**
     * 获取当前登录用户
     *
     * @return 用户
     **/
    @Nullable
    public static User getNullableCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            return (User) authentication.getPrincipal();
        }catch (Exception e) {
            log.error("parse login user error!", e);
            return null;
        }
    }

    /**
     * 获取当前登录用户
     *
     * @return 用户
     **/
    public static User getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User user = (User) authentication.getPrincipal();
            if (user == null) {
                throw new AuthenticationException("current login user not found!");
            }
            return user;
        } catch (Exception e) {
            throw new AuthenticationException("parse login user error!", e);
        }
    }

    /**
     * 获取当前登录用户id
     *
     * @return 用户id
     **/
    public static Integer getCurrentUserId() {
        return getCurrentUser().getId();
    }

    /**
     * 获取当前登录用户租户id 注意：当前用户租户id：重构中的实体库标签库需要这个租户id，重构完成后移除掉这个属性
     *
     * @return 租户id
     */
    public static Integer getTenantId() {
        return CURRENT_USER_TENANT_ID;
    }

    /**
     * 检查当前用户是否是超级管理员，不是超级管理员则抛出异常
     *
     * @return true 是超级管理员，false 不是
     */
    public static boolean isSuperAdmin() {
        User user = getCurrentUser();
        List<Integer> roleIds = user.getRoleIds();
        return roleIds.contains(SUPER_ADMIN_ROLE_ID);
    }

    /**
     * 检查当前用户是否是超级管理员，不是超级管理员则抛出异常
     */
    public static void checkSuperAdmin() {
        if (!isSuperAdmin()) {
            throw new AuthenticationException(String.format("当前用户【%s】不是超级管理员，无权操作！", getCurrentUser().getAccount()));
        }
    }
}

