package com.trs.ai.moye.storageengine.request.nebula;

import com.trs.moye.base.data.standard.entity.TagEdgeField;
import com.trs.ai.moye.storageengine.dto.nebula.AddModelTagEdgeFieldDto;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MultiValuedMap;

/**
 * <AUTHOR>
 * @since 2025-01-20 10:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class AddModelTagEdgeFieldsRequest extends BaseModelTagEdgeFieldsRequest {


    /**
     * 要添加的tag
     */
    private List<AddModelTagEdgeFieldDto> tag;
    /**
     * 要添加的edge
     */
    private List<AddModelTagEdgeFieldDto> edge;

    public AddModelTagEdgeFieldsRequest(Integer modelId
        , MultiValuedMap<String, TagEdgeField> updateTagFieldMap,
        MultiValuedMap<String, TagEdgeField> updateEdgeFieldMap) {
        setDataModelId(modelId);
        this.tag = buildModelTagEdgeFieldDtoList(updateTagFieldMap);
        this.edge = buildModelTagEdgeFieldDtoList(updateEdgeFieldMap);
    }

    private List<AddModelTagEdgeFieldDto> buildModelTagEdgeFieldDtoList(
        MultiValuedMap<String, TagEdgeField> updateTagFieldMap) {
        Set<String> keySet = updateTagFieldMap.keySet();
        List<AddModelTagEdgeFieldDto> dtoList = new ArrayList<>(keySet.size());
        for (String key : keySet) {
            dtoList.add(new AddModelTagEdgeFieldDto(key, new ArrayList<>(updateTagFieldMap.get(key))));
        }
        return dtoList;
    }
}
