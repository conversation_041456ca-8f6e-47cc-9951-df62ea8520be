package com.trs.ai.moye.backstage.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.trs.moye.base.common.enums.ResultType;
import com.trs.moye.base.common.log.api.ApiLog;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import java.time.LocalDateTime;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-03-05 17:01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiLogResponse {

    private String id;

    /**
     * 外部日志id，例如一体化平台发送的问答id
     */
    private String externalLogId;

    /**
     * 应用名称
     */
    private String applicationName;


    /**
     * API名称
     */
    private String apiName;

    /**
     * API名称
     */
    private String apiPath;

    /**
     * 请求参数
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, Object> requestParameters;

    /**
     * 请求时间
     */
    private LocalDateTime requestTime; // 对应ClickHouse的DateTime64(3)类型，使用String保持时间精度

    /**
     * 响应状态:成功(SUCCESS)或失败(FAILURE)
     */
    private ResultType responseStatus;

    /**
     * 响应时长（毫秒）
     */
    private Long responseDuration;

    /**
     * 相应详情
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, Object> responseDetails;

    /**
     * 连接类型
     */
    private String connectionType;

    /**
     * 连接名称
     */
    private String connectionName;

    public ApiLogResponse(ApiLog apiLog) {
        this.id = String.valueOf(apiLog.getId());
        this.externalLogId = apiLog.getExternalLogId();
        this.applicationName = apiLog.getApplicationName();
        this.apiName = apiLog.getApiName();
        this.apiPath = apiLog.getApiPath();
        this.requestParameters = apiLog.getRequestParameters();
        this.requestTime = apiLog.getRequestTime();
        this.responseStatus = apiLog.getResponseStatus();
        this.responseDetails = apiLog.getResponseDetails();
        this.responseDuration = apiLog.getResponseDuration();
        this.connectionType = apiLog.getConnectionType();
        this.connectionName = apiLog.getConnectionName();
    }
}
