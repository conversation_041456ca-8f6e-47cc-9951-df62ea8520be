package com.trs.ai.moye.backstage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 用户登录日志表
 *
 * <AUTHOR>
 * @since 2022/11/8 9:50
 */
@Setter
@Getter
@NoArgsConstructor
@TableName(value = "user_log")
public class UserLog implements Serializable {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer userId;

    private LocalDateTime loginTime;

    private LocalDateTime logoutTime;

    private String ip;
}
