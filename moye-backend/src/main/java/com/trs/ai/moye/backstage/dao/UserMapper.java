package com.trs.ai.moye.backstage.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.backstage.entity.User;
import com.trs.ai.moye.backstage.entity.UserCheckRepeatRequest;
import com.trs.ai.moye.backstage.response.UserRoleInfoResponse;
import com.trs.moye.base.common.request.SearchParams;
import com.trs.moye.base.common.request.SortParams;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * users表数据访问接口
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {


    /**
     * 查询全部
     *
     * @return {@link User}
     * <AUTHOR>
     * @since 2024/9/24 11:58
     */
    List<User> selectAll();

    /**
     * 通过用户名找用户
     *
     * @param account 账号
     * @return {@link User} 用户
     */

    User findByAccount(@Param("account") String account);

    /**
     * 根据用户ID查找角色列表
     *
     * @param userId 用户ID
     * @return {@link UserRoleInfoResponse}
     */
    List<UserRoleInfoResponse> findRolesByUserId(@Param("userId") Integer userId);

    /**
     * 根据用户ID查找模块权限
     *
     * @param roleId 角色ID
     * @return {@link String}
     */
    List<String> findRoleModule(@Param("roleId") Integer roleId);

    /**
     * 根据用户ID查找操作权限
     *
     * @param roleId 角色ID
     * @return {@link String}
     */
    List<String> findRoleOperation(@Param("roleId") Integer roleId);

    /**
     * 设置用户登录失败次数
     *
     * @param id   用户id
     * @param nums 登录失败次数
     * <AUTHOR>
     * @since 2022/11/8 9:50
     */
    void updateLoginFailedNum(@Param("id") int id, @Param("nums") int nums);

    /**
     * 用户账号加锁，解锁
     *
     * @param userId 用户ID
     * <AUTHOR>
     * @since 2020/10/18 19:08
     */
    void updateUserStatus(@Param("userId") Integer userId);

    /**
     * 通过账号找到用户id
     *
     * @param account 账号
     * @return java.lang.Integer
     * <AUTHOR>
     * @since 2020/11/30 10:40
     */
    Integer selectIdByAccount(@Param("account") String account);

    /**
     * 查询重复用户
     *
     * @param request 请求
     * @return 用户
     */
    User getRepeatUser(@Param("request") UserCheckRepeatRequest request);


    /**
     * 找到该部门及子部门下用户数量
     *
     * @param departmentId 部门ID
     * @return {@link Integer}
     * <AUTHOR>
     * @since 2024/9/25 14:49
     */
    Integer countDepartmentByDepartmentId(@Param("departmentId") Set<Integer> departmentId);

    /**
     * 用户分页列表查询
     *
     * @param departmentId 部门id
     * @param searchParams 模糊查询参数
     * @param sortParams   排序参数
     * @param page         分页信息
     * @return 用户信息
     */
    Page<User> userPageList(@Param("departmentId") Integer departmentId,
        @Param("searchParams") SearchParams searchParams, @Param("sortParams") SortParams sortParams, Page<User> page);

    /**
     * 根据用户id查询用户名
     *
     * @param userId 用户id
     * @return {@link String }
     * <AUTHOR>
     * @since 2024/10/30 15:08:31
     */
    String selectUserNameById(@Param("userId") Integer userId);


    /**
     * 查询所有map
     *
     * @return {@link Map }
     * <AUTHOR>
     * @since 2024/11/4 17:09
     */
    @MapKey("id")
    Map<Integer, User> selectAllAsMap();

    /**
     * 根据角色id查询用户list
     *
     * @param roleId 角色id
     * @return 用户id列表
     */
    List<User> getUsersByRoleId(@Param("roleId") Integer roleId);

}
