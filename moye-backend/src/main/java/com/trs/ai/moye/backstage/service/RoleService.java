package com.trs.ai.moye.backstage.service;

import com.trs.ai.moye.backstage.request.RoleRequest;
import com.trs.ai.moye.backstage.response.CertificateResponse;
import com.trs.ai.moye.backstage.response.RoleResponse;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.request.BaseRequestParams;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/18
 **/
public interface RoleService {

    /**
     * 添加角色
     *
     * @param request 角色
     */
    void addRole(RoleRequest request);

    /**
     * 更新角色
     *
     * @param request 角色
     */
    void updateRole(RoleRequest request);

    /**
     * 删除角色
     *
     * @param id 角色id
     */
    void deleteRole(Integer id);

    /**
     * 获取角色列表
     *
     * @param request 请求参数
     * @return 角色列表
     */
    PageResponse<RoleResponse> pageList(BaseRequestParams request);

    /**
     * 检查角色名称是否重复
     *
     * @param name 角色名称
     * @param id   角色id
     * @return 是否重复
     */
    Boolean isRoleNameDuplicate(String name, Integer id);

    /**
     * 检查角色名称是否合法
     *
     * @param name 角色名称
     * @param id   角色id
     */
    void checkRoleNameValid(String name, Integer id);

    /**
     * 根据id获取详情
     *
     * @param id 角色id
     * @return 角色详情
     */
    RoleResponse getDetail(Integer id);

    /**
     * 获取凭证列表
     *
     * @return 凭证列表
     */
    List<CertificateResponse> selectCredentialsList();
}
