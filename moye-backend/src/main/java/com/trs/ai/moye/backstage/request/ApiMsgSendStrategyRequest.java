package com.trs.ai.moye.backstage.request;

import com.trs.ai.moye.backstage.entity.NoticeSendConfHttp;
import com.trs.ai.moye.data.operator.domain.vo.AbilityDTO;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description API消息推送策略请求
 * @since 2024/9/6 15:15
 **/
@Data
public class ApiMsgSendStrategyRequest {


    private Integer id;
    /**
     * 策略名称
     */
    @NotBlank
    private String name;

    /**
     * 推送用户
     */
    private List<Integer> notifyUserIds;

    /**
     * 推送租户
     */
    private List<Integer> notifyTenantIds;

    /**
     * 推送角色
     */
    private List<Integer> notifyRoleIds;

    /**
     * 推送的消息类型
     */
    private List<Integer> messageTypeIds;

    /**
     * 业务范围id
     */
    @NotNull
    private List<Integer> businessScopeIds;

    /**
     * 是否推送
     */
    private Boolean enable;

    /**
     * 外部API
     */
    @NotNull
    private AbilityDTO apiInfo;

    /**
     * 配置项
     */
    private String configuration;


    /**
     * 获取添加实体
     *
     * @return {@link NoticeSendConfHttp}
     * <AUTHOR>
     * @since 2024/11/1 10:40
     */
    public NoticeSendConfHttp getAddEntity() {
        NoticeSendConfHttp noticeSendConfHttp = new NoticeSendConfHttp();
        noticeSendConfHttp.setName(this.name);
        noticeSendConfHttp.setSendRoleIds(this.notifyRoleIds);
        noticeSendConfHttp.setSendUserIds(this.notifyUserIds);
        noticeSendConfHttp.setMessageTypeIds(this.messageTypeIds);
        noticeSendConfHttp.setBusinessIds(this.businessScopeIds);
        noticeSendConfHttp.setState(this.enable);
        noticeSendConfHttp.setApiInfo(this.apiInfo);
        noticeSendConfHttp.setSendParameter(this.configuration);
        return noticeSendConfHttp;
    }


    /**
     * 获取更新实体
     *
     * @param oldNoticeSendConfHttp 旧对象
     * @return {@link NoticeSendConfHttp}
     * <AUTHOR>
     * @since 2024/11/1 14:26
     */
    public NoticeSendConfHttp getUpdateEntity(NoticeSendConfHttp oldNoticeSendConfHttp) {
        NoticeSendConfHttp noticeSendConfHttp = getAddEntity();
        noticeSendConfHttp.setId(oldNoticeSendConfHttp.getId());
        noticeSendConfHttp.setCreateBy(oldNoticeSendConfHttp.getCreateBy());
        noticeSendConfHttp.setCreateTime(oldNoticeSendConfHttp.getCreateTime());
        return noticeSendConfHttp;
    }
}
