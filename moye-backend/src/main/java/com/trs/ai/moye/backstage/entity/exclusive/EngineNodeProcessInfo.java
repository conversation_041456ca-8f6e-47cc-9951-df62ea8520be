package com.trs.ai.moye.backstage.entity.exclusive;

import com.trs.ai.moye.monitor.entity.NodeProcessCountInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-01-14 10:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class EngineNodeProcessInfo extends BaseProcessInfo {

    /**
     * 节点地址，例如：192.168.0.1:8080
     */
    private String node;

    public EngineNodeProcessInfo(String node) {
        this.node = node;
        this.accessCount = 0;
        this.processCount = 0;
        this.processErrorCount = 0;
        this.processingTimeMillis = 0L;
    }

    public EngineNodeProcessInfo(NodeProcessCountInfo processCountInfo){
        this.node = processCountInfo.getNode();
        this.accessCount = 0;
        this.processCount = processCountInfo.getProcessCount();
        this.processErrorCount = processCountInfo.getProcessErrorCount();
        this.processingTimeMillis = processCountInfo.getProcessingTimeMillis();
    }
}
