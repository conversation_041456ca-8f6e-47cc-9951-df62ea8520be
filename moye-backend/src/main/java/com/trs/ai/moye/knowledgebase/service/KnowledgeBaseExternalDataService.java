package com.trs.ai.moye.knowledgebase.service;

import com.trs.ai.moye.knowledgebase.request.external.ExternalAttrDataRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalConditionRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalDataRankRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalMediaRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalMultiSearchRequest;
import com.trs.ai.moye.knowledgebase.request.external.ExternalPageListRequest;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.knowledgebase.entity.EntityColumn;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import java.util.List;
import java.util.Map;

/**
 * 实体业务接口
 *
 * <AUTHOR>
 * @since 2020/6/8 14:19
 */
public interface KnowledgeBaseExternalDataService {

    /**
     * 获取知识库数据分页列表
     *
     * @param request 请求参数
     * @return 知识库数据
     */
    PageResponse<Map<String, Object>> getPageList(ExternalPageListRequest request);

    /**
     * 获取知识库数据分页列表
     *
     * @param request 请求参数
     * @return 知识库数据
     */
    PageResponse<Map<String, Object>> getConditionPageList(ExternalConditionRequest request);

    /**
     * 获取知识库数据分页列表
     *
     * @param request 请求参数
     * @return 知识库数据
     */
    PageResponse<Map<String, Object>> getMultiSearchPageList(ExternalMultiSearchRequest request);

    /**
     * 获取知识库数据量
     *
     * @param type   知识库类型
     * @param enName 知识库中文名
     * @return 数据量
     */
    int getDataCount(KnowledgeBaseType type, String enName);

    /**
     * 获取属性数据
     *
     * @param request 请求参数
     * @return 属性数据
     */
    List<String> getAttrData(ExternalAttrDataRequest request);

    /**
     * 获取媒体数据列表
     *
     * @param request 请求参数
     * @return 媒体数据列表
     */
    List<String> getMediaDataList(ExternalMediaRequest request);

    /**
     * 数据排行
     *
     * @param request 请求参数
     * @return 数据排行
     */
    List<String> getDataRank(ExternalDataRankRequest request);

    /**
     * 删除数据
     *
     * @param type    知识库类型
     * @param enName  知识库英文名
     * @param dataIds 数据ID
     */
    void deleteData(KnowledgeBaseType type, String enName, List<Integer> dataIds);

    /**
     * 添加数据
     *
     * @param type           知识库类型
     * @param enName         知识库英文名
     * @param fieldValueList 字段值列表
     * @return 数据ID
     */
    long addData(KnowledgeBaseType type, String enName, List<EntityColumn> fieldValueList);

    /**
     * 更新数据
     *
     * @param type           知识库类型
     * @param enName         知识库英文名
     * @param dataId         数据ID
     * @param fieldValueList 字段值列表
     */
    void updateData(KnowledgeBaseType type, String enName, Integer dataId, List<EntityColumn> fieldValueList);

}
