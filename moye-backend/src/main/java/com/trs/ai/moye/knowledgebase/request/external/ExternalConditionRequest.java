package com.trs.ai.moye.knowledgebase.request.external;

import com.trs.moye.base.common.exception.BizException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 外部条件查询参数
 *
 * <AUTHOR>
 * @since 2024-11-25 11:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class ExternalConditionRequest extends ExternalPageListRequest {

    private String conditions;

    /**
     * 检查条件参数
     */
    public void checkConditions() {
        if (ObjectUtils.isEmpty(conditions)) {
            return;
        }
        if (sqlInj()) {
            throw new BizException("conditions参数可能存在sql注入风险，请规范查询条件");
        }
    }

    /**
     * sql注入关键词检测
     *
     * @return boolean
     */
    private boolean sqlInj() {
        // 构建正则表达式，用于匹配特殊字符
        String regex = "exec|insert|select|delete|update|count|\\*|chr|mid|master|truncate|char|declare|;|-|\\+";
        // 将输入字符串转为小写，并匹配正则表达式
        return conditions.toLowerCase().matches(regex);
    }
}
