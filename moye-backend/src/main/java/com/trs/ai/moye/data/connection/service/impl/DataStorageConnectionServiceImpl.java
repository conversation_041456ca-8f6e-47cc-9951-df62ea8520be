package com.trs.ai.moye.data.connection.service.impl;

import static com.trs.ai.moye.common.constants.Number.ZERO;

import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.ai.moye.data.connection.request.ConnectionListRequest;
import com.trs.ai.moye.data.connection.response.DataConnectionCardResponse;
import com.trs.ai.moye.data.connection.response.DataConnectionResponse;
import com.trs.ai.moye.data.connection.response.DataConnectionStatisticsResponse;
import com.trs.ai.moye.data.connection.service.DataStorageConnectionService;
import com.trs.ai.moye.data.model.dao.DataConnectionDisplayMapper;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 数据存储服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/18 14:07
 **/
@Service
@Slf4j
public class DataStorageConnectionServiceImpl implements DataStorageConnectionService {

    @Resource
    private DataConnectionDisplayMapper dataConnectionDisplayMapper;

    @Resource
    private DataConnectionMapper dataConnectionMapper;

    @Resource
    private DataStorageMapper dataStorageMapper;


    /**
     * 获取统计信息
     *
     * @return 统计信息
     */
    @Override
    public DataConnectionStatisticsResponse getStatistics() {
        return dataConnectionDisplayMapper.countStatistics(false);
    }


    @Override
    public List<DataConnectionCardResponse> getCardList(ConnectionListRequest request) {
        List<DataConnection> dataConnections = dataConnectionDisplayMapper.selectConnections(request, false);
        //判断是否需要返回图形化相关连接
        if (Objects.nonNull(request.getIsNeedGraphics()) && Boolean.FALSE.equals(request.getIsNeedGraphics())) {
            dataConnections = dataConnections.stream()
                .filter(dataConnection -> !ConnectionType.NEBULA.equals(dataConnection.getConnectionType())).toList();
        }
        DynamicUserNameService dynamicUserNameService = BeanUtil.getBean(DynamicUserNameService.class);
        Map<DataSourceCategory, List<DataConnectionResponse>> map = dataConnections.stream()
            .map(dataConnection -> new DataConnectionResponse(dataConnection, dynamicUserNameService))
            .collect(Collectors.groupingBy(DataConnectionResponse::getCategory));
        return map.entrySet().stream()
            .sorted(Comparator.comparing(entry -> entry.getKey().ordinal()))
            .map(entry -> new DataConnectionCardResponse(entry.getKey().getLabel(), entry.getValue()))
            .toList();
    }

    @Override
    public Boolean checkName(String name) {
        return dataConnectionMapper.existsByName(name, false);
    }

    @Override
    public Integer getConnectionUsedCount(Integer id) {
        List<DataStorage> dataStorages = dataStorageMapper.selectByConnectionId(id);
        return Objects.nonNull(dataStorages) ? dataStorages.size() : ZERO;
    }

}
