package com.trs.ai.moye.backstage.entity.exclusive;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据建模节点详情列表：数据建模所在引擎节点
 *
 * <AUTHOR>
 * @since 2025-01-10 13:44
 */
@Data
@NoArgsConstructor
public class DataModelNodeDetail {

    /**
     * 数据建模id
     */
    private int id;

    /**
     * 数据建模名称
     */
    private String name;

    /**
     * 期望节点数
     */
    private int expectNodeCount;


    /**
     * 节点列表
     */
    private List<String> actualNodes;

    /**
     * 实际节点数
     *
     * @return 实际节点数
     */
    @JsonProperty
    public int getActualNodeCount() {
        return actualNodes == null ? 0 : actualNodes.size();
    }
}
