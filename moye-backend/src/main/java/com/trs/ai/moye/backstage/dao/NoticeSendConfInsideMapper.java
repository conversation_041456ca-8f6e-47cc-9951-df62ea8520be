package com.trs.ai.moye.backstage.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.backstage.entity.NoticeSendConfInside;
import com.trs.moye.base.common.request.SearchParams;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 消息中心mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/30 18:01
 **/
@Mapper
public interface NoticeSendConfInsideMapper extends BaseMapper<NoticeSendConfInside> {


    /**
     * 获取消息分页列表
     *
     * @param searchParams   检索参数
     * @param messageTypeId  消息类型ID
     * @param toPage         分页
     * @param createTypeName 创建类型枚举
     * @return {@link NoticeSendConfInside}
     * <AUTHOR>
     * @since 2024/10/31 15:03
     */
    Page<NoticeSendConfInside> getNoticePageList(@Param("searchParams") SearchParams searchParams,
        @Param("messageTypeId") Integer messageTypeId, Page<Object> toPage, @Param("createType") String createTypeName);


    /**
     * 通过名称查询条数
     *
     * @param name 名称
     * @return {@link Integer }
     * <AUTHOR>
     * @since 2024/10/31 17:07
     */
    Integer selectByName(@Param("name") String name);


    /**
     * 通过ID更新状态
     *
     * @param id    ID
     * @param state 状态
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/10/31 17:21
     */
    Boolean updateStateById(@Param("id") Integer id, @Param("state") Boolean state);


    /**
     * 通过建模ID删除记录
     *
     * @param id 建模ID
     * @return {@link Integer}
     * <AUTHOR>
     * @since 2025/2/25 16:51
     */
    Integer deleteByDataModelId(@Param("id") Integer id);

}
