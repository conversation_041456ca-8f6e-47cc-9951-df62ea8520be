package com.trs.ai.moye.backstage.help;

import com.trs.ai.moye.backstage.entity.User;
import com.trs.ai.moye.common.web.filter.RequestUtils;
import com.trs.ai.moye.log.KafkaLogService;
import com.trs.ai.moye.permission.service.AuthHelper;
import com.trs.moye.base.common.annotaion.Ignore;
import com.trs.moye.base.common.annotaion.OperateLogSign;
import com.trs.moye.base.common.log.api.ApiLogUtils;
import com.trs.moye.base.common.log.operate.OperateLog;
import com.trs.moye.base.common.log.operate.OperateLogUtils;
import com.trs.moye.base.common.log.operate.OperateType;
import com.trs.moye.base.common.entity.ModuleEnum;
import com.trs.moye.base.common.entity.TwoTuple;
import com.trs.moye.base.common.enums.ResultType;
import com.trs.moye.base.common.help.container.DefaultSyncContainer;
import com.trs.moye.base.common.utils.ExceptionUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.common.utils.SnowflakeIdUtil;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;

/**
 * <AUTHOR>
 * @since 2025-03-04 17:58
 */
@Order(2000)
@Slf4j
@Aspect
@Component
public class OperateLogSignAspect {

    private static final Class<?>[] IGNORE_PARAMETER_TYPES = new Class<?>[]{HttpSession.class
        , HttpServletRequest.class
        , HttpServletResponse.class};


    private final DefaultSyncContainer<ProceedingJoinPoint, Method, ApiInfo> apiInfoContainer =
        new DefaultSyncContainer<>(joinPoint -> ((MethodSignature) joinPoint.getSignature()).getMethod(),
            this::parseApiInfo);

    @Resource
    private KafkaLogService kafkaLogService;

    /**
     * 定义切点为带有 @ApiLogSign 注解的方法
     */
    @Pointcut("@annotation(com.trs.moye.base.common.annotaion.OperateLogSign)")
    public void apiLogSignPointcut() {
    }

    /**
     * 使用环绕通知处理切点
     *
     * @param joinPoint 切点对象
     * @return 处理结果或抛出异常
     * @throws Throwable 异常信息
     */
    @Around("apiLogSignPointcut()")
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        OperateLog operateLog = createOperateLog(joinPoint);
        try {
            OperateLogUtils.set(operateLog);
            Object result = joinPoint.proceed();
            supplementSuccessInfo(operateLog);
            return result;
        } catch (Exception e) {
            // 3. 异常处理：记录异常日志
            supplementFailInfo(operateLog, e);
            throw e;
        } finally {
            ApiLogUtils.remove();
            sendLog(operateLog);
        }
    }

    private OperateLog createOperateLog(ProceedingJoinPoint joinPoint) {
        OperateLog operateLog = new OperateLog();
        operateLog.setId(SnowflakeIdUtil.newId());
        User user = AuthHelper.getNullableCurrentUser();
        if (user != null){
            // 不需要登录的方法不能使用OperateLogSign注解，因为缺少用户信息
            operateLog.setUserId(user.getId());
            operateLog.setUserName(user.getName());
        }
        operateLog.setOperateTime(LocalDateTime.now());
        operateLog.setIp(RequestUtils.getClientIp());
        ApiInfo apiInfo = apiInfoContainer.getOrCreateElement(joinPoint);
        operateLog.setModule(apiInfo.getModule());
        operateLog.setOperateType(apiInfo.getOperateType());
        operateLog.setApiName(apiInfo.getApiName());
        operateLog.setDetails(new LinkedHashMap<>());
        operateLog.setApiPath(RequestUtils.getUrlPath());
        Object[] args = joinPoint.getArgs();
        for (TwoTuple<Integer, String> parameter : apiInfo.getParameters()) {
            operateLog.getDetails().put(parameter.getSecond(), args[parameter.getFirst()]);
        }
        return operateLog;
    }

    private ApiInfo parseApiInfo(ProceedingJoinPoint joinPoint) {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        OperateLogSign sign = method.getAnnotation(OperateLogSign.class);
        ApiInfo apiInfo = new ApiInfo();
        apiInfo.setModule(sign.module());
        apiInfo.setOperateType(parseOperateType(method, sign));
        apiInfo.setApiName(sign.apiName().isEmpty() ? method.getName() : sign.apiName());
        Parameter[] parameters = method.getParameters();
        List<TwoTuple<Integer, String>> params = new ArrayList<>(parameters.length);
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            Class<?> parameterType = parameter.getType();
            if (parameter.isAnnotationPresent(Ignore.class) || isIgnoreParameterType(parameterType)) {
                continue;
            }
            params.add(new TwoTuple<>(i, parameters[i].getName()));
        }
        apiInfo.setParameters(params);
        return apiInfo;
    }

    private OperateType parseOperateType(Method method, OperateLogSign sign) {
        if (sign.operateType() != OperateType.UNKNOWN) {
            return sign.operateType();
        }
        for (Annotation annotation : method.getAnnotations()) {
            if (annotation instanceof PostMapping) {
                return OperateType.ADD;
            }
            if (annotation instanceof DeleteMapping) {
                return OperateType.DELETE;
            }
            if (annotation instanceof PutMapping) {
                return OperateType.UPDATE;
            }
            if (annotation instanceof GetMapping) {
                return OperateType.SELECT;
            }
        }
        return OperateType.UNKNOWN;
    }

    private void supplementSuccessInfo(OperateLog operateLog) {
        operateLog.setOperateResult(ResultType.SUCCESS);
        operateLog.setResponseDuration(Duration.between(operateLog.getOperateTime(), LocalDateTime.now()).toMillis());
    }

    private void supplementFailInfo(OperateLog operateLog, Exception exception) {
        operateLog.setOperateResult(ResultType.FAILURE);
        operateLog.setResponseDuration(Duration.between(operateLog.getOperateTime(), LocalDateTime.now()).toMillis());
        operateLog.getDetails().put("异常信息", ExceptionUtils.readTraceMessage(exception));
    }

    private void sendLog(OperateLog operateLog) {
        try {
            kafkaLogService.sendOperateLog(operateLog);
        } catch (Exception e) {
            log.error("发送API日志失败，日志信息：{}", JsonUtils.toJsonString(operateLog), e);
        }
    }

    private static boolean isIgnoreParameterType(Class<?> type) {
        for (Class<?> ignoreType : IGNORE_PARAMETER_TYPES) {
            if (ignoreType.isAssignableFrom(type)) {
                return true;
            }
        }
        return false;
    }

    @Data
    private static class ApiInfo {

        /**
         * 模块，枚举
         */
        private ModuleEnum module;

        /**
         * 操作类型，枚举
         */
        private OperateType operateType;

        /**
         * api名称
         */
        private String apiName;

        /**
         * 方法参数列表，参数名称和参数索引
         */
        private List<TwoTuple<Integer, String>> parameters;
    }


}

