package com.trs.ai.moye.backstage.controller;

import com.trs.ai.moye.backstage.request.ApiLogPageListRequest;
import com.trs.ai.moye.backstage.response.ApiLogResponse;
import com.trs.ai.moye.backstage.response.ApiLogTraceResponse;
import com.trs.ai.moye.backstage.service.ApiLogService;
import com.trs.moye.base.common.response.PageResponse;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025-03-04 13:46
 */
@RestController
@RequestMapping("/api-log")
@Validated
public class ApiLogController {

    @Resource
    private ApiLogService apiLogService;


    /**
     * 分页列表
     *
     * @param request 请求参数
     * @return 配置
     */
    @PostMapping("/page-list")
    public PageResponse<ApiLogResponse> pageList(@Validated @RequestBody ApiLogPageListRequest request) {
        return apiLogService.pageList(request).toNewPageResult(ApiLogResponse::new);
    }

    /**
     * 日志执行链路
     *
     * @param id 日志记录id
     * @return 配置
     */
    @GetMapping("/{id}/tracer")
    public List<ApiLogTraceResponse> getTracerList(@PathVariable Long id) {
        return apiLogService.getTracerList(id);
    }
}
