package com.trs.ai.moye.backstage.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.backstage.entity.NoticeSendConfHttp;
import com.trs.moye.base.common.request.SearchParams;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * http消息发送配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/31 14:12
 **/
@Mapper
public interface NoticeSendConfHttpMapper extends BaseMapper<NoticeSendConfHttp> {


    /**
     * 通过名字查询
     *
     * @param name 名称
     * @return {@link NoticeSendConfHttp}
     * <AUTHOR>
     * @since 2024/11/1 10:08
     */
    List<NoticeSendConfHttp> selectByName(String name);

    /**
     * 获取分页查询
     *
     * @param searchParams  检索字段
     * @param messageTypeId 消息类型Id
     * @param toPage        分页
     * @return {@link NoticeSendConfHttp}
     * <AUTHOR>
     * @since 2024/11/1 11:10
     */
    Page<NoticeSendConfHttp> getNoticePageList(@Param("searchParams") SearchParams searchParams,
        @Param("messageTypeId") Integer messageTypeId, Page<Object> toPage);


    /**
     * 通过ID更新状态
     *
     * @param id    ID
     * @param state 状态
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/11/1 14:42
     */
    Boolean updateStateById(@Param("id") Integer id, @Param("state") Boolean state);

}
