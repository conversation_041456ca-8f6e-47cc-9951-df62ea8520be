package com.trs.ai.moye.backstage.entity.ui;

import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 数据建模层级配置参数
 *
 * <AUTHOR>
 * @since 2025/04/14 16:57:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class ModelLayerAppConfigParam extends AppConfigParam {

    /**
     * 贴源库名称
     */
    @NotBlank(message = "贴源库名称不能为空")
    private String odsName;

    /**
     * 要素库名称
     */
    @NotBlank(message = "要素库名称不能为空")
    private String dwdName;

    /**
     * 主题库名称
     */
    @NotBlank(message = "主题库名称不能为空")
    private String themeName;

    /**
     * 专题库名称
     */
    @NotBlank(message = "专题库名称不能为空")
    private String subjectName;

    /**
     * 指标库名称
     */
    @NotBlank(message = "指标库名称不能为空")
    private String indicatorName;
}