package com.trs.ai.moye.backstage.response;


import com.trs.moye.base.common.enums.ModelLayer;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * V4消息中心请求列表返回体
 *
 * <AUTHOR>
 * @since 2024/8/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoticeResponse {

    /**
     * id主键
     */
    private Integer id;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息类型
     */
    private int noticeTypeCode;

    /**
     * 消息类型中文
     */
    private String noticeType;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 数据模建模d
     */
    private Integer dataModelId;

    /**
     * 建模的类型
     */
    private ModelLayer layer;

    /**
     * 是否已读
     */
    private boolean readFlag = false;
}
