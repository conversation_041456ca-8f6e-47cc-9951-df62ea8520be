package com.trs.ai.moye.backstage.config;

import com.trs.ai.moye.backstage.response.SparkConfigResponse;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

/**
 * spark配置项
 *
 * <AUTHOR>
 * @since 2024/12/10
 */
@Validated
@Configuration
@Data
@ConfigurationProperties(prefix = "spark")
public class SparkConfig {

    /**
     * 驱动核心数量
     */
    @NotBlank(message = "spark驱动核心数量不能为空")
    private String driverCores;

    /**
     * 驱动内存大小
     */
    @NotBlank(message = "spark驱动内存大小不能为空")
    private String driverMemory;

    /**
     * 执行内存大小
     */
    @NotBlank(message = "spark执行内存数量不能为空")
    private String executorMemory;

    /**
     * 最大核心数
     */
    @NotBlank(message = "spark最大核心数不能为空")
    private String coresMax;

    /**
     * 执行核心数
     */
    @NotBlank(message = "spark执行核心数量不能为空")
    private String executorCores;

    /**
     * 执行实例数
     */
    private String executorInstances = "4";

    /**
     * 获取sparkConfig具体值
     *
     * @return sparkConfig具体值
     */
    public List<SparkConfigResponse> getSparkConfig() {
        return List.of(
            new SparkConfigResponse("spark.driver.cores", this.driverCores),
            new SparkConfigResponse("spark.driver.memory", this.driverMemory),
            new SparkConfigResponse("spark.executor.memory", this.executorMemory),
            new SparkConfigResponse("spark.cores.max", this.coresMax),
            new SparkConfigResponse("spark.executor.cores", this.executorCores),
            new SparkConfigResponse("spark.executor.instances", this.executorInstances)
        );
    }

}
