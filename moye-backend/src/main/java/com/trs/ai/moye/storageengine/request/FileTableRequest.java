package com.trs.ai.moye.storageengine.request;

import com.trs.moye.base.data.source.setting.file.FtpDataSourceSettings;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-10-08 14:07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class FileTableRequest extends FileBaseRequest{

    public FileTableRequest(FtpDataSourceSettings fileSettings){
        this.workingDirectory = fileSettings.getWorkingDirectory();
        this.fileTypeConfig = fileSettings.getFileTypeConfig();
        this.fileChosenConfig = fileSettings.getFileChosenConfig();
    }
}
