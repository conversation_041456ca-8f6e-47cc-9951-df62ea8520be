package com.trs.ai.moye.storageengine.request;

import com.trs.moye.base.data.execute.ExecuteParams;
import com.trs.moye.base.data.execute.ExecuteMode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 立即执行参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/12 14:58
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubmitJobRequest {

    /**
     * 任务id
     */
    private Integer dataModelId;

    /**
     * 执行模式 立即执行 定时调度
     */
    private ExecuteMode executeMode;

    /**
     * 执行参数
     */
    private ExecuteParams executeParams;


    /**
     * 构造存储引擎参数
     *
     * @param id            数据建模ID
     * @param executeParams 前端请求立即执行需要的参数
     * @return {@link SubmitJobRequest}
     * <AUTHOR>
     * @since 2024/10/12 15:06
     */
    public static SubmitJobRequest from(ExecuteParams executeParams, Integer id) {
        SubmitJobRequest param = new SubmitJobRequest();
        param.setDataModelId(id);
        param.setExecuteMode(ExecuteMode.IMMEDIATE);
        param.setExecuteParams(executeParams);
        return param;
    }


}
