package com.trs.ai.moye.storageengine.feign;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.ai.moye.common.config.OpenFeignConfig;
import com.trs.ai.moye.data.connection.request.TestConnectionRequest;
import com.trs.ai.moye.data.model.response.StorageEngineResponse;
import com.trs.ai.moye.storageengine.request.CreateTableRequest;
import com.trs.ai.moye.storageengine.response.ConnectionTestDetailResponse;
import com.trs.ai.moye.storageengine.response.alltable.TableResponse;
import com.trs.moye.base.data.model.field.mapping.FieldMappingResponse;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 存储引擎数据库连接feign
 */
@FeignClient(name = "moye-storage-engine", path = "/storage/connection/mq", contextId = "mq", configuration = OpenFeignConfig.class)
public interface MqConnectionFeign {

    /**
     * 测试连接
     *
     * @param request 测试连接请求
     * @return 测试连接结果
     */
    @PostMapping("/test")
    boolean testConnection(@RequestBody TestConnectionRequest request);

    /**
     * 测试连接
     * 若失败, 返回失败原因
     *
     * @param request 测试连接请求
     * @return 测试连接结果
     */
    @PostMapping("/test-with-detail")
    ConnectionTestDetailResponse testConnectionWithDetail(@RequestBody TestConnectionRequest request);

    /**
     * 获取所有表
     *
     * @param connectionId 连接id
     * @return {@link TableResponse}
     */
    @GetMapping("/{connectionId}/table-list")
    List<TableResponse> getAllTable(@PathVariable("connectionId") Integer connectionId);

    /**
     * 获取表的字段信息
     *
     * @param connectionId 连接id
     * @param tableName    表名
     * @return {@link FieldMappingResponse}
     */
    @GetMapping("/{connectionId}/table-fields")
    FieldMappingResponse getTableFields(@PathVariable("connectionId") Integer connectionId,
        @RequestParam("tableName") String tableName);

    /**
     * 建表
     *
     * @param connectionId       连接id
     * @param createTableRequest 建表请求
     * @return 成功或失败
     */
    @PostMapping("/{connectionId}/create-table")
    StorageEngineResponse createTable(@PathVariable("connectionId") Integer connectionId,
        @RequestBody CreateTableRequest createTableRequest);

    /**
     * 获取最近一条数据
     *
     * @param connectionId 连接id
     * @param tableName    主题名
     * @param count       获取条数
     * @return 最近一条数据
     */
    @GetMapping("/{connectionId}/recent-message")
    List<JsonNode> getMessage(@PathVariable("connectionId") Integer connectionId,
        @RequestParam("tableName") String tableName, @RequestParam("count") Integer count);
}
