package com.trs.ai.moye.backstage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 角色-用户表
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/9/29 17:10
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "user_role")
public class UserRole {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer roleId;

    private Integer userId;
}
