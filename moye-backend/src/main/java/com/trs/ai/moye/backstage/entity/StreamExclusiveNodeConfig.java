package com.trs.ai.moye.backstage.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.trs.ai.moye.backstage.request.ExclusiveNodeConfigRequest;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.response.IdNameResponse;
import java.util.Set;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 流式专属节点配置表.
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@TableName(value = "stream_exclusive_node_config", autoResultMap = true)
public class StreamExclusiveNodeConfig extends AuditBaseEntity {

    // 通用节点数据建模id
    public static final int COMMON_DATA_MODEL_ID = -1;

    // 通用节点数据建模id
    public static final String COMMON_DATA_MODEL_NAME = "通用节点配置";

    /**
     * 数据源id
     **/
    @NotNull(message = "数据源id不允许空")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<IdNameResponse> dataModels;

    /**
     * 期望节点数
     **/
    @NotNull(message = "期望节点数不允许空")
    @Min(value = 0, message = "期望节点数不允许小于1")
    private Integer expectNodeCount;

    /**
     * 优先级
     **/
    @NotNull(message = "优先级不允许空")
    private Integer priority;

    /**
     * 并发线程数, nacos上对此有默认值
     */
    private Integer concurrentThreads;

    /**
     * 限流, 单位: 条数据/分钟
     */
    private Integer rateLimit;

    public StreamExclusiveNodeConfig(ExclusiveNodeConfigRequest request) {
        this.id = request.getId();
        this.expectNodeCount = request.getExpectNodeCount();
        this.priority = request.getPriority();
    }

    /**
     * 构建默认通用节点配置
     *
     * @return 默认通用节点
     */
    public static StreamExclusiveNodeConfig buildDefaultCommonNodeConfig() {
        StreamExclusiveNodeConfig commonNodeConfig = new StreamExclusiveNodeConfig();
        commonNodeConfig.setDataModels(Set.of(new IdNameResponse(COMMON_DATA_MODEL_ID, COMMON_DATA_MODEL_NAME)));
        commonNodeConfig.setExpectNodeCount(1);
        commonNodeConfig.setPriority(0);
        return commonNodeConfig;
    }
}