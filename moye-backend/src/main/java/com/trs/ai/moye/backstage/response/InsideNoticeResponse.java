package com.trs.ai.moye.backstage.response;

import com.trs.ai.moye.backstage.entity.NoticeSendConfInside;
import com.trs.ai.moye.backstage.enums.NoticeSendConfInsideCreateType;
import com.trs.ai.moye.backstage.enums.NoticeType;
import com.trs.ai.moye.backstage.utils.NoticeCenterUtils;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

/**
 * 站内消息响应
 *
 * <AUTHOR>
 * @since 2024/10/31 14:46
 */
@Data
@Validated
@AllArgsConstructor
@NoArgsConstructor
public class InsideNoticeResponse {

    private Integer id;

    /**
     * 策略名称
     */
    private String name;
    /**
     * 通知角色id集合
     */
    private List<Integer> notifyRoleIds;
    /**
     * 通知的角色名称集合
     */
    private String notifyRoleNames;
    /**
     * 通知用户id集合
     */
    private List<Integer> notifyUserIds;
    /**
     * 通知的用户名称集合
     */
    private String notifyUserNames;
    /**
     * 接收消息类型id集合
     */
    private List<Integer> messageTypeIds;
    /**
     * 接收消息类型名称集合
     */
    private String messageTypeNames;

    /**
     * 数据范围id集合
     */
    private List<Integer> businessScopeIds;
    /**
     * 数据范围名称集合
     */
    private String businessScopeNames;
    /**
     * 是否启用 0:禁用 1:启用
     */
    private boolean enable;
    /**
     * 创建人id
     */
    private Integer createId;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新人id
     */
    private Integer updateId;
    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建方式
     */
    private NoticeSendConfInsideCreateType createType;

    /**
     * 构造响应体
     *
     * @param noticeSendConfInside 消息配置
     * @param users                用户
     * @param roles                角色
     * @param category             层级
     * <AUTHOR>
     * @since 2024/11/1 11:38
     */
    public InsideNoticeResponse(NoticeSendConfInside noticeSendConfInside, Map<Integer, String> users,
        Map<Integer, String> roles, Map<Integer, String> category) {
        this.id = noticeSendConfInside.getId();
        this.name = noticeSendConfInside.getName();
        this.enable = noticeSendConfInside.getState();
        this.createTime = noticeSendConfInside.getCreateTime();
        this.updateTime = noticeSendConfInside.getUpdateTime();
        this.notifyUserIds = noticeSendConfInside.getSendUserIds();
        this.notifyUserNames = NoticeCenterUtils.getCorrespondingValue(users, noticeSendConfInside.getSendUserIds());
        this.notifyRoleIds = noticeSendConfInside.getSendRoleIds();
        this.notifyRoleNames = NoticeCenterUtils.getCorrespondingValue(roles, noticeSendConfInside.getSendRoleIds());
        this.messageTypeIds = noticeSendConfInside.getMessageTypeIds();
        this.messageTypeNames = NoticeCenterUtils.getCorrespondingValue(NoticeType.toMap(),
            noticeSendConfInside.getMessageTypeIds());
        this.businessScopeIds = noticeSendConfInside.getBusinessIds();
        this.businessScopeNames = NoticeCenterUtils.getCorrespondingValue(category,
            noticeSendConfInside.getBusinessIds());
        this.updateId = noticeSendConfInside.getUpdateBy();
        this.updateBy = users.getOrDefault(noticeSendConfInside.getUpdateBy(), "");
        this.createId = noticeSendConfInside.getCreateBy();
        this.createBy = users.getOrDefault(noticeSendConfInside.getCreateBy(), "");
        this.createType = noticeSendConfInside.getCreateModel();
    }
}
