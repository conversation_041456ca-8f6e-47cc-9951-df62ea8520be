package com.trs.ai.moye.backstage.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.backstage.entity.StreamExclusiveNodeConfig;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2025-01-10 16:04
 */

@Mapper
public interface StreamExclusiveNodeConfigMapper extends BaseMapper<StreamExclusiveNodeConfig> {

    /**
     * id集合查询
     *
     * @return {@link List}<{@link StreamExclusiveNodeConfig}>
     */
    List<StreamExclusiveNodeConfig> selectAll();

    /**
     * 更新数据
     *
     * @param streamExclusiveNodeConfigs streamExclusiveNodeConfigs
     */
    void updateById(@Param("configs") List<StreamExclusiveNodeConfig> streamExclusiveNodeConfigs);
}
