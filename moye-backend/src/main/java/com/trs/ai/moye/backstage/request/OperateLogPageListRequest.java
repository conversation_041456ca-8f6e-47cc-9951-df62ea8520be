package com.trs.ai.moye.backstage.request;

import com.trs.moye.base.common.enums.ResultType;
import com.trs.moye.base.common.log.operate.OperateType;
import com.trs.moye.base.common.request.BasePageRequest;
import com.trs.moye.base.common.request.SearchParams;
import com.trs.moye.base.common.request.TimeRangeParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025-03-04 13:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OperateLogPageListRequest extends BasePageRequest {

    /**
     * 操作类型，枚举
     */
    private OperateType operateType;

    /**
     * 操作结果
     */
    private ResultType operateResult;

    /**
     * 时间范围参数
     */
    protected TimeRangeParams timeRange;

    /**
     * 检索参数
     */
    protected SearchParams searchParams;
}
