package com.trs.ai.moye.storageengine.feign;


import com.trs.ai.moye.common.config.OpenFeignConfig;
import com.trs.ai.moye.data.model.response.KafkaBacklogResponse;
import com.trs.ai.moye.storageengine.dto.MonitorDTO;
import com.trs.moye.base.common.entity.mq.MqConsumeInfoResponse;
import com.trs.moye.base.monitor.entity.TodayMonitorMetric;
import java.util.List;
import java.util.Map;
import javax.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 请求存储引擎feign接口
 *
 * <AUTHOR>
 * @since 2025/1/16 15:19
 */
@FeignClient(name = "moye-storage-engine", path = "/storage/monitor", configuration = OpenFeignConfig.class)
public interface MonitorFeign {

    /**
     * 获取消费信息
     *
     * @param monitorDTO 监控 DTO
     * @return {@link MqConsumeInfoResponse }
     */
    @PostMapping("/mq-consume-info")
    MqConsumeInfoResponse getMqConsumeInfo(@RequestBody @Valid MonitorDTO monitorDTO);


    /**
     * 获取数据源数据积压量
     *
     * @param monitorDTO 数据源信息
     * @return 数据总量
     */
    @PostMapping("/lag")
    Long getDataSourceLag(@RequestBody @Valid MonitorDTO monitorDTO);


    /**
     * 要素库获取卡夫卡各个存储点的积压情况，
     *
     * @param monitorDTO 监控 DTO
     * @return {@link KafkaBacklogResponse }
     * <AUTHOR>
     * @since 2025/02/17 16:02:00
     */
    @PostMapping("/kafka-consume-info")
    List<KafkaBacklogResponse> getKafkaBacklogInfo(@RequestBody @Valid MonitorDTO monitorDTO);

    /**
     * 获取数据处理的kafka消费信息
     *
     * @param connectionId 数据源连接id
     * @param topic        kafka主题
     * @param group        kafka消费组
     * @return {@link MqConsumeInfoResponse}
     */
    @GetMapping("/data-process/consume-info/one")
    MqConsumeInfoResponse getDataProcessKafkaConsumeInfo(@RequestParam(name = "connectionId") Integer connectionId,
        @RequestParam(name = "topic") String topic,
        @RequestParam(name = "group") String group);

    /**
     * 获取数据处理的kafka消费信息
     *
     * @param connectionId 连接id
     * @param topic        主题
     * @param group        消费组
     * @return {@link MqConsumeInfoResponse}
     */
    @GetMapping("/data-process/today-monitor")
    TodayMonitorMetric dataProcessTodayMonitor(@RequestParam(name = "connectionId") Integer connectionId,
        @RequestParam(name = "topic") String topic, @RequestParam(name = "group") String group);

    /**
     * 获取数据处理的kafka消费信息列表
     *
     * @param topicGroupMap topic和group的映射关系，key为topic，value为group
     * @return {@link Map} key为topic，value为{@link MqConsumeInfoResponse}
     */
    @PostMapping("/data-process/consume-info/multiple")
    Map<String, MqConsumeInfoResponse> getDataProcessKafkaConsumeInfoMap(
        @RequestBody Map<String, String> topicGroupMap);

    /**
     * 获取数据处理的kafka消费信息列表
     *
     * @param topicGroupMap topic集合
     * @return 消费信息Map，key为topic，value为消费信息
     */
    @PostMapping("/data-storage/today-monitor")
    Map<String, TodayMonitorMetric> dataStorageTodayMonitor(@RequestBody Map<String, String> topicGroupMap);
}






