package com.trs.ai.moye.knowledgebase.request.external;

import com.trs.moye.base.common.annotaion.validation.NotRepeat;
import com.trs.moye.base.knowledgebase.entity.EntityColumn;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-11-20 17:55
 */
@Data
@NoArgsConstructor
public class ExternalUpdateDataRequest {

    @NotNull(message = "知识库类型不能为空")
    private KnowledgeBaseType type;

    @NotBlank(message = "实体名称不能为空")
    private String enName;

    @NotNull(message = "数据ID不能为空")
    private Integer dataId;

    @NotRepeat(message = "存在字段名重复", fields = "name:字段名")
    @Valid
    @NotEmpty(message = "字段和字段值列表不能为空")
    List<EntityColumn> fieldValueList;
}
