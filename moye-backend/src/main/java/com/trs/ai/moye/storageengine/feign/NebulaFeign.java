package com.trs.ai.moye.storageengine.feign;

import com.trs.ai.moye.common.config.OpenFeignConfig;
import com.trs.ai.moye.storageengine.request.NebulaAddTagEdgeRequest;
import com.trs.ai.moye.storageengine.request.NebulaDropTagEdgeRequest;
import com.trs.ai.moye.storageengine.request.NebulaUpdateTagEdgeRequest;
import com.trs.ai.moye.storageengine.request.nebula.AddModelTagEdgeFieldsRequest;
import com.trs.ai.moye.storageengine.request.nebula.DeleteModelTagEdgeFieldsRequest;
import com.trs.ai.moye.storageengine.request.nebula.UpdateModelTagEdgeFieldsRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 存储引擎nebula feign
 */
@FeignClient(name = "moye-storage-engine", path = "/storage/connection/nebula", contextId = "nebula", configuration = OpenFeignConfig.class)
public interface NebulaFeign {

    /**
     * 添加tag/edge
     *
     * @param request 请求体
     */
    @PostMapping(value = "/tag-edge")
    void addTagEdge(@RequestBody @Validated NebulaAddTagEdgeRequest request);

    /**
     * 删除tag/edge
     *
     * @param request 请求体
     */
    @DeleteMapping(value = "/tag-edge")
    void dropTagEdge(@RequestBody @Validated NebulaDropTagEdgeRequest request);

    /**
     * 修改tag/edge名称
     *
     * @param request 请求体
     */
    @PutMapping(value = "/tag-edge")
    void updateTagEdge(@RequestBody @Validated NebulaUpdateTagEdgeRequest request);

    /**
     * 增加tag/edge到数据标准、数据建模、物理库
     *
     * @param request 请求参数
     */
    @PostMapping("/tag-edge/fields")
    void addTagEdgeFields(@RequestBody @Validated AddModelTagEdgeFieldsRequest request);

    /**
     * 修改tag/edge到数据标准、数据建模、物理库
     *
     * @param request 请求参数
     */
    @PutMapping("/tag-edge/fields")
    void updateTagEdgeFields(@RequestBody @Validated UpdateModelTagEdgeFieldsRequest request);

    /**
     * 删除tag/edge到数据标准、数据建模、物理库
     *
     * @param request 请求参数
     */
    @DeleteMapping("/tag-edge/fields")
    void deleteTagEdgeFields(@RequestBody @Validated DeleteModelTagEdgeFieldsRequest request);
}
