package com.trs.ai.moye.knowledgebase.service;

import com.trs.moye.base.common.response.PageResponse;
import com.trs.ai.moye.knowledgebase.request.BaseDataBatchDeleteRequest;
import com.trs.ai.moye.knowledgebase.request.KnowledgeBaseDataPageListRequest;
import com.trs.ai.moye.knowledgebase.request.KnowledgeBaseDataRequest;
import java.util.Map;

/**
 * 实体业务接口
 *
 * <AUTHOR>
 * @since 2020/6/8 14:19
 */
public interface KnowledgeBaseDataService {

    /**
     * 添加知识库数据
     *
     * @param request 请求参数
     * @return 知识库数据id
     */
    int addKnowledgeBaseData(KnowledgeBaseDataRequest request);

    /**
     * 添加知识库数据
     *
     * @param dataId  知识库数据id
     * @param request 请求参数
     */
    void updateKnowledgeBaseData(Integer dataId, KnowledgeBaseDataRequest request);

    /**
     * 获取知识库数据
     *
     * @param dataId 知识库数据id
     * @param baseId 知识库id
     */
    void deleteKnowledgeBaseData(Integer dataId, Integer baseId);

    /**
     * 删除知识库数据
     *
     * @param request 请求参数
     */
    void batchDelete(BaseDataBatchDeleteRequest request);

    /**
     * 获取知识库数据分页列表
     *
     * @param request 请求参数
     * @return 知识库数据
     */
    PageResponse<Map<String, Object>> getKnowledgeBaseDataPageList(KnowledgeBaseDataPageListRequest request);
}
