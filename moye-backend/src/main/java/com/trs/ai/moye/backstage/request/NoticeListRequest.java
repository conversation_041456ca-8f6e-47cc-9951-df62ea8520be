package com.trs.ai.moye.backstage.request;

import com.trs.ai.moye.backstage.enums.NoticeSendConfInsideCreateType;
import com.trs.moye.base.common.request.BaseRequestParams;
import java.util.Objects;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.validation.annotation.Validated;

/**
 * 配置消息查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/31 14:29
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Validated
public class NoticeListRequest extends BaseRequestParams {

    /**
     * 消息类型id
     */
    private Integer messageTypeId;
    /**
     * 是否已读
     */
    private Boolean isRead;

    /**
     * 创建类型
     */
    private NoticeSendConfInsideCreateType createType;


    /**
     * 获取枚举
     *
     * @return {@link String }
     * <AUTHOR>
     * @since 2025/2/25 17:19
     */
    public String getCreateTypeName() {
        return Objects.isNull(createType) ? "" : createType.name();
    }
}
