package com.trs.ai.moye.backstage.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.backstage.entity.AuthCertificateKerberos;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 * @since 2024/10/18
 */
@Mapper
public interface AuthCertificateKerberosMapper extends BaseMapper<AuthCertificateKerberos> {

    /**
     * 返回根据名称相同当时id不同的行
     *
     * @param principal 认证名称
     * @param id        数据库主键id
     * @return 行数
     */
    Integer countByPrincipalAndExcludeId(@Param("principal") String principal, @Param("id") Integer id);

    /**
     * 分页查询
     *
     * @param principal 查询条件
     * @param page   分页信息
     * @return 分页数据
     */
    Page<AuthCertificateKerberos> pageAuthCertificateKerberosList(@Param("principal") String principal, Page<AuthCertificateKerberos> page);
}




