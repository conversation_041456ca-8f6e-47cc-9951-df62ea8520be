package com.trs.ai.moye.backstage.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *
 */
@TableName(value = "auth_certificate_kerberos")
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AuthCertificateKerberos extends AuditBaseEntity {

    /**
     * 用户名称
     */
    private String principal;

    /**
     * krb5配置文件路径
     */
    private String krb5Path;

    /**
     * 密钥文件地址
     */
    private String keytabPath;

    /**
     * 是否在使用
     */
    private Boolean inUse;


    /**
     * 构造函数
     *
     * @param principal  认证信息
     * @param krb5Path   krb5配置文件路径
     * @param keytabPath 密钥文件地址
     */
    public AuthCertificateKerberos(String principal, String krb5Path, String keytabPath) {
        this.principal = principal;
        this.krb5Path = krb5Path;
        this.keytabPath = keytabPath;
    }

}