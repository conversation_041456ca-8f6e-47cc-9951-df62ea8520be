package com.trs.ai.moye.knowledgebase.request.external;

import com.trs.moye.base.common.request.SortParams;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-11-25 15:32
 */
@Data
@NoArgsConstructor
public class ExternalAttrDataRequest {

    @NotNull(message = "知识库类型不能为空")
    private KnowledgeBaseType type;

    @NotBlank(message = "实体名称不能为空")
    private String enName;

    @NotBlank(message = "属性名不能为空")
    private String attrName;

    private String keyword;

    private String situation;

    @Valid
    private SortParams sortParams;
}
