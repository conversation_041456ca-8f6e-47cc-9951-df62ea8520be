package com.trs.ai.moye.backstage.help;

import com.trs.ai.moye.log.KafkaLogService;
import com.trs.moye.base.common.annotaion.enums.SignField;
import com.trs.moye.base.common.annotaion.processor.fieldsign.FieldSignProcessor;
import com.trs.moye.base.common.annotaion.processor.fieldsign.SignFieldReader;
import com.trs.moye.base.common.entity.FieldSignParseParam;
import com.trs.moye.base.common.enums.ResultType;
import com.trs.moye.base.common.help.container.DefaultSyncContainer;
import com.trs.moye.base.common.log.api.ApiLogTrace;
import com.trs.moye.base.common.log.api.ApiLogTracerUtils;
import com.trs.moye.base.common.log.api.ApiLogUtils;
import com.trs.moye.base.common.log.data.service.DataServiceLog;
import com.trs.moye.base.common.log.data.service.DataServiceLogUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.common.utils.SnowflakeIdUtil;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2025/4/17
 **/
@Order(1000)
@Slf4j
@Aspect
@Component
public class DataServiceLogAspect {

    private static final Supplier<Object> DEFAULT_SESSION_ID_SUPPLIER = () -> SnowflakeIdUtil.newId() + "";

    private final DefaultSyncContainer<ProceedingJoinPoint, Method, SignFieldReader> sessionIdReaderContainer =
        new DefaultSyncContainer<>(this::getInterceptMethod, this::parseSessionIdReader);

    @Resource
    private KafkaLogService kafkaLogService;

    /**
     * 定义切点为带有 @ApiLogSign 注解的方法
     */
    @Pointcut("@annotation(com.trs.moye.base.common.annotaion.DataServiceLogSign)")
    public void dataServiceLogPointcut() {
    }

    /**
     * 使用环绕通知处理切点
     *
     * @param joinPoint 切点对象
     * @return 处理结果或抛出异常
     * @throws Throwable 异常信息
     */
    @Around("dataServiceLogPointcut()")
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        if (ApiLogTracerUtils.containLocalTracer()) {
            return joinPoint.proceed();
        }
        DataServiceLog apiLog = createDataServiceLog(joinPoint);
        try {
            DataServiceLogUtils.set(apiLog);
            ApiLogTracerUtils.initLocalTracer(apiLog.getLogId());
            Object result = joinPoint.proceed();
            supplementSuccessEndInfo(result);
            return result;
        } catch (Exception e) {
            // 3. 异常处理：记录异常日志
            supplementErrorEndInfo(e);
            throw e;
        } finally {
            ApiLogUtils.remove();
            sendDataServiceLog(apiLog);
            List<ApiLogTrace> traceList = ApiLogTracerUtils.getTraceList();
            ApiLogTracerUtils.remove();
            sendApiLogTrace(traceList);
        }
    }

    private DataServiceLog createDataServiceLog(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        DataServiceLog apiLog = new DataServiceLog();
        apiLog.setLogId(SnowflakeIdUtil.newId());
        apiLog.setRequestTime(LocalDateTime.now());
        Object[] args = joinPoint.getArgs();
        apiLog.setRequestParameters(parseRequestParameters(method, args));
        return apiLog;
    }

    private void supplementSuccessEndInfo(Object result) {
        DataServiceLog apiLog = DataServiceLogUtils.get();
        apiLog.setResponseStatus(ResultType.SUCCESS);
        apiLog.setResponseDuration(Duration.between(apiLog.getRequestTime(), LocalDateTime.now()).toMillis());
    }

    private void supplementErrorEndInfo(Exception e) {
        DataServiceLog apiLog = DataServiceLogUtils.get();
        apiLog.setResponseStatus(ResultType.FAILURE);
        apiLog.setResponseDuration(Duration.between(apiLog.getRequestTime(), LocalDateTime.now()).toMillis());
    }

    private void sendDataServiceLog(DataServiceLog dataServiceLog) {
        try {
            kafkaLogService.sendDataServiceLog(dataServiceLog);
        } catch (Exception e) {
            log.error("发送API日志失败，日志信息：{}", JsonUtils.toJsonString(dataServiceLog), e);
        }
    }

    private void sendApiLogTrace(List<ApiLogTrace> traces) {
        try {
            kafkaLogService.sendApiLogTracers(traces);
        } catch (Exception e) {
            log.error("发送API-tracer日志失败，日志信息：{}", JsonUtils.toJsonString(traces), e);
        }
    }


    private Map<String, Object> parseRequestParameters(Method method, Object[] args) {
        Map<String, Object> parameters = new HashMap<>();
        ParameterNameDiscoverer nameDiscoverer = new DefaultParameterNameDiscoverer();
        String[] paramNames = nameDiscoverer.getParameterNames(method);
        Parameter[] params = method.getParameters();

        for (int i = 0; i < params.length; i++) {
            Parameter param = params[i];
            Object argValue = args[i];

            // 排除原生请求对象
            if (argValue instanceof HttpServletRequest) {
                continue;
            }

            // 1. 优先通过注解获取参数名
            String paramKey = null;

            // 处理 @PathVariable
            PathVariable pathVar = param.getAnnotation(PathVariable.class);
            if (pathVar != null) {
                paramKey = pathVar.value().isEmpty() ? Objects.requireNonNull(paramNames)[i] : pathVar.value();
            }

            // 处理 @RequestParam（虽然示例没有，但需要兼容）
            RequestParam requestParam = param.getAnnotation(RequestParam.class);
            if (requestParam != null) {
                paramKey =
                    requestParam.value().isEmpty() ? Objects.requireNonNull(paramNames)[i] : requestParam.value();
            }

            // 处理 @RequestBody
            RequestBody requestBody = param.getAnnotation(RequestBody.class);
            if (requestBody != null) {
                paramKey = Objects.requireNonNull(paramNames)[i]; // 直接使用变量名作为键
            }

            // 2. 兜底逻辑：使用反射获取的参数名
            if (paramKey == null) {
                paramKey = paramNames != null ? paramNames[i] : "param_" + i;
            }

            parameters.put(paramKey, argValue);
        }

        return parameters;
    }


    private Method getInterceptMethod(ProceedingJoinPoint joinPoint) {
        return ((MethodSignature) joinPoint.getSignature()).getMethod();
    }

    private SignFieldReader parseSessionIdReader(ProceedingJoinPoint joinPoint) {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        Object[] args = joinPoint.getArgs();
        FieldSignParseParam param = new FieldSignParseParam();
        param.setMethod(method);
        param.setArgs(args);
        param.setDefaultSignFieldValueSupplier(DEFAULT_SESSION_ID_SUPPLIER);
        param.setSignField(SignField.SESSION_ID);
        return FieldSignProcessor.parseSignFieldReader(param);
    }

}
