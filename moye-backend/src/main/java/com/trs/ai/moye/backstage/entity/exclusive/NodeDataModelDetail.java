package com.trs.ai.moye.backstage.entity.exclusive;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.trs.moye.base.common.response.IdNameResponse;
import java.util.Set;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 节点数据建模详情列表：引擎节点上的数据源
 *
 * <AUTHOR>
 * @since 2025-01-10 13:43
 */
@Data
@NoArgsConstructor
public class NodeDataModelDetail {

    /**
     * 节点（ip）
     */
    private String node;

    private Set<IdNameResponse> dataModels;

    /**
     * 数据建模数量
     *
     * @return 数据建模数量
     */
    @JsonProperty
    public int getDataModelCount() {
        return dataModels == null ? 0 : dataModels.size();
    }
}
