package com.trs.ai.moye.permission.service;

import com.trs.ai.moye.redis.starter.service.RedisService;
import com.trs.ai.moye.permission.properties.SecurityProperties;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * token仓库
 *
 * <AUTHOR>
 * @since 2024/11/8 16:45
 */
@Service
public class TokenStore {

    @Resource
    private RedisService redisService;

    @Resource
    private SecurityProperties securityProperties;

    private static final String PREFIX = "login-token:";


    /**
     * 根据user获取所有token
     *
     * @param account user account
     * @return token
     */
    public List<String> getTokenByUser(String account) {
        return redisService.getSetValue(getKey(account)).stream().map(Object::toString).toList();
    }

    /**
     * 根据user获取token
     *
     * @param account   user account
     * @param loginTime 登录时间
     * @return token
     */
    public String getTokenByUser(String account, long loginTime) {
        return (String) redisService.get(getKey(account, loginTime));
    }

    /**
     * 保存token
     *
     * @param account   账号名
     * @param loginTime 登录时间
     * @param token     token
     */
    public void saveToken(String account, long loginTime, String token) {
        long expireSeconds = securityProperties.getRedisTokenExpirationMillis() / 1000;
        redisService.set(getKey(account, loginTime), token, expireSeconds);
    }

    /**
     * 删除用户的所有token
     *
     * @param account 账户名
     */
    public void removeAllTokenByUser(String account) {
        redisService.delByPrefix(getKey(account) + ":");
    }

    /**
     * 删除用户的token
     *
     * @param account   账户名
     * @param loginTime 登录时间
     */
    public void removeTokenByUser(String account, long loginTime) {
        redisService.del(getKey(account, loginTime));
    }

    private String getKey(String account) {
        return PREFIX + account;
    }

    private String getKey(String account, long loginTime) {
        return getKey(account) + ":" + loginTime;
    }
}
