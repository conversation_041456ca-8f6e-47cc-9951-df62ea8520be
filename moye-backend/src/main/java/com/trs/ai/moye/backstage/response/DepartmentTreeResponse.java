package com.trs.ai.moye.backstage.response;


import com.trs.ai.moye.backstage.entity.DepartmentTree;
import com.trs.ai.moye.common.response.TreeBaseResponse;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 部门树
 *
 * <AUTHOR>
 * @since 2024/9/25 11:45
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DepartmentTreeResponse extends TreeBaseResponse {


    /**
     * 构造部门树前端响应
     *
     * @param departmentTree 部门树
     * <AUTHOR>
     * @since 2024/9/25 14:03
     */

    public DepartmentTreeResponse(DepartmentTree departmentTree) {
        super.setId(departmentTree.getId());
        super.setPid(departmentTree.getPid());
        super.setName(departmentTree.getName());
        List<DepartmentTreeResponse> children = departmentTree.getChildren().stream().map(DepartmentTreeResponse::new)
            .toList();
        this.setChildren(children);
    }
}