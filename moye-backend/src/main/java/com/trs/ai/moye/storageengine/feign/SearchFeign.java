package com.trs.ai.moye.storageengine.feign;

import com.trs.ai.moye.common.config.OpenFeignConfig;
import com.trs.ai.moye.data.model.response.indicator.IndicatorDataPreviewResponse;
import com.trs.ai.moye.storageengine.request.CodeSearchParams;
import com.trs.ai.moye.storageengine.request.ConditionSearchParams;
import com.trs.ai.moye.storageengine.response.StorageSearchResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 存储引擎搜索feign
 */
@FeignClient(name = "${data.service.application.name:moye-storage-engine}", path = "/storage/search", contextId = "search", configuration = OpenFeignConfig.class)
public interface SearchFeign {

    /**
     * 条件查询
     *
     * @param connectionId 数据库连接
     * @param tableName    表名
     * @param request      查询参数
     * @return 搜索结果
     */
    @PostMapping(value = "/condition")
    StorageSearchResponse conditionQuery(
        @RequestParam("connectionId") @NotNull(message = "连接id不能为空") Integer connectionId,
        @RequestParam("tableName") @NotBlank(message = "表名不能为空") String tableName,
        @RequestBody ConditionSearchParams request);

    /**
     * 视图的条件查询
     *
     * @param connectionId 数据库连接
     * @param sql          查询sql
     * @param request      查询参数
     * @return 搜索结果
     */
    @PostMapping(value = "/condition-sql")
    StorageSearchResponse conditionSqlQuery(
        @RequestParam("connectionId") @NotNull(message = "连接id不能为空") Integer connectionId,
        @RequestParam("sql") @NotBlank(message = "sql不能为空") String sql,
        @RequestBody ConditionSearchParams request);

    /**
     * 执行任意sql查询
     *
     * @param connectionId 连接id
     * @param tableName    表名
     * @param request      预览条件
     * @return 预览结果
     */
    @PostMapping(value = "/code")
    StorageSearchResponse codeQuery(
        @RequestParam("connectionId") @NotNull(message = "连接id不能为空") Integer connectionId,
        @RequestParam(value = "tableName", required = false) String tableName,
        @RequestBody CodeSearchParams request);

    /**
     * 代码查询 Nebula 原始数据
     *
     * @param connectionId 连接id
     * @param request      请求
     * @return {@link StorageSearchResponse }
     * <AUTHOR>
     * @since 2024/12/23 11:18:44
     */
    @PostMapping(value = "/code/nebula")
    StorageSearchResponse codeQueryNebulaRawData(
        @RequestParam("connectionId") @NotNull(message = "连接id不能为空") Integer connectionId,
        @RequestBody CodeSearchParams request);

    /**
     * 指标库预览（执行任意sql）
     *
     * @param connectionId 连接id
     * @param request      预览条件
     * @return 预览结果
     */
    @PostMapping(value = "/indicator/preview")
    IndicatorDataPreviewResponse indicatorPreview(
        @RequestParam("connectionId") @NotNull(message = "连接id不能为空") Integer connectionId,
        @RequestBody CodeSearchParams request);
}
