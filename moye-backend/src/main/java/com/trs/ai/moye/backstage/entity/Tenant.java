package com.trs.ai.moye.backstage.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 租户
 *
 * <AUTHOR>
 * @since 2022/12/22 15:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Tenant implements Serializable {
    /**
     * 租户id
     */
    private Integer id;

    /**
     * 租户名称
     */
    private String name;

    /**
     * 租户描述
     */
    private String description;

    /**
     * 创建人
     */
    private Integer createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最近更新人
     */
    private Integer updateUser;

    /**
     * 最近更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;

}