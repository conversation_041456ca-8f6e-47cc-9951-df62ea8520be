package com.trs.ai.moye.knowledgebase.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 监听
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/2/8 17:37
 */
public class ReadDataListener extends AnalysisEventListener<Map<Integer, String>> {

    private static final List<String> HEAD = new LinkedList<>();

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        // 空实现即可
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 空实现即可
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        HEAD.clear();
        for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
            HEAD.add(entry.getValue());
        }
    }

    /**
     * 校验表头
     *
     * @return 表头
     */
    public static List<String> checkHead() {
        return HEAD;
    }
}
