package com.trs.ai.moye.backstage.request;

import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * 更新站内推送配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/31 10:39
 **/
@Data
@Validated
public class UpdateInsideNoticeRequest {

        @NotNull(message = "id不能为空")
        private Integer id;

        @NotBlank(message = "策略名称不能为空")
        private String name;
        /**
         * 接收角色id集合
         */
        private List<Integer> notifyRoleIds;
        /**
         * 接收用户id集合
         */
        private List<Integer> notifyUserIds;
        /**
         * 接收消息类型id集合
         */
        @NotEmpty(message = "messageTypeIds不能为空")
        private List<Integer> messageTypeIds;
        /**
         * 数据范围id集合
         */

        private List<Integer> businessScopeIds;
        /**
         * 是否启用 0:禁用 1:启用
         */
        private boolean enable;
}
