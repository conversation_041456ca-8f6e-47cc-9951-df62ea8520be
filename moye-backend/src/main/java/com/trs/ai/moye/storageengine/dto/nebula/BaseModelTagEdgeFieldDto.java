package com.trs.ai.moye.storageengine.dto.nebula;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-01-20 10:44
 */
@Data
@NoArgsConstructor
public class BaseModelTagEdgeFieldDto {

    @NotBlank(message = "英文名称不能为空")
    @Size(max = 50, message = "英文名称长度不能超过50")
    private String enName;

}
