package com.trs.ai.moye.permission.service;

import javax.annotation.Resource;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;

/**
 * Bcrypt操作
 *
 * <AUTHOR>
 * @since 2024/11/12 14:58
 */
@Component
public class BcryptService {

    @Resource
    private BCryptPasswordEncoder passwordEncoder;

    /**
     * 生成bcrypt加密的密码
     *
     * @param originPassword 原密码
     * @return 加密密码
     */
    public String getBcryptPassword(String originPassword) {
        return passwordEncoder.encode(originPassword);
    }

}
