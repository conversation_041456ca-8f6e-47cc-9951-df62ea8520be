package com.trs.ai.moye.backstage.init;

import com.trs.ai.moye.backstage.service.impl.DataModelLayerLabelProvider;
import com.trs.ai.moye.init.async.AsyncInitialize;
import com.trs.moye.base.common.enums.ModelLayer;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 数据建模分层配置初始化
 *
 * <AUTHOR>
 * @since 2025/04/15 10:06:57
 */
@Slf4j
@Component
public class ModelLayerConfigInitialize implements AsyncInitialize {

    @Resource
    private DataModelLayerLabelProvider labelProvider;

    @Override
    public void initialize() {
        log.info("开始初始化数据建模分层配置...");
        try {
            ModelLayer.setLabelProvider(labelProvider);
        } catch (Exception e) {
            log.error("数据建模分层配置初始化失败", e);
        }
    }
}