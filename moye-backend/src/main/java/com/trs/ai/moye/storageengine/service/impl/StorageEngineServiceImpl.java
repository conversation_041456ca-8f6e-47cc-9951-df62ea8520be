package com.trs.ai.moye.storageengine.service.impl;


import com.trs.ai.moye.common.utils.BizUtils;
import com.trs.ai.moye.data.connection.request.ApiGetTreeRequest;
import com.trs.ai.moye.data.connection.request.TestConnectionRequest;
import com.trs.ai.moye.data.connection.response.ApiUrlTreeResponse;
import com.trs.ai.moye.data.connection.response.TableInfoResponse;
import com.trs.ai.moye.data.model.entity.StorageTask;
import com.trs.ai.moye.data.model.response.StorageEngineResponse;
import com.trs.ai.moye.monitor.dao.DataConnectionHealthRecordMapper;
import com.trs.ai.moye.monitor.entity.DataConnectionHealthRecord;
import com.trs.ai.moye.monitor.entity.DataConnectionNoticeEvent;
import com.trs.ai.moye.monitor.enums.DetectionType;
import com.trs.ai.moye.monitor.feign.MonitorCenterService;
import com.trs.ai.moye.out.request.NebulaStorageRequest;
import com.trs.ai.moye.storageengine.feign.ApiConnectionFeign;
import com.trs.ai.moye.storageengine.feign.DatabaseConnectionFeign;
import com.trs.ai.moye.storageengine.feign.FileConnectionFeign;
import com.trs.ai.moye.storageengine.feign.JobFeign;
import com.trs.ai.moye.storageengine.feign.MqConnectionFeign;
import com.trs.ai.moye.storageengine.feign.NebulaFeign;
import com.trs.ai.moye.storageengine.feign.SearchFeign;
import com.trs.ai.moye.storageengine.request.CodeSearchParams;
import com.trs.ai.moye.storageengine.request.ConditionSearchParams;
import com.trs.ai.moye.storageengine.request.CreateTableRequest;
import com.trs.ai.moye.storageengine.request.FileColumnRequest;
import com.trs.ai.moye.storageengine.request.FileTableRequest;
import com.trs.ai.moye.storageengine.request.NebulaAddTagEdgeRequest;
import com.trs.ai.moye.storageengine.request.NebulaDropTagEdgeRequest;
import com.trs.ai.moye.storageengine.request.NebulaUpdateTagEdgeRequest;
import com.trs.ai.moye.storageengine.request.SubmitJobRequest;
import com.trs.ai.moye.storageengine.response.ConnectionTestDetailResponse;
import com.trs.ai.moye.storageengine.response.DatabaseMetadataResponse;
import com.trs.ai.moye.storageengine.response.DirectoryAndFileResponse;
import com.trs.ai.moye.storageengine.response.RestfulResponse;
import com.trs.ai.moye.storageengine.response.StorageSearchResponse;
import com.trs.ai.moye.storageengine.response.alltable.TableResponse;
import com.trs.ai.moye.storageengine.service.StorageEngineService;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.data.model.dao.BusinessCategoryMapper;
import com.trs.moye.base.data.model.entity.BusinessCategory;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.field.mapping.ColumnResponse;
import com.trs.moye.base.data.model.field.mapping.FieldMappingResponse;
import com.trs.moye.base.data.model.field.mapping.MoyeFieldResponse;
import com.trs.moye.base.data.source.setting.http.HttpDataSourceSettings;
import com.trs.moye.base.data.storage.nebula.ChangeFieldNameDto;
import com.trs.moye.base.data.storage.setting.DataStorageSettings;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-09-13 17:29
 */
@Slf4j
@Service
public class StorageEngineServiceImpl implements StorageEngineService {

    private static final String MSG_CONNECTION_NOT_EXIST = "主键为【%s】的连接不存在";

    @Resource
    private DataConnectionMapper dataConnectionMapper;

    @Resource
    private BusinessCategoryMapper businessCategoryMapper;

    @Resource
    private DataConnectionHealthRecordMapper dataConnectionHealthRecordMapper;

    @Resource
    private DatabaseConnectionFeign databaseConnectionFeign;

    @Resource
    private FileConnectionFeign fileConnectionFeign;

    @Resource
    private ApiConnectionFeign apiConnectionFeign;

    @Resource
    private MqConnectionFeign mqConnectionFeign;

    @Resource
    private SearchFeign searchFeign;

    @Resource
    private JobFeign jobFeign;

    @Resource
    private NebulaFeign nebulaFeign;

    @Resource
    private MonitorCenterService monitorCenterService;


    @Override
    public boolean testConnection(TestConnectionRequest request) {
        return getConnectionTestResultWithDetail(request).isSuccess();
    }


    /**
     * 测试连接并记录测试结果以及推送告警
     * <p>
     *
     * @param request       请求参数
     * @param detectionType 检测类型
     * @param userName      用户名
     * @return success与失败原因
     */
    @Override
    public ConnectionTestDetailResponse testConnectionAndRecord(TestConnectionRequest request,
        DetectionType detectionType, String userName) {
        // 测试连接
        ConnectionTestDetailResponse result = getConnectionTestResultWithDetail(request);

        // 需要更新数据库状态
        if (request.isUpdateRequest()) {
            dataConnectionMapper.updateConnectionStatusById(request.getConnectionId(), result.isSuccess());

            try {
                DataConnectionHealthRecord healthRecord = new DataConnectionHealthRecord(request.getConnectionId(), !result.isSuccess(),
                    result.getErrorMessage(), LocalDateTime.now(), detectionType.getDescription(), userName);
                dataConnectionHealthRecordMapper.insert(healthRecord);
            } catch (Exception e) {
                // clickhouse异常，不影响业务流程
                log.error("连接测试结果 记录到clickhouse发生异常", e);
            }

            // 推送告警
            DataConnection dataConnection = dataConnectionMapper.selectById(request.getConnectionId());
            sendNotice(dataConnection, result);
        }
        return result;
    }

    private ConnectionTestDetailResponse getConnectionTestResultWithDetail(TestConnectionRequest request) {
        return switch (request.getConnectionCategory()) {
            case DATA_BASE -> databaseConnectionFeign.testConnectionWithDetail(request);
            case FILE -> fileConnectionFeign.testConnectionWithDetail(request);
            case API -> apiConnectionFeign.testConnectionWithDetail(request);
            case MQ -> mqConnectionFeign.testConnectionWithDetail(request);
        };
    }

    private void sendNotice(DataConnection dataConnection, ConnectionTestDetailResponse result) {
        if (result.isSuccess()) {
            return;
        }

        try {
            monitorCenterService.sendDataConnectionMessage(DataConnectionNoticeEvent.from(dataConnection, result));
        } catch (Exception e) {
            log.error("连接测试结果 推送消息到消息中心发生异常", e);
        }
    }

    /**
     * 获取数据库metadata
     *
     * @param connectionId 连接id
     * @return metadata
     */
    @Override
    public DatabaseMetadataResponse getMetadata(Integer connectionId) {
        return databaseConnectionFeign.getDatabaseMetadata(connectionId);
    }

    @Override
    public List<TableResponse> getDbTables(Integer connectionId) {
        List<TableResponse> responseList = databaseConnectionFeign.getTableList(connectionId);
        // 表备注为空则使用表名作为备注
        responseList.forEach(tableResponse -> {
            if (ObjectUtils.isEmpty(tableResponse.getTableComment())) {
                tableResponse.setTableComment(tableResponse.getTableName());
            }
        });
        return responseList;
    }

    @Override
    public FieldMappingResponse getDbTableFields(Integer connectionId, String tableName) {
        DataConnection connection = dataConnectionMapper.selectById(connectionId);
        AssertUtils.notEmpty(connection, MSG_CONNECTION_NOT_EXIST, connectionId);
        return databaseConnectionFeign.getTableFields(connectionId, tableName);
    }

    @Override
    public DirectoryAndFileResponse getFileTables(Integer connectionId, FileTableRequest request) {
        return fileConnectionFeign.getTableList(connectionId, request);
    }

    @Override
    public FieldMappingResponse getFileTableFields(Integer connectionId, FileColumnRequest request) {
        DataConnection connection = dataConnectionMapper.selectById(connectionId);
        AssertUtils.notEmpty(connection, MSG_CONNECTION_NOT_EXIST, connectionId);
        return fileConnectionFeign.getTableFields(connectionId, request);
    }

    @Override
    public List<TableResponse> getMqTables(Integer connectionId) {
        return mqConnectionFeign.getAllTable(connectionId);
    }

    @Override
    public FieldMappingResponse getMqTableFields(Integer connectionId, String tableName) {
        DataConnection connection = dataConnectionMapper.selectById(connectionId);
        AssertUtils.notEmpty(connection, MSG_CONNECTION_NOT_EXIST, connectionId);
        return mqConnectionFeign.getTableFields(connectionId, tableName);
    }

    @Override
    public StorageEngineResponse createTable(Integer connectionId, Integer dataModelId, Integer dataStorageId,
        DataStorageSettings settings) {
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        AssertUtils.notEmpty(dataConnection, MSG_CONNECTION_NOT_EXIST, connectionId);
        DataSourceCategory category = dataConnection.getConnectionType().getCategory();
        CreateTableRequest request = new CreateTableRequest(dataModelId, dataStorageId, settings);
        switch (category) {
            case DATA_BASE -> {
                return databaseConnectionFeign.createTable(connectionId, request);
            }
            case MQ -> {
                return mqConnectionFeign.createTable(connectionId, request);
            }
            default -> throw new BizException("连接分类【%s】不支持建表", category);
        }
    }

    @Override
    public StorageSearchResponse conditionQuery(Integer connectionId, String tableName, ConditionSearchParams request) {
        return searchFeign.conditionQuery(connectionId, tableName, request);
    }

    @Override
    public StorageSearchResponse conditionSqlQuery(Integer connectionId, String sql, ConditionSearchParams request) {
        return searchFeign.conditionSqlQuery(connectionId, sql, request);
    }

    @Override
    public StorageSearchResponse codeQuery(Integer connectionId, String tableName, CodeSearchParams request) {
        return searchFeign.codeQuery(connectionId, tableName, request);
    }

    @Override
    public StorageSearchResponse codeQueryNebulaRawData(Integer connectionId, CodeSearchParams request) {
        return searchFeign.codeQueryNebulaRawData(connectionId, request);
    }

    @Override
    public RestfulResponse submitJob(SubmitJobRequest request) {
        return jobFeign.submitJob(request);
    }

    @Override
    public void stopJob(Integer dataModelId) {
        jobFeign.stopJob(dataModelId);
    }

    @Override
    public void createStorageTopic(Integer dataModelId) {
        jobFeign.createStorageOperatorTopic(dataModelId);
    }

    @Override
    public StorageTask updateJobStatus(String storageJobId) {
        return jobFeign.updateJobStatus(storageJobId);
    }

    @Override
    public void stopJobSingle(String storageJobId) {
        jobFeign.stopJobSingle(storageJobId);
    }

    @Override
    public ApiUrlTreeResponse getUrlResponseTree(ApiGetTreeRequest request) {
        Integer connectionId = request.getConnectionId();
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        if (Objects.isNull(dataConnection)) {
            throw new BizException("传入连接信息id为:{ " + connectionId + "}, 无法查询到连接信息!");
        }
        //调用存储引擎的接口
        HttpDataSourceSettings httpConnectionInfo = request.getHttpDataSourceSettings();
        Map<String, Object> map = requestApiUrlResponse(httpConnectionInfo, dataConnection);
        //构造返回类
        log.info("获取到的字段返回: {}", map);
        return buildResponse(request, httpConnectionInfo, map);
    }

    private Map<String, Object> requestApiUrlResponse(HttpDataSourceSettings apiConnectionInfo,
        DataConnection dataConnection)
        throws BizException {
        //拼接完成的路径
        apiConnectionInfo.buildTrueUrl(dataConnection);
        Map<String, Object> returnObject = apiConnectionFeign.httpField(apiConnectionInfo);
        if (Objects.isNull(returnObject)) {
            if (apiConnectionInfo.authorizeFlag()) {
                throw new BizException("{" + apiConnectionInfo.getAuthorize().getUrl() + "}接口未查询到数据!");
            }
            throw new BizException("{" + apiConnectionInfo.getBase().getUrl() + "}接口未查询到数据!");
        }
        return returnObject;
    }

    private ApiUrlTreeResponse buildResponse(ApiGetTreeRequest request,
        HttpDataSourceSettings httpConnectionInfo, Map<String, Object> map)
        throws BizException {
        ApiUrlTreeResponse response = new ApiUrlTreeResponse();
        String data = JsonUtils.toJsonString(map);
        log.info("转换成String之后的data:{}", data);
        boolean authorizeFlag = httpConnectionInfo.authorizeFlag();
        //字段树
        response.setTree(BizUtils.extractJsonFields(data, authorizeFlag));
        if (Objects.nonNull(httpConnectionInfo.getRequestParams())) {
            TableInfoResponse odsTableInfoResponse = TableInfoResponse.getTableResponse(request.getConnectionId(),
                data);
            BusinessCategory businessCategory = businessCategoryMapper.selectById(request.getBusinessCategoryId());
            String url = httpConnectionInfo.getBase().getUrl();
            if (Objects.isNull(url)) {
                throw new BizException("接口地址为空! 无法查询字段信息");
            }
            String lastWord = url.substring(url.lastIndexOf("/") + 1);
            odsTableInfoResponse.setModelEnName("ods_" + businessCategory.getEnName() + "_" + lastWord);
            response.setTableInfoResponse(odsTableInfoResponse);
        }
        return response;
    }

    @Override
    public List<MoyeFieldResponse> getMoyeFieldsByStorageEngine(DataConnection dataConnection, String tableName,
        FileColumnRequest fileColumnRequest, Integer dataModelId) {
        DataSourceCategory category = dataConnection.getConnectionType().getCategory();
        Integer connectionId = dataConnection.getId();
        FieldMappingResponse fieldMappingResponse;
        switch (category) {
            case DATA_BASE -> fieldMappingResponse = getDbTableFields(connectionId,
                tableName);
            case FILE -> fieldMappingResponse = getFileTableFields(connectionId,
                fileColumnRequest);
            case MQ -> fieldMappingResponse = getMqTableFields(connectionId,
                tableName);
            case API -> {
                List<ColumnResponse> tableFields = apiConnectionFeign.getTableFields(connectionId, dataModelId);
                List<MoyeFieldResponse> fieldResponses = tableFields.stream()
                    .map(BizUtils::convertToMoyeFieldResponse).toList();
                fieldMappingResponse = new FieldMappingResponse(tableFields, fieldResponses);
            }
            default -> throw new BizException("数据连接【%s】是【%s】连接分类，暂不支持同步【%s】连接分类的字段",
                dataConnection.getName(),
                category, category);
        }
        if (fieldMappingResponse == null || Objects.isNull(fieldMappingResponse.getModelFields())) {
            return new ArrayList<>();
        }
        return fieldMappingResponse.getModelFields();
    }

    @Override
    public void nebulaStorage(NebulaStorageRequest request) {
        databaseConnectionFeign.nebulaStorage(request);
    }

    @Override
    public boolean nebulaDataTransfer(Integer sourceConnectionId, Integer targetConnectionId) {
        return databaseConnectionFeign.nebulaDataTransfer(sourceConnectionId, targetConnectionId);
    }


    /**
     * 增加tag/edge物理库
     *
     * @param connectionIds 连接id
     * @param fields        字段
     */
    @Override
    public void addTagEdge(List<Integer> connectionIds, List<DataModelField> fields) {
        nebulaFeign.addTagEdge(new NebulaAddTagEdgeRequest(connectionIds, fields));
    }

    /**
     * 删除tag/edge物理库
     *
     * @param connectionIds 连接id
     * @param fields        字段
     */
    @Override
    public void deleteTagEdge(List<Integer> connectionIds, List<DataModelField> fields) {
        nebulaFeign.dropTagEdge(new NebulaDropTagEdgeRequest(connectionIds, fields));
    }

    /**
     * 修改tag/edge名称
     *
     * @param connectionIds 连接id
     * @param fields        字段
     */
    @Override
    public void changeTagEdgeName(List<Integer> connectionIds, List<ChangeFieldNameDto> fields) {
        nebulaFeign.updateTagEdge(new NebulaUpdateTagEdgeRequest(connectionIds, fields));
    }
}