package com.trs.ai.moye.backstage.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 消息推送记录
 */
@Data
@TableName(value = "t_message_push_records")
public class MessagePushRecord {

    /**
     * 消息类型名称
     */
    private String messageType;

    /**
     * 消息子类型名称
     */
    private String messageSubtype;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 推送配置id
     */
    private Integer pushConfId;

    /**
     * 消息推送类型名称
     */
    private String pushType;

    /**
     * 消息发送时间
     */
    private LocalDateTime pushTime;

    /**
     * 推送地址
     */
    private String pushAddress;

    /**
     * 推送对象-部门
     */
    private List<Integer> pushDepts;

    /**
     * 推送对象-角色
     */
    private List<Integer> pushRoles;

    /**
     * 推送对象-用户
     */
    private List<Integer> pushUsers;

    /**
     * 是否推送成功
     */
    private Boolean isSuccess;

    /**
     * 失败原因
     */
    private String errorMsg;

    /**
     * 记录入库时间
     */
    private LocalDateTime storageTime;
}
