package com.trs.ai.moye.backstage.response.exclusive;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-01-13 20:31
 */
@Data
@NoArgsConstructor
public class ExclusiveBaseResponse {

    /**
     * 实际专属节点总数
     */
    private int actualExclusiveNodeCount = 0;

    /**
     * 实际通用节点总数
     */
    private int actualCommonNodeCount = 0;

    /**
     * 实际节点总数
     *
     * @return 实际节点总数
     */
    @JsonProperty
    public int getActualNodeCount() {
        return actualCommonNodeCount + actualExclusiveNodeCount;
    }
}
