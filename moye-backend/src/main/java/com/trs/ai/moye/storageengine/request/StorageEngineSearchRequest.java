package com.trs.ai.moye.storageengine.request;

import com.trs.ai.moye.data.service.enums.ServiceConfigType;
import com.trs.moye.base.common.request.PageParams;
import java.util.List;
import lombok.Data;

/**
 * 查询请求
 *
 * <AUTHOR>
 */
@Data
public class StorageEngineSearchRequest {

    /**
     * 分页参数
     */
    private PageParams pageParams;

    /**
     * 操作类型
     */
    private ServiceConfigType operationType = ServiceConfigType.QUERY;

    /**
     * 数据建模id
     */
    private List<Integer> dataModelIds;
}
