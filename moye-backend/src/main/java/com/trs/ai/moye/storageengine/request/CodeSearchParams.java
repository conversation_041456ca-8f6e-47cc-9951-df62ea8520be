package com.trs.ai.moye.storageengine.request;

import com.trs.moye.base.common.request.PageParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 代码模式查询参数
 *
 * <AUTHOR>
 * @since 2024/10/18 11:24:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CodeSearchParams extends StorageEngineSearchRequest {

    /**
     * 代码
     */
    private String code;


    private Long logId;


    /**
     * 生成CodeSearchParams
     *
     * @param sql sql
     * @return CodeSearchParams
     */
    public static CodeSearchParams fromSql(String sql) {
        CodeSearchParams params = new CodeSearchParams();
        params.setCode(sql);
        params.setPageParams(new PageParams(1, 100));
        return params;
    }
}
