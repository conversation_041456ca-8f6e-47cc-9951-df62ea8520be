package com.trs.ai.moye.backstage.service;

import com.trs.ai.moye.backstage.entity.User;
import com.trs.ai.moye.backstage.entity.UserCheckRepeatRequest;
import com.trs.ai.moye.backstage.request.UserListRequest;
import com.trs.ai.moye.backstage.request.UserRequest;
import com.trs.ai.moye.backstage.response.UserDetailResponse;
import com.trs.ai.moye.backstage.response.UserInfoResponse;
import com.trs.moye.base.common.response.PageResponse;

/**
 * 用户服务类
 *
 * <AUTHOR>
 * @since 2024-09-25 11:20
 */
public interface UserService {

    /**
     * 用户信息
     *
     * @param user 用户
     * @return 用户信息
     */
    UserInfoResponse userInfo(User user);

    /**
     * 添加用户
     *
     * @param request 请求参数
     * @return {@link int} 用户id
     * <AUTHOR>
     * @since 2024/9/25 11:43
     */
    int addUser(UserRequest request);

    /***
     * 更新用户
     *
     * @param    id 用户id
     * @param    request   请求参数
     * <AUTHOR>
     * @since 2024/9/25 15:51
     */
    void updateUser(Integer id, UserRequest request);

    /**
     * 删除用户
     *
     * @param id 用户主键
     */
    void deleteUser(Integer id);

    /**
     * 检查用户重复
     *
     * @param id      用户id
     * @param request 请求参数
     * @return 重复返回true，否则返回false
     */
    boolean checkUserRepeat(Integer id, UserCheckRepeatRequest request);

    /**
     * 用户详情
     *
     * @param id 用户主键
     * @return 用户详情信息
     */
    UserDetailResponse userDetail(Integer id);

    /**
     * 启用用户
     *
     * @param id 用户id
     */
    void enableUser(Integer id);

    /**
     * 停用用户
     *
     * @param id 用户is
     */
    void disableUser(Integer id);

    /**
     * 重置密码
     *
     * @param id       用户id
     * @param password 密码
     */
    void resetPassword(Integer id, String password);

    /**
     * 用户列表
     *
     * @param request 请求参数
     * @return 用户分页信息
     */
    PageResponse<UserDetailResponse> userPageList(UserListRequest request);

    /**
     * 根据account查询用户信息（使用缓存）
     *
     * @param account 账户
     * @return user
     */
    User getUserByAccountCacheable(String account);

    /**
     * 删除user缓存
     *
     * @param account 用户账号
     */
    void deleteUserCache(String account);

    /**
     * 查询用户信息，同时更新缓存
     *
     * @param account 账号
     * @return user
     */
    User getUserByAccountUpdateCache(String account);
}
