package com.trs.ai.moye.backstage.response;

import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 角色列表
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/6/22 15:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoleResponseChild {

    private Integer id;

    private String name;

    private Timestamp updateTime;

    private String updateBy;
}
