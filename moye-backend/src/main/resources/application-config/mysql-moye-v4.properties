spring.datasource.dynamic.primary=mysql
spring.datasource.dynamic.datasource.mysql.url=*******************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.mysql.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.mysql.username=root
spring.datasource.dynamic.datasource.mysql.password=!QAZ2wsx1234
spring.datasource.dynamic.datasource.mysql.lazy=true
spring.datasource.dynamic.datasource.mysql.druid.max-wait=100000
spring.datasource.dynamic.datasource.mysql.druid.validation-query=select 1
spring.datasource.dynamic.datasource.mysql.druid.validation-query-timeout=200
spring.datasource.dynamic.datasource.mysql.druid.initial-size=5
spring.datasource.dynamic.datasource.mysql.druid.max-active=5
spring.datasource.dynamic.datasource.mysql.druid.min-idle=5