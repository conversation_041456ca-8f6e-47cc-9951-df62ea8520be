# Kafka 基础连接配置
kafka.log.server.bootstrap-servers=kafka-svc:9092

# Admin 客户端配置（Map 格式，支持特殊字符键名）
kafka.log.server.admin-config.request.timeout.ms=30000

# Producer 生产者配置（如序列化器、重试策略等）
kafka.log.server.produce-config.key.serializer=org.apache.kafka.common.serialization.StringSerializer
kafka.log.server.produce-config.value.serializer=org.apache.kafka.common.serialization.StringSerializer
kafka.log.server.produce-config.retries=3

# Topic 配置列表（支持多个 Topic）
kafka.log.server.topic-configs[0].id=API_LOG
kafka.log.server.topic-configs[0].name=moye-api-log-topic
kafka.log.server.topic-configs[0].partitions=20
kafka.log.server.topic-configs[0].replication-factor=1
kafka.log.server.topic-configs[0].msg-retention-millis=259200000

kafka.log.server.topic-configs[1].id=API_LOG_TRACER
kafka.log.server.topic-configs[1].name=moye-api-log-trace-topic
kafka.log.server.topic-configs[1].partitions=20
kafka.log.server.topic-configs[1].replication-factor=1
kafka.log.server.topic-configs[1].msg-retention-millis=259200000

kafka.log.server.topic-configs[2].id=OPERATE_LOG
kafka.log.server.topic-configs[2].name=moye-operate-log-topic
kafka.log.server.topic-configs[2].partitions=20
kafka.log.server.topic-configs[2].replication-factor=1
kafka.log.server.topic-configs[2].msg-retention-millis=259200000
