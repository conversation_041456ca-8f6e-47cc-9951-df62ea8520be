#nlp 词云
nlp-service.word-cloud-url=http://trs-nlp-event-svc:8234/tianyuan/nlp/word_cloud
#nlp status
nlp-service.keyword-extract-url=http://trs-nlp-event-svc:8234/tianyuan/nlp/keyword_extract
#nlp s接受事件状态信息
nlp-service.event-status-url=http://trs-nlp-event-svc:8234/tianyuan/nlp/event_status
#nlp 相似度计算
nlp-service.similar-event-url=http://trs-nlp-event-svc:8234/tianyuan/nlp/similar_event
#nlp 接收事件消息
nlp-service.receive-event-message-url=http://192.168.200.192:8234/tianyuan/nlp/receive_event_message
#nlp 实体识别与关系抽取自动标记
nlp-service.auto-mark-url=http://192.168.200.192:9527/tianyuan/nlp/entity_relation_predict
#nlp 向量化
nlp-service.word-embedding-url=http://192.168.210.28:7000/nlp/embedding
#nlp 向量化（批量）
nlp-service.word-embedding-batch-url=http://192.168.210.29:8001/nlp/embedding/batch

#nlp cws
nlp-service.cws=http://trs-nlp-cws-svc:8800/nlp/cws/event
#nlp 情感识别
nlp-service.emotion=http://trs-nlp-emotion-classify-svc:8083/nlp/emotion
#nlp 垃圾文
nlp-service.spam=http://nlp-spam-classify-svc:8903/nlp/spam
