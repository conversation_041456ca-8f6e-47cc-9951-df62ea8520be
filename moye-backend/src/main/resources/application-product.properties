# 应用名称
spring.application.name=moye
#
spring.cloud.nacos.server-addr=nacos-svc:8848
spring.cloud.nacos.username=${TRS_MOYE_NACOS_USER_NAME}
spring.cloud.nacos.password=${TRS_MOYE_NACOS_PASSWORD}
spring.cloud.nacos.config.namespace=moye
spring.cloud.nacos.discovery.namespace=${spring.cloud.nacos.config.namespace}
logging.level.com.alibaba.cloud.nacos=debug
#
spring.config.import[0]=nacos:moye.properties
spring.config.import[1]=nacos:mysql-moye-v4.properties?increment-config-merge=true
spring.config.import[2]=nacos:clickhouse.properties?increment-config-merge=true
spring.config.import[3]=nacos:xxl-job.properties?increment-config-merge=true
spring.config.import[4]=nacos:redis.properties?increment-config-merge=true
spring.config.import[5]=nacos:mysql-moye-v4-dynamic.properties?increment-config-merge=true
spring.config.import[6]=nacos:minio.properties?increment-config-merge=true
spring.config.import[7]=nacos:kafka.properties?increment-config-merge=true
spring.config.import[8]=nacos:kafka-log-server.properties?increment-config-merge=true
spring.config.import[9]=nacos:ability-center.properties?increment-config-merge=true
spring.config.import[10]=nacos:llm.properties?increment-config-merge=true
spring.config.import[11]=nacos:nlp-service.properties?increment-config-merge=true