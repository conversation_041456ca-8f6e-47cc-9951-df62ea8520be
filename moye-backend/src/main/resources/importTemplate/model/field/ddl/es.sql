{"test1": {"mappings": {"properties": {"dataList": {"type": "keyword"}, "doc": {"properties": {"test_field5": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "field1": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "field2": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "g_adp": {"type": "keyword"}, "g_ch_key": {"type": "keyword"}, "hbase_table": {"type": "text"}, "recordId": {"type": "keyword"}, "test_field2": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "test_field3": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "test_field4": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "test_field5": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}}}