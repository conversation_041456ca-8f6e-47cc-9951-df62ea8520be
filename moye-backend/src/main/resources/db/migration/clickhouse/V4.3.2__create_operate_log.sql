CREATE TABLE IF NOT EXISTS  operate_log
(
    `id`            Int64    COMMENT '主键id',
    `user_id`       Nullable(Int32)     COMMENT '用户id',
    `user_name`     Nullable(String)    COMMENT '用户名称',
    `operate_time`  DateTime(3)  COMMENT '操作时间‌',
    `ip`            Nullable(String)    COMMENT '操作ip',
    `module`        Nullable(String)    COMMENT '模块（枚举）',
    `operate_type`  Nullable(String)    COMMENT '操作类型（枚举）',
    `api_name`      Nullable(String)    COMMENT 'api名称‌',
    `api_path`      Nullable(String)    COMMENT 'api路径‌',
    `operate_result` Nullable(String)    COMMENT '操作结果（枚举）',
    `details`       Nullable(String)    COMMENT '操作详情（JSON键值对）',
    `response_duration` Nullable(Int64) COMMENT '响应时长（毫秒）'
)
ENGINE = MergeTree()
    PARTITION BY toYYYYMMDD(`operate_time`)
    PRIMARY KEY `id`
    ORDER BY (`id`, `operate_time`)
    SETTINGS index_granularity = 8192;


CREATE TABLE IF NOT EXISTS  operate_log_kafka_engine
(
    `id`            Int64,
    `userId`        Nullable(Int32),
    `userName`      Nullable(String),
    `operateTime`   DateTime(3),
    `ip`            Nullable(String),
    `module`        Nullable(String),
    `operateType`   Nullable(String),
    `apiName`       Nullable(String),
    `apiPath`       Nullable(String),
    `operateResult` Nullable(String),
    `details`       Nullable(String),
    `responseDuration` Nullable(Int64)
)
ENGINE = Kafka
SETTINGS
    kafka_broker_list = 'kafka-svc:9092',
    kafka_topic_list = 'moye-operate-log-topic',
    kafka_group_name = 'moye-operate-log-group',
    kafka_format = 'JSONEachRow',
    kafka_skip_broken_messages = 20000,
    kafka_num_consumers = 4;


CREATE MATERIALIZED VIEW IF NOT EXISTS operate_log_view TO operate_log
AS
SELECT
    `id`,
    `userId`        AS `user_id`,
    `userName`      AS `user_name`,
    `operateTime`   AS `operate_time`,
    `ip`,
    `module`,
    `operateType`   AS `operate_type`,
    `apiName`       AS `api_name`,
    `apiPath`       AS `api_path`,
    `operateResult` AS `operate_result`,
    `details`,
    `responseDuration` AS `response_duration`
FROM operate_log_kafka_engine;