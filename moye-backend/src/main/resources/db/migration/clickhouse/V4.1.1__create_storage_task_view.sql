-- moye_v4.storage_task_view source

CREATE VIEW  IF NOT EXISTS storage_task_view
(

    `data_model_id` Int32,

    `name` Nullable(String),

    `dispatch_num` UInt64,

    `dispatch_time` DateTime,

    `error_count` UInt64,

    `normal_count` UInt64,

    `mode` String
) AS
SELECT
    data_model_id,

    visitParamExtractString(JSONExtractRaw(storage_job_config,
 'env'),
 'job.name') AS name,

    count(1) AS dispatch_num,

    toStartOfDay(start_time) AS dispatch_time,

    count(multiIf((execution_status = 'FAILED') OR (execution_status = 'FAILING') OR (execution_status = 'CANCELED'),
 1,
 NULL)) AS error_count,

    count(multiIf((execution_status = 'RUNNING') OR (execution_status = 'FINISHED'),
 1,
 NULL)) AS normal_count,

    multiIf((visitParamExtractString(JSONExtractRaw(storage_job_config,
 'source'),
 'plugin_name') = 'SftpFile') OR (visitParamExtractString(JSONExtractRaw(storage_job_config,
 'source'),
 'plugin_name') = 'Ftpfile'),
 'FILE',
 (visitParamExtractString(JSONExtractRaw(storage_job_config,
 'source'),
 'plugin_name') = 'Jdbc') OR (visitParamExtractString(JSONExtractRaw(storage_job_config,
 'source'),
 'plugin_name') = 'Elasticsearch') OR (visitParamExtractString(JSONExtractRaw(storage_job_config,
 'source'),
 'plugin_name') = 'Clickhouse') OR (visitParamExtractString(JSONExtractRaw(storage_job_config,
 'source'),
 'plugin_name') = 'Hive') OR (visitParamExtractString(JSONExtractRaw(storage_job_config,
 'source'),
 'plugin_name') = 'Oracle'),
 'DATA_BASE',
 visitParamExtractString(JSONExtractRaw(storage_job_config,
 'source'),
 'plugin_name') = 'Http',
 'API',
 (visitParamExtractString(JSONExtractRaw(storage_job_config,
 'source'),
 'plugin_name') = 'Kafka') OR (visitParamExtractString(JSONExtractRaw(storage_job_config,
 'source'),
 'plugin_name') = 'RocketMQ'),
 'MQ',
 'OTHER') AS mode
FROM storage_task AS tsb
GROUP BY
    data_model_id,

    name,

    toStartOfDay(start_time),

    mode;