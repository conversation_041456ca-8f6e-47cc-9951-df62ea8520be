ALTER TABLE data_process_trace ADD COLUMN if not exists storage_id Nullable(Int32)  COMMENT '存储点id';

DROP TABLE IF EXISTS data_process_trace_kafka_engine;
CREATE TABLE IF NOT EXISTS data_process_trace_kafka_engine
(

    `recordId` Int64,

    `processId` Int64,

    `receiveMqType` String,

    `receiveMqPath` String,

    `receiveGroup` String,

    `receiveTopic` String,

    `msgTitle` String,

    `dataSourceId` Int64,

    `tableId` Int64,

    `dataSourceName` String,

    `metadataId` Int64,

    `msgContent` String,

    `results` String,

    `storageTime` DateTime,

    `startTime` DateTime,

    `endTime` DateTime,

    `processingTime` Int64,

    `nodeName` String,

    `serviceName` String,

    `parentProcessingNode` String,

    `processingName` String,

    `processingType` String,

    `operatorId` Int64,
    
    `abilityId` Int32,

    `storageId` Int32,

    `nodeOrder` Int64,

    `isError` Int8,

    `errorMsg` String,

    `storeInfo` String,

    `contextInfo` String,

    `masterNode` String,

    `tenantId` Int64,

    `podIp` String
)
ENGINE = Kafka
SETTINGS kafka_broker_list = 'kafka-svc:9092',
 kafka_topic_list = 'moye_tracer_msg_topic',
 kafka_group_name = 'moye_tracer_msg_group',
 kafka_format = 'JSONStringsEachRow',
 kafka_skip_broken_messages = 20000,
 kafka_num_consumers = 1;

DROP TABLE IF EXISTS kafka_tracer_view;
CREATE MATERIALIZED VIEW IF NOT EXISTS kafka_tracer_view TO data_process_trace
(

    `record_id` Int64,

    `process_id` Int64,

    `receive_mq_type` Nullable(String),

    `receive_mq_path` Nullable(String),

    `receive_group` Nullable(String),

    `receive_topic` Nullable(String),

    `msg_title` Nullable(String),

    `data_source_id` Nullable(Int64),

    `table_id` Nullable(Int64),

    `data_source_name` Nullable(String),

    `data_model_id` Nullable(Int64),

    `msg_content` Nullable(String),

    `results` Nullable(String),

    `storage_time` DateTime,

    `start_time` Nullable(DateTime),

    `end_time` Nullable(DateTime),

    `processing_time` Int64,

    `node_name` Nullable(String),

    `service_name` Nullable(String),

    `parent_processing_node` Nullable(String),

    `processing_name` Nullable(String),

    `processing_type` Nullable(String),

    `operator_id` Nullable(Int64),
    
    `ability_id` Nullable(Int32),

    `storage_id` Nullable(Int32),

    `node_order` Int64,

    `is_error` Int8,

    `error_msg` Nullable(String),

    `store_info` Nullable(String),

    `context_info` Nullable(String),

    `master_node` Nullable(String),

    `tenant_id` Nullable(Int64),

    `pod_ip` Nullable(String)
) AS
SELECT
    recordId AS record_id,

    processId AS process_id,

    if(receiveMqType = '',
       NULL,
       receiveMqType) AS receive_mq_type,

    if(receiveMqPath = '',
       NULL,
       receiveMqPath) AS receive_mq_path,

    if(receiveGroup = '',
       NULL,
       receiveGroup) AS receive_group,

    if(receiveTopic = '',
       NULL,
       receiveTopic) AS receive_topic,

    if(msgTitle = '',
       NULL,
       msgTitle) AS msg_title,

    if(dataSourceId = 0,
       NULL,
       dataSourceId) AS data_source_id,

    if(tableId = 0,
       NULL,
       tableId) AS table_id,

    if(dataSourceName = '',
       NULL,
       dataSourceName) AS data_source_name,

    if(metadataId = 0,
       NULL,
       metadataId) AS data_model_id,

    if(msgContent = '',
       NULL,
       msgContent) AS msg_content,

    if(results = '',
       NULL,
       results) AS results,

    storageTime AS storage_time,

    if(startTime = toDateTime('1970-01-01 00:00:00'),
       NULL,
       startTime) AS start_time,

    if(endTime = toDateTime('1970-01-01 00:00:00'),
       NULL,
       endTime) AS end_time,

    processingTime AS processing_time,

    if(nodeName = '',
       NULL,
       nodeName) AS node_name,

    if(serviceName = '',
       NULL,
       serviceName) AS service_name,

    if(parentProcessingNode = '',
       NULL,
       parentProcessingNode) AS parent_processing_node,

    if(processingName = '',
       NULL,
       processingName) AS processing_name,

    if(processingType = '',
       NULL,
       processingType) AS processing_type,

    if(operatorId = 0,
       NULL,
       operatorId) AS operator_id,
       
    if(abilityId = 0,
       NULL,
       abilityId) AS ability_id,

    if(storageId = 0,
       NULL,
       storageId) AS storage_id,

    nodeOrder AS node_order,

    isError AS is_error,

    if(errorMsg = '',
       NULL,
       errorMsg) AS error_msg,

    if(storeInfo = '',
       NULL,
       storeInfo) AS store_info,

    if(contextInfo = '',
       NULL,
       contextInfo) AS context_info,

    if(masterNode = '',
       NULL,
       masterNode) AS master_node,

    if(tenantId = 0,
       NULL,
       tenantId) AS tenant_id,

    if(podIp = '',
       NULL,
       podIp) AS pod_ip
FROM data_process_trace_kafka_engine;