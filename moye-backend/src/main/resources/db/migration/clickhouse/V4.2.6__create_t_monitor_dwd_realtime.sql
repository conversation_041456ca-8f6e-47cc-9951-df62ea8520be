
CREATE TABLE IF NOT EXISTS t_monitor_dwd_realtime
(

    `id` Int64,

    `data_model_id` Int32 COMMENT '要素库ID',

    `data_model_name` String COMMENT '元数据名称',

    `topic` String COMMENT '消息主题',

    `monitor_time` DateTime64(3) COMMENT '监控时间',

    `current_offset` Int64 COMMENT '当前Offset',

    `end_offset` Int64 COMMENT '结束Offset',

    `lag` Int64 COMMENT '积压量',

    `avg_tps` Nullable(Int64) COMMENT '平均TPS',

    `source_type` String COMMENT '表来源分类：MQ',

    `source_sub_type` String COMMENT '表来源实际类型；kafka、rocketmq',

    `storage_time` DateTime64(3) COMMENT '入库时间',

    `data_quality` String COMMENT '数据质量标记,NORMAL,FIRST_RUN,CLOCK_DRIFT,LONG_INTERVAL,SHORT_INTERVAL,DATA_ERROR',

    `remark` Nullable(String) COMMENT '备注'
)
    ENGINE = MergeTree
    PARTITION BY toYYYYMMDD(monitor_time)
    PRIMARY KEY id
    ORDER BY(
    id,
    monitor_time,
    storage_time)
    SETTINGS index_granularity = 8192
    COMMENT '监控实时要素库表';