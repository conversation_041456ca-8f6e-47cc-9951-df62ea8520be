

CREATE TABLE IF NOT EXISTS ods_access_error_data
(

    `node_id` Nullable(String),

    `data_model_id` Nullable(Int64),

    `batch_no` Nullable(String),

    `node` Nullable(String),

    `msg_title` Nullable(String),

    `msg_content` Nullable(String),

    `error_msg` Nullable(String),

    `storage_time` DateTime
)
    ENGINE = MergeTree
PARTITION BY toYYYYMMDD(storage_time)
ORDER BY storage_time
SETTINGS index_granularity = 8192;



CREATE TABLE IF NOT EXISTS ods_data_access_trace
(

    `id` UUID DEFAULT generateUUIDv4(),

    `data_model_id` Nullable(Int64),

    `batch_no` Nullable(String),

    `node` Nullable(String),

    `details` Nullable(String),

    `is_error` Nullable(Int8),

    `storage_time` DateTime,

    `start_time` Nullable(DateTime),

    `end_time` Nullable(DateTime),

    `processing_time` Nullable(Int64)
)
    ENGINE = MergeTree
PARTITION BY toYYYYMMDD(storage_time)
PRIMARY KEY id
ORDER BY (id,
 storage_time)
SETTINGS index_granularity = 8192;