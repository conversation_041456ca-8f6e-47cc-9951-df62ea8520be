CREATE TABLE if not exists dashboard_data (
                                              id UInt64,                     -- 主键，唯一标识一条记录
                                              batch_id UInt64,               -- 批次号，每次插入新数据时，批次号递增
                                              category String,               -- 数据类别（如 dataSource, sourceLayer 等）
                                              metric String,                 -- 指标名称（如 industryCount, totalVolume 等）
                                              value Float64,                 -- 指标值
                                              created_at DateTime DEFAULT now() -- 数据写入时间
    )
    ENGINE = MergeTree
    PARTITION BY toYYYYMM(created_at) -- 按月分区
    ORDER BY (batch_id, category, metric); -- 主键顺序