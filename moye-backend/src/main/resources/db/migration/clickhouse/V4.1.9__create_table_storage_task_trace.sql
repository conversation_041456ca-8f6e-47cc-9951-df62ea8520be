drop table if exists ods_access_error_data;
drop table if exists ods_data_access_trace;

CREATE TABLE IF NOT EXISTS storage_task_trace
(

    `id` String,

    `data_model_id` Nullable(Int64),

    `batch_no` Nullable(String),

    `node` Nullable(String),

    `details` Nullable(String),

    `is_error` Nullable(Int8),

    `storage_time` DateTime,

    `start_time` Nullable(DateTime),

    `end_time` Nullable(DateTime),

    `processing_time` Nullable(Int64)
    )
    ENGINE = MergeTree
    PARTITION BY toYYYYMMDD(storage_time)
    PRIMARY KEY id
    ORDER BY (id,
              storage_time)
    SETTINGS index_granularity = 8192;