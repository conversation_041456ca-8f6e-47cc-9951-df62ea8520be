INSERT INTO storage_task_aggregated
WITH task_stats AS (
    SELECT
        count() AS total,
        countIf(execution_status = 'FINISHED') AS normal,
        countIf(execution_status = 'EXECUTION_FAILED') AS abnormal
    FROM storage_task
    WHERE layer_id = 1
)
SELECT
    'TASK' AS stat_type,
    toInt256(total) AS total_count,
    toInt256(normal) AS normal_count,
    toInt256(abnormal) AS abnormal_count,
    now() AS last_stat_time
FROM task_stats;
--
SELECT *
FROM storage_task_aggregated FINAL
WHERE stat_type = 'TASK'