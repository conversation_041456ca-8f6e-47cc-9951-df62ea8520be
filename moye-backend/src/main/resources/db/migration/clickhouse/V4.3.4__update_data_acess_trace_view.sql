DROP TABLE IF EXISTS data_access_trace_view;


CREATE VIEW data_access_trace_view
            (

             `data_model_id` Int32,

             `read_success_count` Int32,

             `read_fail_count` Int32,

             `access_count` Int64,

             `start_time` DateTime64(3)
                ) AS
SELECT
    data_model_id,

    read_success_count,

    read_fail_count,

    dat.read_success_count + dat.read_fail_count AS access_count,

    start_time
FROM storage_task AS dat
WHERE layer_id = 1;