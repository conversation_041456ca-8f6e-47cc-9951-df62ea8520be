INSERT
INTO storage_task_aggregated
WITH service_stats AS (SELECT SUM(JSONExtractInt(elem, 'successCount') + JSONExtractInt(elem, 'failCount')) as total,
                              SUM(JSONExtractInt(elem, 'successCount'))                                     AS normal,
                              SUM(JSONExtractInt(elem, 'failCount'))                                        AS abnormal
                       FROM storage_task
                                ARRAY JOIN JSONExtractArrayRaw(write_count_info) AS elem
                       where layer_id = 2)
SELECT 'DWD_STORAGE'      AS stat_type,
       toInt256(total)    AS total_count,
       toInt256(normal)   AS normal_count,
       toInt256(abnormal) AS abnormal_count,
       now()              AS last_stat_time
FROM service_stats;


INSERT INTO storage_task_aggregated
WITH service_stats AS (SELECT SUM(JSONExtractInt(elem, 'successCount') + JSONExtractInt(elem, 'failCount')) as total,
                              SUM(JSONExtractInt(elem, 'successCount'))                                     AS normal,
                              SUM(JSONExtractInt(elem, 'failCount'))                                        AS abnormal
                       FROM storage_task ARRAY
                                JOIN JSONExtractArrayRaw(write_count_info) AS elem
                       where layer_id = 3)
SELECT 'THEME_STORAGE'    AS stat_type,
       toInt256(total)    AS total_count,
       toInt256(normal)   AS normal_count,
       toInt256(abnormal) AS abnormal_count,
       now()              AS last_stat_time
FROM service_stats;


INSERT INTO storage_task_aggregated
WITH service_stats AS (SELECT SUM(JSONExtractInt(elem, 'successCount') + JSONExtractInt(elem, 'failCount')) as total,
                              SUM(JSONExtractInt(elem, 'successCount'))                                     AS normal,
                              SUM(JSONExtractInt(elem, 'failCount'))                                        AS abnormal
                       FROM storage_task ARRAY
                                JOIN JSONExtractArrayRaw(write_count_info) AS elem
                       where layer_id = 4)
SELECT 'SUBJECT_STORAGE'  AS stat_type,
       toInt256(total)    AS total_count,
       toInt256(normal)   AS normal_count,
       toInt256(abnormal) AS abnormal_count,
       now()              AS last_stat_time
FROM service_stats;

OPTIMIZE
TABLE storage_task_aggregated FINAL;
