CREATE TABLE IF NOT EXISTS data_process_monitor_record
(
    `id` Int64,
    `data_model_id` Nullable(Int32) COMMENT '要素库ID',
    `data_model_name` Nullable(String) COMMENT '元数据名称',
    `topic` Nullable(String) COMMENT '消息主题',
    `group` Nullable(String) COMMENT '消费者分组',
    `schedule_time` Nullable(DateTime64(3)) COMMENT '调度时间',
    `monitor_time` DateTime64(3) COMMENT '监控时间',
    `current_offset` Nullable(Int64) COMMENT '当前Offset',
    `total_offset` Nullable(Int64) COMMENT '总Offset',
    `access_count` Nullable(Int64) COMMENT '接入量',
    `process_count` Nullable(Int64) COMMENT '处理量',
    `lag_count` Nullable(Int64) COMMENT '积压量',
    `lag_change_count` Nullable(Int64) COMMENT '积压变化量',
    `access_tps` Nullable(Decimal(10, 1)) COMMENT '接入TPS',
    `process_tps` Nullable(Decimal(10, 1)) COMMENT '处理TPS',
    `storage_time` DateTime64(3) COMMENT '入库时间',
    `data_quality` Nullable(String) COMMENT '数据质量标记',
    `remark` Nullable(String) COMMENT '备注'
)
ENGINE = MergeTree
PARTITION BY toYYYYMMDD(monitor_time)
PRIMARY KEY id
ORDER BY (id, monitor_time, storage_time)
SETTINGS index_granularity = 8192
COMMENT '数据处理监控记录表';


CREATE TABLE IF NOT EXISTS data_storage_monitor_record
(
    `id` Int64,
    `data_model_id` Nullable(Int32) COMMENT '要素库ID',
    `data_model_name` Nullable(String) COMMENT '元数据名称',
    `data_storage_connection_id` Nullable(Int32) COMMENT '数据存储id',
    `topic` Nullable(String) COMMENT '消息主题',
    `group` Nullable(String) COMMENT '消费者分组',
    `schedule_time` Nullable(DateTime64(3)) COMMENT '调度时间',
    `monitor_time` DateTime64(3) COMMENT '监控时间',
    `current_offset` Nullable(Int64) COMMENT '当前Offset',
    `total_offset` Nullable(Int64) COMMENT '总Offset',
    `access_count` Nullable(Int64) COMMENT '接入量',
    `process_count` Nullable(Int64) COMMENT '处理量',
    `lag_count` Nullable(Int64) COMMENT '积压量',
    `lag_change_count` Nullable(Int64) COMMENT '积压变化量',
    `access_tps` Nullable(Decimal(10, 1)) COMMENT '接入TPS',
    `process_tps` Nullable(Decimal(10, 1)) COMMENT '处理TPS',
    `storage_time` DateTime64(3) COMMENT '入库时间',
    `data_quality` Nullable(String) COMMENT '数据质量标记',
    `remark` Nullable(String) COMMENT '备注'
)
ENGINE = MergeTree
PARTITION BY toYYYYMMDD(monitor_time)
PRIMARY KEY id
ORDER BY (id, monitor_time, storage_time)
SETTINGS index_granularity = 8192
COMMENT '数据存储监控记录表';