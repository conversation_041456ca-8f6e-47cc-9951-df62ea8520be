create table if not exists t_monitor_batch_task
(
    id              Int64 comment '主键',
    data_model_id   Int32 comment '元数据id；冗余',
    data_model_name String comment '元数据名称',
    config_id       Int32 comment '配置id',
    config_version  String comment '配置版本',
    monitor_time    DateTime64(3) comment '监控时间',
    storage_time    DateTime64(3) comment '实际入库时间',
    monitor_value   Int64 comment '监控值',
    monitor_detail  Nullable(String) comment '监控详情：对于监控数据的补充说明，可以为空',
    layer           String default 'ODS',
    done            Int8
    )
    engine = MergeTree PARTITION BY toYYYYMMDD(monitor_time)
    PRIMARY KEY id
    ORDER BY (id, monitor_time, storage_time)
    SETTINGS index_granularity = 8192
    comment '批任务执行时长监控表';