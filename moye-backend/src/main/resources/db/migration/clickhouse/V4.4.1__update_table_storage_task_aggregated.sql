INSERT INTO storage_task_aggregated
WITH service_stats AS (
    SELECT
        count() AS total,
        countIf(response_status = 'SUCCESS') AS normal,
        countIf(response_status != 'SUCCESS') AS abnormal
    FROM data_service_log
)
SELECT
    'DATA_SERVICE_INVOKE' AS stat_type,
    toInt256(total) AS total_count,
    toInt256(normal) AS normal_count,
    toInt256(abnormal) AS abnormal_count,
    now() AS last_stat_time
FROM service_stats;
OPTIMIZE TABLE storage_task_aggregated FINAL;