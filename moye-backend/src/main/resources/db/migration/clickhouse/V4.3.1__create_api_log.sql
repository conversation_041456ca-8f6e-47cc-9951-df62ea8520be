CREATE TABLE IF NOT EXISTS `api_log` (
    `id` Int64 COMMENT 'id',
    `external_log_id` Nullable(String) COMMENT '外部日志ID',
    `application_name` Nullable(String) COMMENT '应用名称',
    `api_name` Nullable(String) COMMENT 'API名称',
    `api_path` Nullable(String) COMMENT 'API名称',
    `request_parameters` Nullable(String) COMMENT '请求参数，业务',
    `request_time` DateTime64(3) COMMENT '请求时间',
    `response_status` Nullable(String) COMMENT '响应状态（成功(SUCCESS)或失败(FAILURE)）',
    `response_duration` Nullable(Int64) COMMENT '响应时长（毫秒）',
    `response_details` Nullable(String) COMMENT '响应详情',
    `connection_type` Nullable(String) COMMENT '连接类型',
    `connection_name` Nullable(String) COMMENT '连接名称'
) ENGINE = MergeTree()
    PARTITION BY toYYYYMMDD(`request_time`)
    PRIMARY KEY `id`
    ORDER BY (`id`, `request_time`)
    SETTINGS index_granularity = 8192;


CREATE TABLE IF NOT EXISTS api_log_kafka_engine
(
    `id` Int64,
    `externalLogId` Nullable(String),
    `applicationName` Nullable(String),
    `apiName` Nullable(String),
    `apiPath` Nullable(String),
    `requestParameters` Nullable(String),
    `requestTime` DateTime64(3),
    `responseStatus` Nullable(String),
    `responseDuration` Nullable(Int64),
    `responseDetails` Nullable(String),
    `connectionType` Nullable(String),
    `connectionName` Nullable(String)
)
ENGINE = Kafka
SETTINGS
    kafka_broker_list = 'kafka-svc:9092',
    kafka_topic_list = 'moye-api-log-topic',
    kafka_group_name = 'moye-api-log-group',
    kafka_format = 'JSONEachRow',
    kafka_skip_broken_messages = 20000,
    kafka_num_consumers = 4;

CREATE MATERIALIZED VIEW IF NOT EXISTS  api_log_view TO api_log
AS
SELECT
    `id`,
    `externalLogId`  AS `external_log_id`,
    `applicationName`  AS `application_name`,
    `apiName`  AS `api_name`,
    `apiPath`  AS `api_path`,
    `requestParameters`  AS `request_parameters`,
    `requestTime`  AS `request_time`,
    `responseStatus`  AS `response_status`,
    `responseDuration`  AS `response_duration`,
    `responseDetails`  AS `response_details`,
    `connectionType`  AS `connection_type`,
    `connectionName`  AS `connection_name`
FROM api_log_kafka_engine;




CREATE TABLE IF NOT EXISTS `api_log_trace` (
    `id` Int64 COMMENT 'id',
    `log_id` Nullable(Int64) COMMENT '日志id',
    `node` Nullable(String) COMMENT '节点',
    `application_name` Nullable(String) COMMENT '应用名称',
    `create_timestamp` Int64 COMMENT '创建时间戳',
    `execute_duration` Nullable(Int64) COMMENT '执行时长',
    `execute_result` Nullable(String) COMMENT '执行结果',
    `input_details` Nullable(String) COMMENT '输入详情',
    `output_details` Nullable(String) COMMENT '输出详情'
) ENGINE = MergeTree()
    PARTITION BY toYYYYMMDD(toDateTime(create_timestamp))
    PRIMARY KEY `id`
    ORDER BY (`id`, `create_timestamp`)
    SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS api_log_trace_kafka_engine
(
    `id` Int64,
    `logId` Nullable(Int64),
    `node` Nullable(String),
    `applicationName` Nullable(String),
    `createTimestamp` Int64,
    `executeDuration` Nullable(Int64),
    `executeResult` Nullable(String),
    `inputDetails` Nullable(String),
    `outputDetails` Nullable(String)
)
ENGINE = Kafka
SETTINGS
    kafka_broker_list = 'kafka-svc:9092',
    kafka_topic_list = 'moye-api-log-trace-topic',
    kafka_group_name = 'moye-api-log-trace-group',
    kafka_format = 'JSONEachRow',
    kafka_skip_broken_messages = 20000,
    kafka_num_consumers = 4;


CREATE MATERIALIZED VIEW IF NOT EXISTS  api_log_trace_view TO api_log_trace
AS
SELECT
    `id`,
    `logId` AS `log_id`,
    `node` AS `node`,
    `applicationName` AS `application_name`,
    `createTimestamp` AS `create_timestamp`,
    `executeDuration` AS `execute_duration`,
    `executeResult` AS `execute_result`,
    `inputDetails` AS `input_details`,
    `outputDetails` AS `output_details`
FROM api_log_trace_kafka_engine;