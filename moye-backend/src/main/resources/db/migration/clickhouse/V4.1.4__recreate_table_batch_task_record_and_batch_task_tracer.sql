-- 创建新表batch_task_record_new
CREATE TABLE IF NOT EXISTS batch_task_record_new_temp
(
    `execute_id` String COMMENT '执行id，主键，定时任务触发时产生一条记录',
    `application_id` Nullable(String) COMMENT 'spark任务id',
    `trigger_mode` Nullable(String) COMMENT '触发模式：fixed_time（定时），immediate（立即执行）',
    `task_id` Nullable(Int32) COMMENT '任务id（对应要素库id）',
    `task_name` Nullable(String) COMMENT '任务名称（yarn上的任务名称）',
    `start_time` DateTime64(3) COMMENT '开始时间',
    `end_time` Nullable(DateTime64(3)) COMMENT '结束时间',
    `is_error` Nullable(Int8) COMMENT '是否异常',
    `error_msg_read_flag` Int32 DEFAULT 0 COMMENT '异常信息读取标记'
)
    ENGINE = MergeTree
    PARTITION BY toYYYYMMDD(start_time)
    PRIMARY KEY execute_id
    ORDER BY (execute_id, start_time)
    SETTINGS index_granularity = 8192;

-- 数据迁移
INSERT INTO batch_task_record_new_temp
SELECT
    execute_id,
    application_id,
    trigger_mode,
    task_id,
    task_name,
    start_time,
    end_time,
    is_error,
    error_msg_read_flag
FROM batch_task_record;

-- 删除旧表并重命名新表
DROP TABLE IF EXISTS batch_task_record;
RENAME TABLE batch_task_record_new_temp TO batch_task_record;

-- 创建新表batch_task_tracer_new
CREATE TABLE IF NOT EXISTS batch_task_tracer_new_temp
(
    `id` Int64 COMMENT 'id',
    `node` String COMMENT '节点',
    `execute_id` String COMMENT '执行id',
    `start_time` DateTime64(3) COMMENT '开始时间或创建时间',
    `end_time` Nullable(DateTime64(3)) COMMENT '结束时间',
    `process_time` Nullable(Int64) COMMENT '处理时长单位毫秒',
    `is_error` Nullable(Int8) COMMENT '是否异常',
    `error_msg` Nullable(String) COMMENT '异常信息'
)
    ENGINE = MergeTree
    PARTITION BY toYYYYMMDD(start_time)
    PRIMARY KEY id
    ORDER BY id
    SETTINGS index_granularity = 8192;

-- 数据迁移
INSERT INTO batch_task_tracer_new_temp
SELECT
    id,
    node,
    execute_id,
    start_time,
    end_time,
    process_time,
    is_error,
    error_msg
FROM batch_task_tracer;

-- 删除旧表并重命名新表
DROP TABLE IF EXISTS batch_task_tracer;
RENAME TABLE batch_task_tracer_new_temp TO batch_task_tracer;
