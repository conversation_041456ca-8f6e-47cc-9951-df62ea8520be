drop table if exists storage_task;


CREATE TABLE IF NOT EXISTS storage_task
(

    `id` Int64 COMMENT 'id',

    `data_model_id` Int32 COMMENT '任务id，如贴源库、要素库等具体库id',

    `batch_no` String COMMENT '批次号',

    `access_mode` String COMMENT '接入模式：real_time（实时接入）；fixed_time（定时接入）',

    `read_success_count` Int32 COMMENT '读取成功量',

    `read_fail_count` Int32 COMMENT '读取失败量',

    `write_count_info` String COMMENT '写入数量信息',

    `start_time` DateTime64(3) COMMENT '开始时间',

    `end_time` Nullable(DateTime64(3)) COMMENT '结束时间',

    `allow_sync` Nullable(Bool) COMMENT '允许同步：',

    `last_sync_time` Nullable(DateTime64(3)) COMMENT '上次同步时间',

    `storage_job_id` Nullable(String) COMMENT '存储任务id，调用存储引擎时返回，用于监控存储引擎运行状况',

    `execution_status` String DEFAULT 'UNKNOWABLE' COMMENT '执行状态,
\r\n\n参考com.trs.moye.storage.engine.seatunnel.enums',

    `error_message` Nullable(String) COMMENT '报错文本信息',

    `layer_id` Nullable(Int32) COMMENT '层级id',

    `error_msg_read_flag` Int32 DEFAULT 0,

    `storage_job_config` Nullable(String) COMMENT '存储任务配置信息'
)
    ENGINE = MergeTree
PRIMARY KEY id
PARTITION BY toYYYYMMDD(start_time)
ORDER BY id
SETTINGS index_granularity = 8192
COMMENT '数据存储任务';