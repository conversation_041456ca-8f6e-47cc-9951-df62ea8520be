alter table data_access_trace add column IF NOT EXISTS `storage_name` Nullable(String);

drop table IF EXISTS data_access_trace_kafka_engine;

CREATE TABLE IF NOT EXISTS data_access_trace_kafka_engine
(

    `record_id` Int64,

    `batch_no` Nullable(String),

    `task_id` Int64,

    `access_time` DateTime,

    `is_error` UInt8,

    `error_message` Nullable(String),

    `data` Nullable(String),

    `storage_name` Nullable(String)
)
    ENGINE = Kafka
    SETTINGS kafka_broker_list = 'kafka-svc:9092',
 kafka_topic_list = 'moye_access_data',
 kafka_group_name = 'moye_access_data_group',
 kafka_format = 'JSONEachRow',
 kafka_skip_broken_messages = 0,
 kafka_num_consumers = 1;

drop table kafka_access_view;
CREATE MATERIALIZED VIEW kafka_access_view TO data_access_trace
(

    `record_id` Int64,

    `batch_no` Nullable(String),

    `task_id` Int64,

    `access_time` DateTime,

    `is_error` UInt8,

    `error_message` Nullable(String),

    `data` Nullable(String),

    `storage_name` Nullable(String)
) AS
SELECT
    record_id,

    if(batch_no = '',
       NULL,
       batch_no) AS batch_no,

    task_id,

    access_time,

    is_error,

    if(error_message = '',
       NULL,
       error_message) AS error_message,

    if(data = '',
       NULL,
       data) AS data,

    if(storage_name = '',
       NULL,
       storage_name) AS storage_name
FROM data_access_trace_kafka_engine;


