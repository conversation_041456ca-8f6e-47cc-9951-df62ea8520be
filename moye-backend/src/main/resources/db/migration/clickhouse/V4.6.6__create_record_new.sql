DROP TABLE IF EXISTS data_process_record_new;
CREATE TABLE data_process_record_new
(
    `record_id` Int64,
    `msg_title` Nullable(String),
    `operator_count` Nullable(Int64),
    `data_model_id` Nullable(Int64),
    `data_source_name` Nullable(String),
    `storage_time` DateTime,
    `start_time` Nullable(DateTime),
    `end_time` Nullable(DateTime),
    `processing_time` Nullable(Int64),
    `is_error` Int8,
    `executed_operator_count` Nullable(Int64),
    `tenant_id` Nullable(Int64),
    `pod_ip` Nullable(String) DEFAULT NULL,
    `error_msg_read_flag` Int32 DEFAULT 0
)
    ENGINE = MergeTree
    PARTITION BY (toYYYYMMDD(storage_time),is_error)
    PRIMARY KEY record_id
ORDER BY (record_id,
 storage_time)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS data_process_trace_new;
CREATE TABLE data_process_trace_new
(

    `record_id` Int64,
    `process_id` Int64,
    `receive_mq_type` Nullable(String),
    `receive_mq_path` Nullable(String),
    `receive_group` Nullable(String),
    `receive_topic` Nullable(String),
    `msg_title` Nullable(String),
    `data_source_id` Nullable(Int64),
    `table_id` Nullable(Int64),
    `data_source_name` Nullable(String),
    `data_model_id` Nullable(Int64),
    `msg_content` Nullable(String) CODEC(ZSTD(1)) TTL storage_time + toIntervalDay(15),
    `results` Nullable(String) CODEC(ZSTD(1)) TTL storage_time + toIntervalDay(15),
    `storage_time` DateTime,
    `start_time` Nullable(DateTime),
    `end_time` Nullable(DateTime),
    `processing_time` Nullable(Int64),
    `node_name` Nullable(String),
    `service_name` Nullable(String),
    `parent_processing_node` Nullable(String),
    `processing_name` Nullable(String),
    `processing_type` Nullable(String),
    `operator_id` Nullable(Int64),
    `node_order` Nullable(Int64),
    `is_error` Int8,
    `error_msg` Nullable(String) CODEC(ZSTD(1)),
    `store_info` Nullable(String) CODEC(ZSTD(1)),
    `context_info` Nullable(String) CODEC(ZSTD(1)),
    `master_node` Nullable(String),
    `tenant_id` Nullable(Int64),
    `pod_ip` Nullable(String) DEFAULT NULL,
    `ability_id` Nullable(Int32) COMMENT '能力id'
)
    ENGINE = MergeTree
    PARTITION BY (toYYYYMMDD(storage_time),is_error)
    PRIMARY KEY (record_id,
 process_id)
ORDER BY (record_id,
 process_id,
 storage_time)
SETTINGS index_granularity = 8192;


DROP TABLE IF EXISTS data_access_trace_new;
CREATE TABLE data_access_trace_new
(
    `record_id` Int64,
    `batch_no` Nullable(String),
    `data_model_id` Int64,
    `access_time` DateTime,
    `is_error` UInt8,
    `error_message` Nullable(String),
    `data` Nullable(String),
    `storage_name` Nullable(String)
)
    ENGINE = MergeTree
    PARTITION BY (toYYYYMMDD(access_time),is_error)
    ORDER BY (record_id,
              access_time)
    SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS batch_task_record_new;
CREATE TABLE batch_task_record_new
(
    `execute_id` String COMMENT '执行id，主键，定时任务触发时产生一条记录',
    `application_id` Nullable(String) COMMENT 'spark任务id',
    `trigger_mode` Nullable(String) COMMENT '触发模式：fixed_time（定时），immediate（立即执行）',
    `task_id` Nullable(Int32) COMMENT '任务id（对应要素库id）',
    `task_name` Nullable(String) COMMENT '任务名称（yarn上的任务名称）',
    `start_time` DateTime64(3) COMMENT '开始时间',
    `end_time` Nullable(DateTime64(3)) COMMENT '结束时间',
    `is_error` Int8 COMMENT '是否异常',
    `error_msg_read_flag` Int32 DEFAULT 0 COMMENT '异常信息读取标记',
    `storage_success_count` Nullable(Int64) COMMENT '存储到表的数据量',
    `layer` Nullable(String) COMMENT '分层',
    `write_count_info` String COMMENT '写入数量'
)
    ENGINE = MergeTree
    PARTITION BY (toYYYYMMDD(start_time))
    PRIMARY KEY execute_id
ORDER BY (execute_id,
 start_time)
SETTINGS index_granularity = 8192;


DROP TABLE IF EXISTS batch_task_tracer_new;
CREATE TABLE batch_task_tracer_new
(   `id` Int64 COMMENT 'id',
    `node` String COMMENT '节点',
    `execute_id` String COMMENT '执行id',
    `start_time` DateTime64(3) COMMENT '开始时间或创建时间',
    `end_time` Nullable(DateTime64(3)) COMMENT '结束时间',
    `process_time` Nullable(Int64) COMMENT '处理时长单位毫秒',
    `is_error` Int8 COMMENT '是否异常',
    `error_msg` Nullable(String) COMMENT '异常信息',
    `data_count` Nullable(Int64) COMMENT '算子输出数据量',
    `output_table_name` Nullable(String) COMMENT '算子输出表名',
    `arranged_name` Nullable(String) COMMENT '编排算子名称',
    `input_table_name` Nullable(String) COMMENT '算子输入表名',
    `conditions` Nullable(String) COMMENT '筛选条件sql',
    `sample_data` Nullable(String) COMMENT '算子输出抽样数据',
    `fields` Nullable(String) COMMENT '算子输出数据字段',
    `pre_process_data_count` Nullable(Int64) COMMENT '算子处理前数据量'
)
    ENGINE = MergeTree
    PARTITION BY (toYYYYMMDD(start_time))
    PRIMARY KEY id
ORDER BY id
SETTINGS index_granularity = 8192;