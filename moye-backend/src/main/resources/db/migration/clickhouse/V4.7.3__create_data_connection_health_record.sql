CREATE TABLE IF NOT EXISTS `data_connection_health_record`
(

    `connection_id` Int64 COMMENT '连接id',

    `is_error` UInt8 COMMENT '是否异常',

    `error_message` Nullable(String) COMMENT '异常信息',

    `detection_time` DateTime COMMENT '检测时间',

    `detection_type` String COMMENT '检测类型',

    `detection_user_name` String COMMENT '检测用户'
) ENGINE = MergeTree
    PARTITION BY toYYYYMMDD(detection_time)
    ORDER BY (connection_id, detection_time)
    SETTINGS index_granularity = 8192;