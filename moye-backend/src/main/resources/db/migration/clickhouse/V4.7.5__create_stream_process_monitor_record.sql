
CREATE TABLE IF NOT EXISTS stream_process_monitor_record
(
    `id` Int64,
    `data_model_id` Nullable(Int32) COMMENT '要素库ID',
    `data_model_name` Nullable(String) COMMENT '元数据名称',
    `topic` Nullable(String) COMMENT '消息主题',
    `group` Nullable(String) COMMENT '消费者分组',
    `monitor_time` DateTime64(3) COMMENT '监控时间',
    `last_monitor_time` Nullable(DateTime64(3)) COMMENT '上一次监控时间',
    `storage_time` DateTime64(3) COMMENT '入库时间',
    `access_total_count` Nullable(Int64) COMMENT '接入总量',
    `process_total_count` Nullable(Int64) COMMENT '处理总量',
    `lag_count` Nullable(Int64) COMMENT '积压量',
    `access_count` Nullable(Int64) COMMENT '接入量',
    `process_count` Nullable(Int64) COMMENT '处理量',
    `lag_change_count` Nullable(Int64) COMMENT '积压变化量',
    `access_tps` Nullable(Decimal(20,10)) COMMENT '接入TPS',
    `process_tps` Nullable(Decimal(20,10)) COMMENT '处理TPS',
    `remark` Nullable(String) COMMENT '备注'
)
ENGINE = MergeTree
PARTITION BY toYYYYMMDD(monitor_time)
PRIMARY KEY id
ORDER BY (id, monitor_time, storage_time)
SETTINGS index_granularity = 8192
COMMENT '流处理接入处理监控记录表';


CREATE TABLE IF NOT EXISTS stream_storage_monitor_record
(
    `id` Int64  COMMENT '主键，同处理监控id',
    `data_model_id` Nullable(Int32) COMMENT '要素库ID',
    `data_model_name` Nullable(String) COMMENT '元数据名称',
    `data_storage_connection_id` Nullable(Int32) COMMENT '数据存储连接id',
    `storage_point_id` Nullable(Int32) COMMENT '存储点id',
    `monitor_time` DateTime64(3) COMMENT '监控时间',
    `last_monitor_time` Nullable(DateTime64(3)) COMMENT '上一次监控时间',
    `storage_time` DateTime64(3) COMMENT '入库时间',
    `storage_total_count` Nullable(Int64) COMMENT '存储总量',
    `storage_lag_count` Nullable(Int64) COMMENT '积压量',
    `storage_count` Nullable(Int64) COMMENT '存储量',
    `storage_lag_change_count` Nullable(Int64) COMMENT '积压变化量',
    `storage_tps` Nullable(Decimal(20,10)) COMMENT '存储TPS',
    `remark` Nullable(String) COMMENT '备注'
)
ENGINE = MergeTree
PARTITION BY toYYYYMMDD(monitor_time)
ORDER BY (id,monitor_time,storage_time)
SETTINGS index_granularity = 8192
COMMENT '流处理存储监控记录表';