DROP TABLE IF EXISTS basic_component_detection_statistics;
CREATE TABLE basic_component_detection_statistics
(

    `id` Int64 COMMENT 'id',

    `component_name` String COMMENT '组件名称',

    `error_count` Int64 COMMENT '异常次数',

    `detection_count` Int64 COMMENT '检测次数',

    `last_status` Int64 COMMENT '最近一次检测状态',

    `last_error_time` DateTime COMMENT '最近一次异常时间',

    `last_detection_time` DateTime COMMENT '最近一次检测时间',

    `last_error_message` String COMMENT '最近一次异常信息'
)
    ENGINE = MergeTree
    PRIMARY KEY id
ORDER BY (id)
SETTINGS index_granularity = 8192;