CREATE TABLE IF NOT EXISTS batch_task_record
(

    `execute_id` String COMMENT '执行id，主键，定时任务触发时产生一条记录',

    `application_id` Nullable(String) COMMENT 'spark任务id',

    `trigger_mode` Nullable(String) COMMENT '触发模式：fixed_time（定时），immediate（立即执行）',

    `task_id` Nullable(Int32) COMMENT '任务id（对应要素库id）',

    `task_name` Nullable(String) COMMENT '任务名称（yarn上的任务名称）',

    `start_time` DateTime64(3) COMMENT '开始时间',

    `end_time` Nullable(DateTime64(3)) COMMENT '结束时间',

    `is_error` Nullable(Int8) COMMENT '是否异常',

    `error_msg_read_flag` Int32 DEFAULT 0 COMMENT '异常信息读取标记'
)
    ENGINE = MergeTree
    PRIMARY KEY execute_id
ORDER BY (execute_id,
 start_time)
SETTINGS index_granularity = 8192;


CREATE TABLE IF NOT EXISTS batch_task_tracer
(

    `id` Int64 COMMENT 'id',

    `node` String COMMENT '节点',

    `execute_id` String COMMENT '执行id',

    `start_time` DateTime64(3) COMMENT '开始时间或创建时间',

    `end_time` Nullable(DateTime64(3)) COMMENT '结束时间',

    `process_time` Nullable(Int64) COMMENT '处理时长单位毫秒',

    `is_error` Nullable(Int8) COMMENT '是否异常',

    `error_msg` Nullable(String) COMMENT '异常信息'
)
    ENGINE = MergeTree
    PRIMARY KEY id
ORDER BY id
SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS data_access_trace
(

    `record_id` Int64,

    `batch_no` Nullable(String),

    `task_id` Int64,

    `access_time` DateTime,

    `is_error` UInt8,

    `error_message` Nullable(String),

    `data` Nullable(String)
)
    ENGINE = MergeTree
    PARTITION BY toYYYYMMDD(access_time)
    ORDER BY (record_id,
              access_time)
    SETTINGS index_granularity = 8192;


CREATE TABLE IF NOT EXISTS data_access_trace_kafka_engine
(

    `record_id` Int64,

    `batch_no` Nullable(String),

    `task_id` Int64,

    `access_time` DateTime,

    `is_error` UInt8,

    `error_message` Nullable(String),

    `data` Nullable(String)
)
    ENGINE = Kafka
    SETTINGS kafka_broker_list = 'kafka-svc.trs:9092',
 kafka_topic_list = 'moye_access_msg_topic',
 kafka_group_name = 'moye_access_msg_topic_group',
 kafka_format = 'JSONEachRow',
 kafka_skip_broken_messages = 0,
 kafka_num_consumers = 1;



CREATE TABLE IF NOT EXISTS data_process_record
(

    `record_id` Int64,

    `msg_title` Nullable(String),

    `operator_count` Nullable(Int64),

    `data_model_id` Nullable(Int64),

    `data_source_name` Nullable(String),

    `storage_time` DateTime,

    `start_time` Nullable(DateTime),

    `end_time` Nullable(DateTime),

    `processing_time` Nullable(Int64),

    `is_error` Nullable(Int8),

    `executed_operator_count` Nullable(Int64),

    `tenant_id` Nullable(Int64),

    `pod_ip` Nullable(String) DEFAULT NULL,

    `error_msg_read_flag` Int32 DEFAULT 0
)
    ENGINE = MergeTree
    PARTITION BY toYYYYMMDD(storage_time)
    PRIMARY KEY record_id
    ORDER BY (record_id,
              storage_time)
    SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS data_process_record_kafka_engine
(

    `recordId` Int64,

    `msgTitle` String,

    `operatorCount` Int64,

    `dataSourceId` Int64,

    `dataSourceName` String,

    `storageTime` DateTime,

    `startTime` DateTime,

    `endTime` DateTime,

    `processingTime` Int64,

    `isError` Int8,

    `executedOperatorCount` Int64,

    `tenantId` Int64,

    `podIp` String
)
    ENGINE = Kafka
    SETTINGS kafka_broker_list = 'kafka-svc.trs:9092',
 kafka_topic_list = 'moye_record_msg_topic',
 kafka_group_name = 'moye_record_msg_group',
 kafka_format = 'JSONStringsEachRow',
 kafka_skip_broken_messages = 20000,
 kafka_num_consumers = 1;

CREATE TABLE IF NOT EXISTS data_process_trace
(

    `record_id` Int64,

    `process_id` Int64,

    `receive_mq_type` Nullable(String),

    `receive_mq_path` Nullable(String),

    `receive_group` Nullable(String),

    `receive_topic` Nullable(String),

    `msg_title` Nullable(String),

    `data_source_id` Nullable(Int64),

    `table_id` Nullable(Int64),

    `data_source_name` Nullable(String),

    `data_model_id` Nullable(Int64),

    `msg_content` Nullable(String) CODEC(ZSTD(1)) TTL storage_time + toIntervalDay(15),

    `results` Nullable(String) CODEC(ZSTD(1)) TTL storage_time + toIntervalDay(15),

    `storage_time` DateTime,

    `start_time` Nullable(DateTime),

    `end_time` Nullable(DateTime),

    `processing_time` Nullable(Int64),

    `node_name` Nullable(String),

    `service_name` Nullable(String),

    `parent_processing_node` Nullable(String),

    `processing_name` Nullable(String),

    `processing_type` Nullable(String),

    `operator_id` Nullable(Int64),

    `node_order` Nullable(Int64),

    `is_error` Nullable(Int8),

    `error_msg` Nullable(String) CODEC(ZSTD(1)),

    `store_info` Nullable(String) CODEC(ZSTD(1)),

    `context_info` Nullable(String) CODEC(ZSTD(1)),

    `master_node` Nullable(String),

    `tenant_id` Nullable(Int64),

    `pod_ip` Nullable(String) DEFAULT NULL
)
    ENGINE = MergeTree
    PARTITION BY toYYYYMMDD(storage_time)
    PRIMARY KEY (record_id,
                 process_id)
    ORDER BY (record_id,
              process_id,
              storage_time)
    SETTINGS index_granularity = 8192;


CREATE TABLE IF NOT EXISTS data_process_trace_kafka_engine
(

    `recordId` Int64,

    `processId` Int64,

    `receiveMqType` String,

    `receiveMqPath` String,

    `receiveGroup` String,

    `receiveTopic` String,

    `msgTitle` String,

    `dataSourceId` Int64,

    `tableId` Int64,

    `dataSourceName` String,

    `metadataId` Int64,

    `msgContent` String,

    `results` String,

    `storageTime` DateTime,

    `startTime` DateTime,

    `endTime` DateTime,

    `processingTime` Int64,

    `nodeName` String,

    `serviceName` String,

    `parentProcessingNode` String,

    `processingName` String,

    `processingType` String,

    `operatorId` Int64,

    `nodeOrder` Int64,

    `isError` Int8,

    `errorMsg` String,

    `storeInfo` String,

    `contextInfo` String,

    `masterNode` String,

    `tenantId` Int64,

    `podIp` String
)
    ENGINE = Kafka
    SETTINGS kafka_broker_list = 'kafka-svc.trs:9092',
 kafka_topic_list = 'moye_tracer_msg_topic',
 kafka_group_name = 'moye_tracer_msg_group',
 kafka_format = 'JSONStringsEachRow',
 kafka_skip_broken_messages = 20000,
 kafka_num_consumers = 1;

CREATE TABLE IF NOT EXISTS storage_task
(

    `id` Int64 COMMENT 'id',

    `data_model_id` Int32 COMMENT '任务id，如贴源库、要素库等具体库id',

    `batch_no` String COMMENT '批次号',

    `access_mode` String COMMENT '接入模式：real_time（实时接入）；fixed_time（定时接入）',

    `read_success_count` Int32 COMMENT '读取成功量',

    `read_fail_count` Int32 COMMENT '读取失败量',

    `write_count_info` String COMMENT '写入数量信息',

    `start_time` DateTime64(3) COMMENT '开始时间',

    `end_time` Nullable(DateTime64(3)) COMMENT '结束时间',

    `allow_sync` Nullable(Bool) COMMENT '允许同步：',

    `last_sync_time` Nullable(DateTime64(3)) COMMENT '上次同步时间',

    `storage_job_id` Nullable(String) COMMENT '存储任务id，调用存储引擎时返回，用于监控存储引擎运行状况',

    `execution_status` String DEFAULT 'UNKNOWABLE' COMMENT '执行状态,
\n参考com.trs.moye.storage.engine.seatunnel.enums',

    `error_message` Nullable(String) COMMENT '报错文本信息',

    `layer_id` Nullable(Int32) COMMENT '层级id',

    `error_msg_read_flag` Int32 DEFAULT 0,

    `storage_job_config` Nullable(String) COMMENT '存储任务配置信息'
)
    ENGINE = MergeTree
    PRIMARY KEY id
ORDER BY id
SETTINGS index_granularity = 8192
COMMENT '数据存储任务';


CREATE TABLE IF NOT EXISTS t_message_push_records
(

    `message_type` LowCardinality(String) COMMENT '消息类型名称',

    `message_subtype` LowCardinality(String) COMMENT '消息子类型名称',

    `title` LowCardinality(String) COMMENT '标题',

    `content` String COMMENT '内容' CODEC(ZSTD(1)),

    `push_conf_id` Int32 COMMENT '推送配置id',

    `push_type` LowCardinality(String) COMMENT '消息推送类型的名称',

    `push_time` DateTime64(3) COMMENT '消息发送时间',

    `push_address` String COMMENT '推送地址',

    `push_depts` Array(Int32) COMMENT '推送对象部门',

    `push_roles` Array(Int32) COMMENT '推送对象角色',

    `push_users` Array(Int32) COMMENT '推送对象用户',

    `is_success` UInt8 COMMENT '是否推送成功,
\r\n 0 失败,
\r\n 1 成功',

    `error_msg` Nullable(String) COMMENT '失败原因' CODEC(ZSTD(1)),

    `storage_time` DateTime64(3) COMMENT '记录入库时间'
)
    ENGINE = MergeTree
    PARTITION BY toYYYYMMDD(storage_time)
    PRIMARY KEY push_time
    ORDER BY (push_time,
              is_success,
              push_type,
              message_type,
              message_subtype)
    SETTINGS index_granularity = 8192
    COMMENT '消息推送记录';

CREATE TABLE IF NOT EXISTS t_monitor_ods_cutoff
(

    `id` Int64 COMMENT '主键',

    `data_model_id` Int32 COMMENT '元数据id；冗余',

    `data_model_name` String COMMENT '元数据名称',

    `source_type` String COMMENT '表来源分类：MQ、DB、文件、API等',

    `source_sub_type` String COMMENT '表来源实际类型；mysql、kafka、sftp',

    `config_id` Int32 COMMENT '配置id',

    `config_version` String COMMENT '配置版本',

    `monitor_time` DateTime64(3) COMMENT '监控时间',

    `storage_time` DateTime64(3) COMMENT '实际入库时间',

    `monitor_value` Int64 COMMENT '监控值',

    `increment_column` Nullable(String) COMMENT '增量字段',

    `increment_value` Nullable(String) COMMENT '增量字段值',

    `monitor_detail` Nullable(String) COMMENT '监控详情：对于监控数据的补充说明，可以为空',

    `is_cutoff` Int8 COMMENT '是否断流：1-断流，0-不断流；监控值与上一次比较无变化被认为是断流，否则不是断流（数据库用数字，代码用布尔）'
)
    ENGINE = MergeTree
    PARTITION BY toYYYYMMDD(monitor_time)
    PRIMARY KEY id
    ORDER BY (id,
              monitor_time,
              storage_time)
    SETTINGS index_granularity = 8192
    COMMENT '监控断流表';



CREATE TABLE IF NOT EXISTS t_monitor_ods_fluctuation
(

    `id` Int64,

    `data_model_id` Int32 COMMENT '贴源库id(元数据id)',

    `data_model_name` String COMMENT '贴源库中文名称',

    `config_id` Int32 COMMENT '配置id',

    `config_version` String COMMENT '配置版本',

    `monitor_time` DateTime64(3) COMMENT '监测时间',

    `total` Nullable(UInt64) COMMENT '上游数据总量',

    `increment_column` Nullable(String) COMMENT '增量字段',

    `increment_value` Nullable(String) COMMENT '增量字段目前的值',

    `increment` Int64 COMMENT '本次监测数据增加量',

    `average` Int64 COMMENT '平均值',

    `fluctuation_type` Int8 COMMENT '波动类型,
\r\n\r\n 1 表示增加，-1 表示减少',

    `fluctuation` UInt64 COMMENT '波动偏差，单位%',

    `is_threshold_exceeded` UInt8 COMMENT '是否超过阈值,
\r\n\r\n 0 否: 1 是',

    `source_type` String COMMENT '表来源分类,
\r\n\r\n MQ、DB、文件、API',

    `source_sub_type` String COMMENT '表来源实际类型,
\r\n\r\n mysql、kafka、sftp',

    `storage_time` DateTime64(3) COMMENT '实际入库时间'
)
    ENGINE = MergeTree
    PARTITION BY toYYYYMMDD(monitor_time)
    ORDER BY (data_model_id,
              config_id,
              monitor_time)
    SETTINGS index_granularity = 8192
    COMMENT '监控波动表';

CREATE TABLE IF NOT EXISTS t_monitor_ods_lag
(

    `id` Int64,

    `data_model_id` Int32 COMMENT '贴源库id(元数据id)',

    `data_model_name` String COMMENT '贴源库名称(元数据名称)',

    `config_id` Int32 COMMENT '配置id',

    `monitor_time` DateTime64(3),

    `offset` Int64 COMMENT '结束时的数据量',

    `lag` Int64 COMMENT '积压的数量，如果数量为0就说明没有积压',

    `is_lag` Int8 COMMENT '是否发生积压',

    `source_type` String COMMENT '表来源分类',

    `source_sub_type` String COMMENT '表来源实际类型',

    `storage_time` DateTime64(3) COMMENT '实际入库时间'
)
    ENGINE = MergeTree
    PARTITION BY toYYYYMMDD(storage_time)
    PRIMARY KEY id
    ORDER BY (id,
              storage_time)
    SETTINGS index_granularity = 8192;


CREATE MATERIALIZED VIEW IF NOT EXISTS data_access_trace_view
(

    `task_id` Int64,

    `access_time_hour` DateTime,

    `success_count` UInt64,

    `error_count` UInt64,

    `access_count` UInt64
)
ENGINE = MergeTree
PARTITION BY toYYYYMM(access_time_hour)
ORDER BY access_time_hour
SETTINGS index_granularity = 8192 AS
SELECT
    task_id,

    toStartOfHour(access_time) AS access_time_hour,

    count(multiIf(is_error = 0,
                  1,
                  NULL)) AS success_count,

    count(multiIf(is_error = 1,
                  1,
                  NULL)) AS error_count,

    count(1) AS access_count
FROM data_access_trace AS dat
GROUP BY
    task_id,

    toStartOfHour(access_time);


CREATE MATERIALIZED VIEW IF NOT EXISTS data_process_record_view TO data_process_record
(

    `record_id` Int64,

    `msg_title` Nullable(String),

    `operator_count` Nullable(Int64),

    `data_model_id` Nullable(Int64),

    `data_source_name` Nullable(String),

    `storage_time` DateTime,

    `start_time` Nullable(DateTime),

    `end_time` Nullable(DateTime),

    `processing_time` Int64,

    `is_error` Int8,

    `executed_operator_count` Nullable(Int64),

    `tenant_id` Nullable(Int64),

    `pod_ip` Nullable(String)
) AS
SELECT
    recordId AS record_id,

    if(msgTitle = '',
       NULL,
       msgTitle) AS msg_title,

    if(operatorCount = 0,
       NULL,
       operatorCount) AS operator_count,

    if(dataSourceId = 0,
       NULL,
       dataSourceId) AS data_model_id,

    if(dataSourceName = '',
       NULL,
       dataSourceName) AS data_source_name,

    storageTime AS storage_time,

    if(startTime = toDateTime('1970-01-01 00:00:00'),
       NULL,
       startTime) AS start_time,

    if(endTime = toDateTime('1970-01-01 00:00:00'),
       NULL,
       endTime) AS end_time,

    processingTime AS processing_time,

    isError AS is_error,

    if(executedOperatorCount = 0,
       NULL,
       executedOperatorCount) AS executed_operator_count,

    if(tenantId = 0,
       NULL,
       tenantId) AS tenant_id,

    if(podIp = '',
       NULL,
       podIp) AS pod_ip
FROM data_process_record_kafka_engine;



CREATE MATERIALIZED VIEW IF NOT EXISTS kafka_access_view TO data_access_trace
(

    `record_id` Int64,

    `batch_no` Nullable(String),

    `task_id` Int64,

    `access_time` DateTime,

    `is_error` UInt8,

    `error_message` Nullable(String),

    `data` Nullable(String)
) AS
SELECT
    record_id,

    if(batch_no = '',
       NULL,
       batch_no) AS batch_no,

    task_id,

    access_time,

    is_error,

    if(error_message = '',
       NULL,
       error_message) AS error_message,

    if(data = '',
       NULL,
       data) AS data
FROM data_access_trace_kafka_engine;

CREATE MATERIALIZED VIEW IF NOT EXISTS kafka_tracer_view TO data_process_trace
(

    `record_id` Int64,

    `process_id` Int64,

    `receive_mq_type` Nullable(String),

    `receive_mq_path` Nullable(String),

    `receive_group` Nullable(String),

    `receive_topic` Nullable(String),

    `msg_title` Nullable(String),

    `data_source_id` Nullable(Int64),

    `table_id` Nullable(Int64),

    `data_source_name` Nullable(String),

    `data_model_id` Nullable(Int64),

    `msg_content` Nullable(String),

    `results` Nullable(String),

    `storage_time` DateTime,

    `start_time` Nullable(DateTime),

    `end_time` Nullable(DateTime),

    `processing_time` Int64,

    `node_name` Nullable(String),

    `service_name` Nullable(String),

    `parent_processing_node` Nullable(String),

    `processing_name` Nullable(String),

    `processing_type` Nullable(String),

    `operator_id` Nullable(Int64),

    `node_order` Int64,

    `is_error` Int8,

    `error_msg` Nullable(String),

    `store_info` Nullable(String),

    `context_info` Nullable(String),

    `master_node` Nullable(String),

    `tenant_id` Nullable(Int64),

    `pod_ip` Nullable(String)
) AS
SELECT
    recordId AS record_id,

    processId AS process_id,

    if(receiveMqType = '',
       NULL,
       receiveMqType) AS receive_mq_type,

    if(receiveMqPath = '',
       NULL,
       receiveMqPath) AS receive_mq_path,

    if(receiveGroup = '',
       NULL,
       receiveGroup) AS receive_group,

    if(receiveTopic = '',
       NULL,
       receiveTopic) AS receive_topic,

    if(msgTitle = '',
       NULL,
       msgTitle) AS msg_title,

    if(dataSourceId = 0,
       NULL,
       dataSourceId) AS data_source_id,

    if(tableId = 0,
       NULL,
       tableId) AS table_id,

    if(dataSourceName = '',
       NULL,
       dataSourceName) AS data_source_name,

    if(metadataId = 0,
       NULL,
       metadataId) AS data_model_id,

    if(msgContent = '',
       NULL,
       msgContent) AS msg_content,

    if(results = '',
       NULL,
       results) AS results,

    storageTime AS storage_time,

    if(startTime = toDateTime('1970-01-01 00:00:00'),
       NULL,
       startTime) AS start_time,

    if(endTime = toDateTime('1970-01-01 00:00:00'),
       NULL,
       endTime) AS end_time,

    processingTime AS processing_time,

    if(nodeName = '',
       NULL,
       nodeName) AS node_name,

    if(serviceName = '',
       NULL,
       serviceName) AS service_name,

    if(parentProcessingNode = '',
       NULL,
       parentProcessingNode) AS parent_processing_node,

    if(processingName = '',
       NULL,
       processingName) AS processing_name,

    if(processingType = '',
       NULL,
       processingType) AS processing_type,

    if(operatorId = 0,
       NULL,
       operatorId) AS operator_id,

    nodeOrder AS node_order,

    isError AS is_error,

    if(errorMsg = '',
       NULL,
       errorMsg) AS error_msg,

    if(storeInfo = '',
       NULL,
       storeInfo) AS store_info,

    if(contextInfo = '',
       NULL,
       contextInfo) AS context_info,

    if(masterNode = '',
       NULL,
       masterNode) AS master_node,

    if(tenantId = 0,
       NULL,
       tenantId) AS tenant_id,

    if(podIp = '',
       NULL,
       podIp) AS pod_ip
FROM data_process_trace_kafka_engine;

CREATE VIEW IF NOT EXISTS task_schedule_batch_view
            (

             `task_id` Int32,

             `name` Nullable(String),

             `dispatch_num` UInt64,

             `dispatch_time` DateTime,

             `error_count` UInt64,

             `normal_count` UInt64,

             `mode` String
                ) AS
SELECT
    task_id,

    visitParamExtractString(JSONExtractRaw(storage_job_config,
                                           'env'),
                            'job.name') AS name,

    count(1) AS dispatch_num,

    toStartOfDay(start_time) AS dispatch_time,

    count(multiIf((execution_status = 'FAILED') OR (execution_status = 'FAILING') OR (execution_status = 'CANCELED'),
                  1,
                  NULL)) AS error_count,

    count(multiIf((execution_status = 'RUNNING') OR (execution_status = 'FINISHED'),
                  1,
                  NULL)) AS normal_count,

    multiIf((visitParamExtractString(JSONExtractRaw(storage_job_config,
                                                    'source'),
                                     'plugin_name') = 'SftpFile') OR (visitParamExtractString(JSONExtractRaw(storage_job_config,
                                                                                                             'source'),
                                                                                              'plugin_name') = 'Ftpfile'),
            'file',
            (visitParamExtractString(JSONExtractRaw(storage_job_config,
                                                    'source'),
                                     'plugin_name') = 'Jdbc') OR (visitParamExtractString(JSONExtractRaw(storage_job_config,
                                                                                                         'source'),
                                                                                          'plugin_name') = 'Elasticsearch') OR (visitParamExtractString(JSONExtractRaw(storage_job_config,
                                                                                                                                                                       'source'),
                                                                                                                                                        'plugin_name') = 'Clickhouse') OR (visitParamExtractString(JSONExtractRaw(storage_job_config,
                                                                                                                                                                                                                                  'source'),
                                                                                                                                                                                                                   'plugin_name') = 'Hive') OR (visitParamExtractString(JSONExtractRaw(storage_job_config,
                                                                                                                                                                                                                                                                                       'source'),
                                                                                                                                                                                                                                                                        'plugin_name') = 'Oracle'),
            'db',
            visitParamExtractString(JSONExtractRaw(storage_job_config,
                                                   'source'),
                                    'plugin_name') = 'Http',
            'api',
            (visitParamExtractString(JSONExtractRaw(storage_job_config,
                                                    'source'),
                                     'plugin_name') = 'Kafka') OR (visitParamExtractString(JSONExtractRaw(storage_job_config,
                                                                                                          'source'),
                                                                                           'plugin_name') = 'RocketMQ'),
            'mq',
            'other') AS mode
FROM task_schedule_batch AS tsb
GROUP BY
    task_id,

    name,

    toStartOfDay(start_time),

    mode;