DROP TABLE IF EXISTS basic_component_detection;
CREATE TABLE basic_component_detection
(

    `component_name` String COMMENT '组件名称',

    `error_message` Int64 COMMENT '异常信息',

    `status` Int64 COMMENT '本次检测状态',

    `detection_time` DateTime COMMENT '检测时间'
)
    ENGINE = MergeTree
    PARTITION BY toYYYYMMDD(detection_time)
    ORDER BY (detection_time,
              component_name)
    SETTINGS index_granularity = 8192;