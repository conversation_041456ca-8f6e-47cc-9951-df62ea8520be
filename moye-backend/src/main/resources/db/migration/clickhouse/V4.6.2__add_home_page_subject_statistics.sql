DROP TABLE IF EXISTS home_page_subject_statistics;

CREATE TABLE home_page_subject_statistics
(

    `data_model_id` Int64 COMMENT '建模id',

    `data_model_name` String COMMENT '建模名字',

    `total_count` Int64 COMMENT '总数量',

    `success_count` Int64 COMMENT '成功数量',

    `fail_count` Int64 COMMENT '失败数量',

    `time` Date COMMENT '时间(年月日)',

    `type` String COMMENT '批处理任务数量BATCH,流处理处理数量STREAM,存储STORAGE',

    `operator_count` Int64 COMMENT '算子执行总数',

    `sync_time` DateTime COMMENT '同步时间',

    `storage_id` Nullable(Int64) COMMENT '存储点id'
)
    ENGINE = MergeTree
    PARTITION BY toYYYYMMDD(time)
    ORDER BY (time,
              data_model_id)
    SETTINGS index_granularity = 8192;