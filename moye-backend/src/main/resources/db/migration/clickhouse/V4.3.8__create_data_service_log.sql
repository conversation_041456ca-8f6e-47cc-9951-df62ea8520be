drop table if exists data_service_log;
create table if not exists data_service_log
(
    log_id             Int64 comment 'id',
    service_name       Nullable(String) comment '服务名称',
    service_id         Int64 comment '数据服务id',
    data_model_id      Int64 comment '数据建模ID',
    business_name      Nullable(String) comment '业务名称',
    db_type            String comment '使用的数据库类型',
    storage_name       String comment '存储点名称',
    request_parameters Nullable(String) comment '请求参数，业务',
    request_time       DateTime64(3) comment '请求时间',
    response_status    Nullable(String) comment '响应状态（成功(SUCCESS)或失败(FAILURE)）',
    response_duration  Nullable(Int64) comment '响应时长（毫秒）',
    raw_message        String,
    read_error         String
    )
    engine = MergeTree PARTITION BY toYYYYMMDD(request_time)
    PRIMARY KEY log_id
    ORDER BY (log_id, request_time)
    SETTINGS index_granularity = 8192;


-- 1. 创建消费Kafka的引擎表（需先安装kafka引擎）
CREATE TABLE if not exists  data_service_log_kafka_engine
(
    serviceId          Int32,
    logId              UInt64,       -- 类型改为UInt64（原UInt256不合法）
    serviceName        Nullable(String),
    dataModelId        Int32,
    businessName       Nullable(String),
    requestParameters  String,
    requestTime        DateTime64(3), -- 必须指定精度
    responseStatus     Nullable(String),
    responseDuration   Nullable(Int64),
    dbType             String,
    storageName        String,

    -- 元数据字段（保持原始名称）
    _raw_message String,
    _error String
)
    ENGINE = Kafka
SETTINGS
    kafka_broker_list = '***************:19092',
    kafka_topic_list = 'moye-test-data-service-log-topic',
    kafka_group_name = 'clickhouse_consumer_group',
    kafka_format = 'JSONEachRow',
    kafka_row_delimiter = '\n',
    kafka_skip_broken_messages = 20000,
    kafka_num_consumers = 2,
    kafka_handle_error_mode = 'stream';




DROP TABLE IF EXISTS data_service_log_mv;
CREATE MATERIALIZED VIEW if not exists data_service_log_mv
TO data_service_log  -- 目标表结构保持不变
AS
SELECT
    logId          AS log_id,
    serviceId      AS service_id,
    serviceName    AS service_name,
    dataModelId    AS data_model_id,
    businessName   AS business_name,
    dbType         AS db_type,
    storageName    AS storage_name,
    requestParameters AS request_parameters,
    requestTime    AS request_time,
    responseStatus AS response_status,
    responseDuration AS response_duration
FROM data_service_log_kafka_engine
WHERE _error = '';  -- 过滤错误数据
