-- moye_v4.data_access_trace_kafka_engine definition

DROP TABLE IF EXISTS data_access_trace_kafka_engine;

CREATE TABLE data_access_trace_kafka_engine
(

    `record_id` Int64,

    `batch_no` Nullable(String),

    `data_model_id` Int64,

    `access_time` DateTime,

    `is_error` UInt8,

    `error_message` Nullable(String),

    `data` Nullable(String),

    `storage_name` Nullable(String)
)
    ENGINE = Kafka
    SETTINGS kafka_broker_list = '${kafkaBootStrapServers}',
 kafka_topic_list = 'moye_access_data',
 kafka_group_name = 'moye_access_data_group',
 kafka_format = 'JSONStringsEachRow',
 kafka_skip_broken_messages = 0,
 kafka_num_consumers = 1;

DROP TABLE IF EXISTS data_process_trace_kafka_engine;

CREATE TABLE IF NOT EXISTS data_process_trace_kafka_engine
(

    `recordId` Int64,

    `processId` Int64,

    `receiveMqType` String,

    `receiveMqPath` String,

    `receiveGroup` String,

    `receiveTopic` String,

    `msgTitle` String,

    `dataSourceId` Int64,

    `tableId` Int64,

    `dataSourceName` String,

    `metadataId` Int64,

    `msgContent` String,

    `results` String,

    `storageTime` DateTime,

    `startTime` DateTime,

    `endTime` DateTime,

    `processingTime` Int64,

    `nodeName` String,

    `serviceName` String,

    `parentProcessingNode` String,

    `processingName` String,

    `processingType` String,

    `operatorId` Int64,

    `nodeOrder` Int64,

    `isError` Int8,

    `errorMsg` String,

    `storeInfo` String,

    `contextInfo` String,

    `masterNode` String,

    `tenantId` Int64,

    `podIp` String
)
    ENGINE = Kafka
    SETTINGS kafka_broker_list = '${kafkaBootStrapServers}',
 kafka_topic_list = 'moye_tracer_msg_topic',
 kafka_group_name = 'moye_tracer_msg_group',
 kafka_format = 'JSONStringsEachRow',
 kafka_skip_broken_messages = 20000,
 kafka_num_consumers = 1;

DROP TABLE IF EXISTS data_process_record_kafka_engine;

CREATE TABLE IF NOT EXISTS data_process_record_kafka_engine
(

    `recordId` Int64,

    `msgTitle` String,

    `operatorCount` Int64,

    `dataSourceId` Int64,

    `dataSourceName` String,

    `storageTime` DateTime,

    `startTime` DateTime,

    `endTime` DateTime,

    `processingTime` Int64,

    `isError` Int8,

    `executedOperatorCount` Int64,

    `tenantId` Int64,

    `podIp` String
)
    ENGINE = Kafka
    SETTINGS kafka_broker_list = '${kafkaBootStrapServers}',
 kafka_topic_list = 'moye_record_msg_topic',
 kafka_group_name = 'moye_record_msg_group',
 kafka_format = 'JSONStringsEachRow',
 kafka_skip_broken_messages = 20000,
 kafka_num_consumers = 1;
