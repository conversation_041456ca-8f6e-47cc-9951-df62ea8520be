CREATE TABLE if not exists  service_statistics_kafka
(
    id UInt64,
    code String,
    name String,
    execution_time Int32,
    storage_time DateTime,
    is_error UInt8,
    error_msg String
)
    ENGINE = Kafka
SETTINGS kafka_broker_list = 'kafka-svc:9092',
         kafka_topic_list = 'data-service-statistic',
         kafka_group_name = 'service_statistics_consumer',
         kafka_format = 'JSONEachRow',
         kafka_handle_error_mode = 'stream';


CREATE TABLE if not exists service_statistics
(
    id UInt64,
    code String,
    name String,
    execution_time Int32,
    storage_time DateTime,
    is_error UInt8,
    error_msg String,
    raw_message             Nullable(String),
    read_error             Nullable(String)
)
    ENGINE = MergeTree()
ORDER BY (storage_time, id);


CREATE MATERIALIZED VIEW if not exists service_statistics_mv TO service_statistics AS
SELECT
    id,
    code,
    name,
    execution_time,
    storage_time,
    is_error,
    error_msg,
    _raw_message as raw_message,
    _error as read_error
FROM service_statistics_kafka;