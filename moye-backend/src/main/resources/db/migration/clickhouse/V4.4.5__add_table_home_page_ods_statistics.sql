CREATE TABLE IF NOT EXISTS home_page_ods_schedule_statistics
(

    `data_model_id` Int64 COMMENT '建模id',

    `data_model_name` String COMMENT '建模名字',

    `access_type` String COMMENT '接入方式',

    `total_count` Int64 COMMENT '总数量',

    `success_count` Int64 COMMENT '成功数量',

    `fail_count` Int64 COMMENT '失败数量',

    `time` Date COMMENT '时间(年月日)',

    `sync_time` DateTime COMMENT '同步时间'
)
    ENGINE = MergeTree
    PARTITION BY toYYYYMMDD(time)
    ORDER BY (time,
              data_model_id)
    SETTINGS index_granularity = 8192;



CREATE TABLE IF NOT EXISTS home_page_ods_storage_statistics
(

    `data_model_id` Int64 COMMENT '建模id',

    `data_model_name` String COMMENT '建模名字',

    `storage_name` String COMMENT '存储点',

    `total_count` Int64 COMMENT '总数量',

    `success_count` Int64 COMMENT '成功数量',

    `fail_count` Int64 COMMENT '失败数量',

    `time` Date COMMENT '时间(年月日)',

    `sync_time` DateTime COMMENT '同步时间'
)
    ENGINE = MergeTree
    PARTITION BY toYYYYMMDD(time)
    ORDER BY (time,
              data_model_id)
    SETTINGS index_granularity = 8192;