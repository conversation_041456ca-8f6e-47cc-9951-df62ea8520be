
-- 人物
CREATE TABLE IF NOT EXISTS `entity_aity_person_510101` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(190) DEFAULT NULL,
  `sex` varchar(190) DEFAULT NULL,
  `company` varchar(190) DEFAULT NULL,
  `post` varchar(190) DEFAULT NULL,
  `level` varchar(190) DEFAULT NULL,
  `category` varchar(190) DEFAULT NULL,
  `sensitiveLevel` int DEFAULT NULL,
  `birthday` varchar(190) DEFAULT NULL,
  `education` varchar(190) DEFAULT NULL,
  `spouse` varchar(190) DEFAULT NULL,
  `censusRegister` varchar(190) DEFAULT NULL,
  `workArea` varchar(190) DEFAULT NULL,
  `excludeWord` varchar(190) DEFAULT NULL,
  `remark` varchar(190) DEFAULT NULL,
  `alias` varchar(190) DEFAULT NULL,
  `areaCode` varchar(190) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `entity_aity_person_510101_backup` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(190) DEFAULT NULL,
  `sex` varchar(190) DEFAULT NULL,
  `company` varchar(190) DEFAULT NULL,
  `post` varchar(190) DEFAULT NULL,
  `level` varchar(190) DEFAULT NULL,
  `category` varchar(190) DEFAULT NULL,
  `sensitiveLevel` int DEFAULT NULL,
  `birthday` varchar(190) DEFAULT NULL,
  `education` varchar(190) DEFAULT NULL,
  `spouse` varchar(190) DEFAULT NULL,
  `censusRegister` varchar(190) DEFAULT NULL,
  `workArea` varchar(190) DEFAULT NULL,
  `excludeWord` varchar(190) DEFAULT NULL,
  `remark` varchar(190) DEFAULT NULL,
  `alias` varchar(190) DEFAULT NULL,
  `areaCode` varchar(190) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 机构
CREATE TABLE IF NOT EXISTS `entity_aity_org_510101` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(190) DEFAULT NULL,
  `alias` varchar(190) DEFAULT NULL,
  `area` varchar(190) DEFAULT NULL,
  `type1` varchar(190) DEFAULT NULL,
  `type2` varchar(190) DEFAULT NULL,
  `nature` varchar(190) DEFAULT NULL,
  `sensitiveLevel` int DEFAULT NULL,
  `excludeWord` varchar(190) DEFAULT NULL,
  `address` varchar(190) DEFAULT NULL,
  `type3` varchar(190) DEFAULT NULL,
  `remark` varchar(190) DEFAULT NULL,
  `website` varchar(190) DEFAULT NULL,
  `areaCode` varchar(190) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `entity_aity_org_510101_backup` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(190) DEFAULT NULL,
  `alias` varchar(190) DEFAULT NULL,
  `area` varchar(190) DEFAULT NULL,
  `type1` varchar(190) DEFAULT NULL,
  `type2` varchar(190) DEFAULT NULL,
  `nature` varchar(190) DEFAULT NULL,
  `sensitiveLevel` int DEFAULT NULL,
  `excludeWord` varchar(190) DEFAULT NULL,
  `address` varchar(190) DEFAULT NULL,
  `type3` varchar(190) DEFAULT NULL,
  `remark` varchar(190) DEFAULT NULL,
  `website` varchar(190) DEFAULT NULL,
  `areaCode` varchar(190) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- 地域
CREATE TABLE IF NOT EXISTS `entity_aity_place_510101` (
  `id` int NOT NULL AUTO_INCREMENT,
  `alias` varchar(190) DEFAULT NULL,
  `parentArea` varchar(190) DEFAULT NULL,
  `areaCode` varchar(190) DEFAULT NULL,
  `level` varchar(190) DEFAULT NULL,
  `sensitiveLevel` int DEFAULT NULL,
  `excludeWord` varchar(190) DEFAULT NULL,
  `remark` varchar(190) DEFAULT NULL,
  `area` varchar(190) DEFAULT NULL,
  `context` varchar(190) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `entity_aity_place_510101_backup` (
  `id` int NOT NULL AUTO_INCREMENT,
  `alias` varchar(190) DEFAULT NULL,
  `parentArea` varchar(190) DEFAULT NULL,
  `areaCode` varchar(190) DEFAULT NULL,
  `level` varchar(190) DEFAULT NULL,
  `sensitiveLevel` int DEFAULT NULL,
  `excludeWord` varchar(190) DEFAULT NULL,
  `remark` varchar(190) DEFAULT NULL,
  `area` varchar(190) DEFAULT NULL,
  `context` varchar(190) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;