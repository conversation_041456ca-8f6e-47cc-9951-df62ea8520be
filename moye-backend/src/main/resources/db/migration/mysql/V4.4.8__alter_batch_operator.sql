DROP TABLE IF EXISTS `batch_operator`;
DROP TABLE IF EXISTS `batch_arranged_operator`;
CREATE TABLE `batch_operator` (
                                  `id` int NOT NULL AUTO_INCREMENT,
                                  `arrangement_id` int NOT NULL COMMENT '编排id',
                                  `data_model_id` int NOT NULL COMMENT '数据建模id',
                                  `canvas` text COMMENT '画布信息',
                                  `display_id` bigint COMMENT '算子编排中算子顺序（前端生成）',
                                  `target_display_ids` text COMMENT '子节点id',
                                  `parent_display_ids` text COMMENT '父节点id（order字段），json数组',
                                  `type` varchar(100) COMMENT '节点类型: TABLE/OPERATOR',
                                  `name` varchar(255) COMMENT '算子名称',
                                  `desc` text COMMENT '描述',
                                  `enabled` tinyint DEFAULT 1 COMMENT '是否启用',
                                  `conditions` text COMMENT '条件',
                                  `input_fields` text COMMENT '输入字段',
                                  `output_fields` text COMMENT '输出字段',
                                  `input_bind` text COMMENT '输入绑定',
                                  `output_bind` text COMMENT '输出绑定',
                                  `ability_id` int COMMENT '能力id',
                                  `table_type` VARCHAR(50) COMMENT 'TABLE类型算子的建模分层',
                                  `table_data_model_id` int COMMENT 'TABLE类型算子的数据建模id',
                                  `table_storage_id` int COMMENT 'TABLE类型算子的数据存储id',
                                  `table_is_increment` int COMMENT 'TABLE类型算子的是否启用增量',
                                  `output_table_name` varchar(500) COMMENT '输出表名',
                                  `create_by` int,
                                  `create_time` datetime,
                                  `update_by` int,
                                  `update_time` datetime,
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;