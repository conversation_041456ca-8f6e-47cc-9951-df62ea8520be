DELIMITER //
DROP PROCEDURE IF EXISTS upsertBatchAbility //
CREATE PROCEDURE upsertBatchAbility (
    IN p_en_name VARCHAR(255),
    IN p_zh_name VARCHAR(255),
    IN p_description TEXT,
    IN p_input_schema TEXT,
    IN p_output_schema TEXT,
    IN p_input_size INT,
    IN p_output_size INT,
    IN p_field_retention_mode VARCHAR(255),
    IN p_is_batch_condition_supported INT
)
BEGIN
    DECLARE flag INT DEFAULT 0;
    DECLARE batch_type VARCHAR(255) DEFAULT 'BATCH';
    DECLARE batch_path VARCHAR(255) DEFAULT '';
    DECLARE batch_operator_category_id INT DEFAULT 0;
    DECLARE batch_user_id INT DEFAULT 1;
    SET flag = (SELECT COUNT(1) FROM `ability` WHERE `type` = batch_type AND `en_name` = p_en_name);

    IF flag < 1 THEN
        INSERT `ability` (`en_name`, `zh_name`, `description`, `type`, `path`, `operator_category_id`, `input_schema`, `output_schema`, `input_size`, `output_size`, `create_time`, `update_time`, `create_by`, `update_by`, `field_retention_mode`, `is_batch_condition_supported`)
        VALUES (p_en_name, p_zh_name, p_description, batch_type, batch_path, batch_operator_category_id, p_input_schema, p_output_schema, p_input_size, p_output_size, NOW(), NOW(), batch_user_id, batch_user_id, p_field_retention_mode, p_is_batch_condition_supported);
    ELSE
        UPDATE `ability`
        SET `zh_name` = p_zh_name, `description` = p_description, `input_schema` = p_input_schema, `output_schema` = p_output_schema, `input_size` = p_input_size, `output_size` = p_output_size, `update_time` = NOW(), `update_by` = batch_user_id, `field_retention_mode` = p_field_retention_mode, `is_batch_condition_supported` = p_is_batch_condition_supported
        WHERE `type` = batch_type AND `en_name` = p_en_name;
    END IF;
END //
DELIMITER ;

CALL upsertBatchAbility(
        'fieldMapping', '字段映射', '字段映射用于筛选和重命名字段，实现数据字段标准化',
        '{}',
    '{}',
        1, 1, 'CURRENT_ONLY', 0
    );