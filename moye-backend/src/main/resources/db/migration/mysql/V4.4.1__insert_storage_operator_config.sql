INSERT INTO `operator_param_config` ( `operator_id`, `position`, `param_type`, `param_detail`, `cascade_field`, `is_group`, `location`) VALUES (1, 'INPUT', 'SINGLE', '[{\"enName\": \"flatMapSwitch\", \"zhName\": \"拆分字段开关(用于es向量字段)\", \"fieldType\": \"STRING\", \"valueType\": \"CONSTANT\", \"isMultiValue\": false, \"fieldTypeName\": \"字符串\"}]', '[]', 0, NULL);
INSERT INTO `operator_param_config` (`operator_id`, `position`, `param_type`, `param_detail`, `cascade_field`, `is_group`, `location`) VALUES ( 1, 'INPUT', 'SINGLE', '[{\"enName\": \"flatMapFields\", \"zhName\": \"拆分字段\", \"fieldType\": \"STRING\", \"valueType\": \"CONSTANT\", \"isMultiValue\": true, \"fieldTypeName\": \"字符串\"}]', '[]', 0, NULL);
