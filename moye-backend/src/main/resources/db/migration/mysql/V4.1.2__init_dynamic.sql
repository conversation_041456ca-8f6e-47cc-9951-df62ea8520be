CREATE TABLE IF NOT EXISTS moye_v4_dynamic.`人物_510101`
(
    `id`   int NOT NULL AUTO_INCREMENT,
    `姓名`   varchar(190) DEFAULT NULL,
    `性别`   varchar(190) DEFAULT NULL,
    `单位`   varchar(190) DEFAULT NULL,
    `当前职务` varchar(190) DEFAULT NULL,
    `级别`   varchar(190) DEFAULT NULL,
    `分类`   varchar(190) DEFAULT NULL,
    `敏感系数` int          DEFAULT NULL,
    `出生年月` varchar(190) DEFAULT NULL,
    `教育经历` varchar(190) DEFAULT NULL,
    `配偶`   varchar(190) DEFAULT NULL,
    `户籍`   varchar(190) DEFAULT NULL,
    `属地`   varchar(190) DEFAULT NULL,
    `排除词`  varchar(190) DEFAULT NULL,
    `备注`   varchar(190) DEFAULT NULL,
    `别名`   varchar(190) DEFAULT NULL,
    `是否涉外` varchar(190) DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
);

CREATE TABLE IF NOT EXISTS moye_v4_dynamic.`地域_510101`
(
    `id`   int NOT NULL AUTO_INCREMENT,
    `地名`   varchar(190) DEFAULT NULL,
    `别名`   varchar(190) DEFAULT NULL,
    `上级区划` varchar(190) DEFAULT NULL,
    `地区代码` varchar(190) DEFAULT NULL,
    `级别`   varchar(190) DEFAULT NULL,
    `敏感系数` int          DEFAULT NULL,
    `排除词`  varchar(190) DEFAULT NULL,
    `备注`   varchar(190) DEFAULT NULL,
    `是否涉外` varchar(190) DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    KEY `place_name_index` (`地名`) USING BTREE COMMENT '地名索引'
);

CREATE TABLE IF NOT EXISTS moye_v4_dynamic.`媒体账号_510101`
(
    `id`     int NOT NULL AUTO_INCREMENT,
    `媒体`     varchar(190) DEFAULT NULL,
    `舆论场`    varchar(190) DEFAULT NULL,
    `账号/站点名` varchar(190) DEFAULT NULL,
    `所属机构`   varchar(190) DEFAULT NULL,
    `级别`     varchar(190) DEFAULT NULL,
    `地域`     varchar(190) DEFAULT NULL,
    `性质`     varchar(190) DEFAULT NULL,
    `类型`     varchar(190) DEFAULT NULL,
    `舆情重点`   varchar(190) DEFAULT NULL,
    `网宣重点`   varchar(190) DEFAULT NULL,
    `敏感系数`   int          DEFAULT NULL,
    `语言`     varchar(190) DEFAULT NULL,
    `链接`     varchar(250) DEFAULT NULL,
    `地区代码`   varchar(190) DEFAULT NULL,
    `级别顺序`   int          DEFAULT NULL,
    `性质顺序`   int          DEFAULT NULL,
    `平台`     varchar(190) DEFAULT NULL,
    `账号类型`   varchar(190) DEFAULT NULL,
    `有害重点`   varchar(190) DEFAULT NULL,
    `巡查分类`   varchar(190) DEFAULT NULL,
    `备案信息`   varchar(190) DEFAULT NULL,
    `巡查二级分类` varchar(190) DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
);

CREATE TABLE IF NOT EXISTS moye_v4_dynamic.`屏蔽分类_510101`
(
    `id`   int NOT NULL AUTO_INCREMENT,
    `一级分类` varchar(190) DEFAULT NULL,
    `二级分类` varchar(190) DEFAULT NULL,
    `优先级`  int          DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
);

CREATE TABLE IF NOT EXISTS moye_v4_dynamic.`机构_510101`
(
    `id`   int NOT NULL AUTO_INCREMENT,
    `机构名`  varchar(190) DEFAULT NULL,
    `别名`   varchar(190) DEFAULT NULL,
    `属地`   varchar(190) DEFAULT NULL,
    `类型一`  varchar(190) DEFAULT NULL,
    `类型二`  varchar(190) DEFAULT NULL,
    `类型三`  varchar(255) DEFAULT NULL,
    `性质`   varchar(190) DEFAULT NULL,
    `敏感系数` int          DEFAULT NULL,
    `排除词`  varchar(190) DEFAULT NULL,
    `地址`   varchar(190) DEFAULT NULL,
    `是否涉外` varchar(190) DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    KEY `org_name_index` (`机构名`) USING BTREE,
    KEY `org_nickname_index` (`别名`) USING BTREE
);

CREATE TABLE IF NOT EXISTS moye_v4_dynamic.`标签库_政策文件人员标签_510101`
(
    `id`     int NOT NULL AUTO_INCREMENT,
    `关键词`    varchar(190) DEFAULT NULL COMMENT '关键词',
    `分类`     varchar(190) DEFAULT NULL,
    `标签`     varchar(190) DEFAULT NULL,
    `标题匹配频次` int          DEFAULT NULL,
    `内容匹配频次` int          DEFAULT NULL,
    `段落匹配频次` int          DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
);

CREATE TABLE IF NOT EXISTS moye_v4_dynamic.`职能机构_510101`
(
    `id` int NOT NULL AUTO_INCREMENT,
    `名称` varchar(190) DEFAULT NULL COMMENT '机构的名称',
    `类型` varchar(190) DEFAULT NULL COMMENT '机构的类型',
    PRIMARY KEY (`id`) USING BTREE
);

CREATE TABLE IF NOT EXISTS moye_v4_dynamic.`行政区划_510101`
(
    `id` int NOT NULL AUTO_INCREMENT,
    `编号` varchar(190) DEFAULT NULL,
    `名称` varchar(190) DEFAULT NULL,
    `父级` varchar(190) DEFAULT NULL,
    `简称` varchar(190) DEFAULT NULL,
    `等级` int          DEFAULT NULL,
    `排序` int          DEFAULT NULL,
    `全称` varchar(190) DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `全称` (`全称`) USING BTREE
);

CREATE TABLE IF NOT EXISTS moye_v4_dynamic.`预警分类_510101`
(
    `id`   int NOT NULL AUTO_INCREMENT,
    `一级分类` varchar(190) DEFAULT NULL,
    `二级分类` varchar(190) DEFAULT NULL,
    `关键词`  text,
    `优先级`  int          DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    KEY `classify_class1_index` (`一级分类`) USING BTREE,
    KEY `classify_class2_index` (`二级分类`) USING BTREE
);

