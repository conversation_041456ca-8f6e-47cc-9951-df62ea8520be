UPDATE `role`
SET operations='frontend-config,home-page,home-page-view,data-source,data-source-list,data-source-add,data-source-connection-test,data-source-connection-edit,data-source-connection-delete,data-storage,data-storage-list,data-storage-add,data-storage-connection-test,data-storage-connection-view,data-storage-connection-edit,data-storage-connection-delete,data-standard,field-standard,field-standard-view,field-standard-category-add,field-standard-category-edit,field-standard-category-delete,field-standard-export,field-standard-import,field-standard-add,field-standard-edit,field-standard-delete,field-standard-detail,meta-standard,meta-standard-view,meta-standard-category-add,meta-standard-category-edit,meta-standard-category-delete,meta-standard-add,meta-standard-edit,meta-standard-delete,ability-center,ability-center-read,ability-center-add,ability-center-category-edit,ability-center-category-delete,ability-center-operator-add,ability-center-operator-edit,ability-center-delete,ability-center-operator-test,ability-center-batch-operation,data-modeling,data-modeling-read,data-modeling-category-add,data-modeling-category-edit,data-modeling-category-delete,data-modeling-ods-add,data-modeling-ods-edit,data-modeling-ods-immediately-run,data-modeling-ods-enable,data-modeling-ods-publish,data-modeling-ods-delete,data-modeling-dwd-add,data-modeling-dwd-edit,data-modeling-dwd-immediately-run,data-modeling-dwd-enable,data-modeling-dwd-publish,data-modeling-dwd-delete,data-modeling-theme-add,data-modeling-theme-edit,data-modeling-theme-immediately-run,data-modeling-theme-enable,data-modeling-theme-publish,data-modeling-theme-delete,data-modeling-subject-add,data-modeling-subject-edit,data-modeling-subject-immediately-run,data-modeling-subject-enable,data-modeling-subject-publish,data-modeling-subject-delete,data-modeling-reverse-modeling,data-modeling-view-add,data-modeling-batch,data-service,data-service-view,data-service-category-add,data-service-category-edit,data-service-category-delete,data-service-add,data-service-edit,data-service-publish,data-service-delete,backend-config,user-config,user-config-view,user-config-dept-add,user-config-dept-edit,user-config-dept-delete,user-config-user-add,user-config-user-edit,user-config-user-delete,user-config-user-detail,user-config-user-enable,user-config-user-reset-password,role-config,role-config-view,role-config-add,role-config-read,role-config-edit,role-config-delete,message-center-backend,all-message,message-center-backend-view,message-center-backend-add,message-center-backend-edit,message-center-backend-delete,message-center-backend-enable,role-config-backend-edit,role-config-backend-enable,role-config-backend-delete,message-push-configuration,message-push-record'
WHERE id = 1;