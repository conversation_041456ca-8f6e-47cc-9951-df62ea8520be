-- moye_v4.knowledge_base definition

CREATE TABLE  IF NOT EXISTS `knowledge_base` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type` varchar(20) NOT NULL COMMENT '知识库类型：ENTITY("实体")，TAG("标签")，COMPOUND("复合")',
  `zh_name` varchar(255) NOT NULL COMMENT '实体中文名称',
  `en_name` varchar(255) NOT NULL COMMENT '实体英文名称',
  `is_sys_source` bit(1) DEFAULT b'0' COMMENT '判断是否是系统资源',
  `recent_backup_time` datetime DEFAULT NULL COMMENT '最近备份时间',
  `is_backup` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否备份',
  `create_by` int DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_by` int DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- moye_v4.knowledge_base_field definition

CREATE TABLE  IF NOT EXISTS `knowledge_base_field` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `base_id` int NOT NULL COMMENT '知识库id',
  `zh_name` varchar(255) NOT NULL COMMENT '属性中文名',
  `en_name` varchar(255) DEFAULT NULL COMMENT '属性英文名',
  `type` varchar(20) NOT NULL COMMENT '属性类型',
  `type_name` varchar(255) DEFAULT NULL COMMENT '属性名称',
  `is_unique` bit(1) NOT NULL COMMENT '是否唯一',
  `is_multi_value` bit(1) NOT NULL COMMENT '是否唯一',
  `advance_config` json DEFAULT NULL COMMENT '高级配置',
  `description` varchar(255) DEFAULT NULL COMMENT '属性注释',
  `create_by` int DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_by` int DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;