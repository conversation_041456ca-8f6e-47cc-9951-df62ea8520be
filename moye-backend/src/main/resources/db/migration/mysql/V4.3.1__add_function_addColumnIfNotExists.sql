DELIMITER //

CREATE PROCEDURE IF NOT EXISTS addColumnIfNotExists(
    IN tableName VARCHAR(255),
    IN columnName VARCHAR(255),
    IN sqlStatement TEXT
)
BEGIN
    DECLARE columnExists INT DEFAULT 0;

    -- 检查字段是否存在
    SELECT COUNT(*)
    INTO columnExists
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = tableName
      AND COLUMN_NAME = columnName;

    -- 如果字段不存在，则准备并执行 SQL 语句
    IF columnExists = 0 THEN
        SET @stmt = sqlStatement;
        PREPARE stmt FROM @stmt;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END //

DELIMITER ;