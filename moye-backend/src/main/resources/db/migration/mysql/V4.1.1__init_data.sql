INSERT IGNORE INTO business_category (id, zh_name, en_name, description, create_by, update_by, update_time, create_time) VALUES(1,'默认分类', 'default_category', '', 1, 1, now(), now());

INSERT IGNORE INTO data_service_category (id, name, create_by, update_by, create_time, update_time, pid, description) VALUES(1,'默认分类', 1, 1, now(), now(), 0, '');

INSERT IGNORE INTO field_standard_category (id, create_time, create_by, update_time, update_by, name, pid) VALUES(1,now(), 1, now(), 1, '默认分类', 0);

INSERT IGNORE INTO meta_data_standard_catalog (id, name, description, pid, create_by, create_time, update_by, update_time) VALUES(1,'默认分类', '', 0, '1', now(), '1', now());

INSERT IGNORE INTO operator_business_category (id, create_by, create_time, update_by, update_time, en_abbr, zh_name, description) VALUES(1,1, now(), 1, now(), 'general', '通用算子', '通用算子');
INSERT IGNORE INTO operator_business_category (id, create_by, create_time, update_by, update_time, en_abbr, zh_name, description) VALUES(2,1, now(), 1, now(), 'nlp', 'NLP', 'NLP算子');
INSERT IGNORE INTO operator_business_category (id, create_by, create_time, update_by, update_time, en_abbr, zh_name, description) VALUES(3,1, now(), 1, now(), 'cv', 'CV', 'CV算子');

INSERT IGNORE INTO data_standard_field (id, category_id, zh_name, en_name, `type`, type_name, description, is_multi_value, is_nullable, is_statistic, is_built_in, advance_config, create_by, create_time, update_by, update_time, default_value) VALUES(1,1, '数据中台入库时间', 'trs_moye_input_time', 'DATETIME', '日期时间', '数据中台内部专用字段', 0, 1, 0, 1, '{"type": "DATETIME", "maxValue": "4102416000999", "minValue": "631123200000", "enumValues": []}', 1, now(), 1, now(), NULL);
INSERT IGNORE INTO data_standard_field (id, category_id, zh_name, en_name, `type`, type_name, description, is_multi_value, is_nullable, is_statistic, is_built_in, advance_config, create_by, create_time, update_by, update_time, default_value) VALUES(2,1, '数据中台批次号', 'trs_moye_batch_no', 'STRING', '字符串', '数据中台内部专用字段', 0, 1, 0, 1, '{"type": "STRING", "enumValues": []}', 1, now(), 1, now(), NULL);
INSERT IGNORE INTO data_standard_field (id, category_id, zh_name, en_name, `type`, type_name, description, is_multi_value, is_nullable, is_statistic, is_built_in, advance_config, create_by, create_time, update_by, update_time, default_value) VALUES(3,1, '数据中台任务主键', 'trs_moye_task_id', 'INT', '整型', '数据中台内部专用字段11', 0, 1, 0, 1, '{"type": "INT", "enumValues": []}', 1, now(), 1, now(), NULL);

INSERT IGNORE INTO departments (id, name, pid, create_by, create_time, update_by, update_time) VALUES(1,'拓尔思', 0, 1, now(), 1, now());

INSERT IGNORE INTO `role` (id, name, `type`, operations, credentials, create_time, update_time, create_by, update_by) VALUES(1,'超级管理员', 'SUPER_ADMIN', NULL, NULL, now(), now(), 1, 1);

INSERT IGNORE INTO t_entity (id, zh_name, en_name, color, parent_entity_id, is_sys_source, recent_backup_time, is_backup, tenant_id, field_list, create_by, create_time, update_by, update_time) VALUES(1, '人物', 'aity_person', '#A77121', 0, 0, '2024-03-12 14:46:58', 1, 510101, '[{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":1,"color":"#F99605","description":"","enName":"name","extend":false,"id":"1723859676207","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"姓名"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":2,"color":"#85916e","description":"","enName":"sex","extend":false,"id":"1723859676208","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"性别"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":3,"color":"#6ebed5","description":"","enName":"company","extend":false,"id":"1723859676209","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"单位"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":4,"color":"#ee0ba6","description":"","enName":"post","extend":false,"id":"1723859676210","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"当前职务"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":5,"color":"#c45bee","description":"","enName":"level","extend":false,"id":"1723859676211","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"级别"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":6,"color":"#225eb","description":"","enName":"category","extend":false,"id":"1723859676212","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"分类"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":7,"color":"#3e700a","description":"","enName":"sensitiveLevel","extend":false,"id":"1723859676213","parentAttrId":null,"scale":null,"type":"int","typeName":"整型","unique":false,"zhName":"敏感系数"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":8,"color":"#7d1f87","description":"","enName":"birthday","extend":false,"id":"1723859676214","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"出生年月"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":9,"color":"#4766ae","description":"","enName":"education","extend":false,"id":"1723859676215","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"教育经历"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":10,"color":"#4c5dac","description":"","enName":"spouse","extend":false,"id":"1723859676216","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"配偶"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":11,"color":"#7e472f","description":"","enName":"censusRegister","extend":false,"id":"1723859676217","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"户籍"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":12,"color":"#e7b980","description":"","enName":"workArea","extend":false,"id":"1723859676218","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"属地"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":13,"color":"#9968e0","description":"","enName":"excludeWord","extend":false,"id":"1723859676219","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"排除词"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":14,"color":"#50bb69","description":"","enName":"remark","extend":false,"id":"1723859676220","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"备注"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":15,"color":"#a608a8","description":"","enName":"alias","extend":false,"id":"1723859676221","parentAttrId":null,"scale":null,"type":"string","typeName":"","unique":false,"zhName":"别名"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":16,"color":"#4392c3","description":"","enName":"foreign","extend":false,"id":"1723859676222","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"是否涉外"}]', NULL, NULL, NULL, NULL);
INSERT IGNORE INTO t_entity (id, zh_name, en_name, color, parent_entity_id, is_sys_source, recent_backup_time, is_backup, tenant_id, field_list, create_by, create_time, update_by, update_time) VALUES(2, '媒体账号', 'aity_media', '#F99605', 0, 0, '2023-05-30 16:38:34', 0, 510101, '[{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":1,"color":"#F99605","description":"","enName":"media","extend":false,"id":"1723859676223","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"媒体"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":2,"color":"#e3b3a5","description":"","enName":"situation","extend":false,"id":"*************","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"舆论场"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":3,"color":"#F99605","description":"账号或者站点的名称","enName":"account","extend":false,"id":"*************","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"账号/站点名"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":4,"color":"#260f12","description":"","enName":"parentCompany","extend":false,"id":"*************","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"所属机构"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":5,"color":"#745186","description":"","enName":"level","extend":false,"id":"*************","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"级别"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":6,"color":"#6ee37a","description":"","enName":"region","extend":false,"id":"*************","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"地域"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":7,"color":"#dbbaad","description":"","enName":"nature","extend":false,"id":"1723859676229","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"性质"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":8,"color":"#8bc7b4","description":"","enName":"type","extend":false,"id":"1723859676230","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"类型"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":9,"color":"#e07d81","description":"","enName":"personCategory","extend":false,"id":"1723859676231","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"舆情重点"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":10,"color":"#540858","description":"","enName":"pointOrder","extend":false,"id":"1723859676232","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"网宣重点"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":11,"color":"#bf43ce","description":"","enName":"sensitiveLevel","extend":false,"id":"1723859676233","parentAttrId":null,"scale":null,"type":"int","typeName":"整型","unique":false,"zhName":"敏感系数"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":12,"color":"#524323","description":"","enName":"laguage","extend":false,"id":"1723859676234","parentAttrId":null,"scale":null,"type":"string","typeName":"","unique":false,"zhName":"语言"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":13,"color":"#9d50c","description":"","enName":"url","extend":false,"id":"1723859676235","parentAttrId":null,"scale":null,"type":"string","typeName":"字符","unique":false,"zhName":"链接"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":14,"color":"#3a7c87","description":"","enName":"areaCode","extend":false,"id":"1723859676236","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"地区代码"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":15,"color":"#f9bcaa","description":"","enName":"levelOrder","extend":false,"id":"1723859676237","parentAttrId":null,"scale":null,"type":"int","typeName":"整型","unique":false,"zhName":"级别顺序"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":16,"color":"#f615a","description":"","enName":"natureOrder","extend":false,"id":"1723859676238","parentAttrId":null,"scale":null,"type":"int","typeName":"整型","unique":false,"zhName":"性质顺序"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":17,"color":"#94e745","description":"","enName":"dataFrom","extend":false,"id":"*************","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"平台"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":18,"color":"#b9a2e8","description":"","enName":"accountType","extend":false,"id":"*************","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"账号类型"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":19,"color":"#f54ada","description":"","enName":"harmPoint","extend":false,"id":"*************","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"有害重点"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":20,"color":"#7bc541","description":"","enName":"harmType","extend":false,"id":"*************","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"巡查分类"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":21,"color":"#7d3ee3","description":"","enName":"icpCode","extend":false,"id":"1723859676243","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"备案信息"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":22,"color":"#9b9ecf","description":"","enName":"harmType2","extend":false,"id":"1723859676244","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"巡查二级分类"}]', NULL, NULL, NULL, NULL);
INSERT IGNORE INTO t_entity (id, zh_name, en_name, color, parent_entity_id, is_sys_source, recent_backup_time, is_backup, tenant_id, field_list, create_by, create_time, update_by, update_time) VALUES(3, '机构', 'aity_org', '#F3C704', 0, 0, '2023-05-30 15:05:49', 1, 510101, '[{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":1,"color":"#F3C704","description":"","enName":"name","extend":false,"id":"1723859676245","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"机构名"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":2,"color":"#F3C704","description":"","enName":"alias","extend":false,"id":"1723859676246","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"别名"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":3,"color":"#edd592","description":"","enName":"area","extend":false,"id":"1723859676247","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"属地"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":4,"color":"#ea593d","description":"","enName":"type1","extend":false,"id":"1723859676248","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"类型一"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":5,"color":"#b2a63c","description":"","enName":"type2","extend":false,"id":"1723859676249","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"类型二"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":6,"color":"#9a7a1b","description":"","enName":"nature","extend":false,"id":"1723859676250","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"性质"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":7,"color":"#3f680f","description":"","enName":"sensitiveLevel","extend":false,"id":"1723859676251","parentAttrId":null,"scale":null,"type":"int","typeName":"整型","unique":false,"zhName":"敏感系数"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":8,"color":"#ce3dd0","description":"","enName":"excludeWord","extend":false,"id":"1723859676252","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"排除词"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":9,"color":"#8840fa","description":"","enName":"address","extend":false,"id":"1723859676253","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"地址"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":10,"color":"#39eca6","description":"","enName":"type3","extend":false,"id":"1723859676254","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"类型三"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":11,"color":"#fdbdb","description":"","enName":"foreign","extend":false,"id":"1723859676255","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"是否涉外"}]', NULL, NULL, NULL, NULL);
INSERT IGNORE INTO t_entity (id, zh_name, en_name, color, parent_entity_id, is_sys_source, recent_backup_time, is_backup, tenant_id, field_list, create_by, create_time, update_by, update_time) VALUES(4, '地域', 'aity_place', '#1D8F69', 0, 0, '2023-05-30 15:13:31', 1, 510101, '[{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":1,"color":"#1D8F69","description":"","enName":"alias","extend":false,"id":"1723859676256","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"别名"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":2,"color":"#cf9cc3","description":"","enName":"parentArea","extend":false,"id":"1723859676257","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"上级区划"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":3,"color":"#b60ba0","description":"","enName":"areaCode","extend":false,"id":"1723859676258","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"地区代码"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":4,"color":"#165825","description":"","enName":"level","extend":false,"id":"1723859676259","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"级别"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":5,"color":"#ba68d7","description":"","enName":"sensitiveLevel","extend":false,"id":"1723859676260","parentAttrId":null,"scale":null,"type":"int","typeName":"整型","unique":false,"zhName":"敏感系数"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":6,"color":"#635f9d","description":"","enName":"excludeWord","extend":false,"id":"1723859676261","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"排除词"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":7,"color":"#1668ef","description":"","enName":"remark","extend":false,"id":"1723859676262","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"备注"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":8,"color":"#1D8F69","description":"","enName":"area","extend":false,"id":"1723859676263","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"地名"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":9,"color":"#2d908c","description":"","enName":"foreign","extend":false,"id":"1723859676264","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"是否涉外"}]', NULL, NULL, NULL, NULL);
INSERT IGNORE INTO t_entity (id, zh_name, en_name, color, parent_entity_id, is_sys_source, recent_backup_time, is_backup, tenant_id, field_list, create_by, create_time, update_by, update_time) VALUES(5, '预警分类', 'aity_nlpFocusClass', '#F10800', 0, 0, '2022-04-14 17:37:56', 1, 510101, '[{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":1,"color":"#000000","description":"","enName":"class1","extend":false,"id":"1723859676265","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"一级分类"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":2,"color":"#f7815d","description":"","enName":"class2","extend":false,"id":"1723859676266","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"二级分类"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":3,"color":"#F10800","description":"","enName":"keyword","extend":false,"id":"1723859676267","parentAttrId":null,"scale":null,"type":"text","typeName":"长文本","unique":false,"zhName":"关键词"}]', NULL, NULL, NULL, NULL);
INSERT IGNORE INTO t_entity (id, zh_name, en_name, color, parent_entity_id, is_sys_source, recent_backup_time, is_backup, tenant_id, field_list, create_by, create_time, update_by, update_time) VALUES(6, '屏蔽分类', 'aity_nlpSpam', '#0026FF', 0, 0, '2022-04-15 15:13:41', 1, 510101, '[{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":1,"color":"#0026FF","description":"","enName":"class1","extend":false,"id":"1723859676268","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"一级分类"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":2,"color":"#0026FF","description":"","enName":"class2","extend":false,"id":"1723859676269","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"二级分类"}]', NULL, NULL, NULL, NULL);
INSERT IGNORE INTO t_entity (id, zh_name, en_name, color, parent_entity_id, is_sys_source, recent_backup_time, is_backup, tenant_id, field_list, create_by, create_time, update_by, update_time) VALUES(7, '行政区划', 'aity_administrative_division', '#25bbf', 0, 0, NULL, 0, 510101, '[{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":1,"color":"#d6a5e7","description":"","enName":"number","extend":false,"id":"1723859676270","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"编号"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":2,"color":"#4294cf","description":"","enName":"name","extend":false,"id":"1723859676271","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"名称"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":3,"color":"#15401a","description":"","enName":"parent","extend":false,"id":"1723859676272","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"父级"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":4,"color":"#5bc15b","description":"","enName":"abbreviation","extend":false,"id":"1723859676273","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"简称"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":5,"color":"#9fbc86","description":"","enName":"grade","extend":false,"id":"1723859676274","parentAttrId":null,"scale":null,"type":"int","typeName":"整型","unique":false,"zhName":"等级"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":6,"color":"#111191","description":"","enName":"order","extend":false,"id":"1723859676275","parentAttrId":null,"scale":null,"type":"int","typeName":"整型","unique":false,"zhName":"排序"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":7,"color":"#6c7175","description":"","enName":"fullName","extend":false,"id":"1723859676276","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"全称"}]', NULL, NULL, NULL, NULL);
INSERT IGNORE INTO t_entity (id, zh_name, en_name, color, parent_entity_id, is_sys_source, recent_backup_time, is_backup, tenant_id, field_list, create_by, create_time, update_by, update_time) VALUES(8, '职能机构', ' functional_org', '#faecaf', 0, 0, NULL, 0, 510101, '[{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":1,"color":"#361f58","description":"机构的名称","enName":"name","extend":false,"id":"1723859676277","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"名称"},{"accuracy":null,"allowMultiValue":null,"allowNull":null,"attrEnums":[],"attrMax":null,"attrMin":null,"attrOrder":2,"color":"#33968e","description":"机构的类型","enName":"type","extend":false,"id":"*************","parentAttrId":null,"scale":null,"type":"string","typeName":"字符串","unique":false,"zhName":"类型"}]', NULL, NULL, NULL, NULL);


-- ----------------------------
-- 用户名 admin  密码  trsadmin@1234
-- ----------------------------
INSERT IGNORE INTO users (id, name, `type`, account, password, department_id, telephone, email, is_enable, login_failed_num, pwd_update_time, create_by, update_by, update_time, create_time, role_ids) VALUES(1, '系统管理员', 'ENTERPRISE', 'admin', '9ca45952fd2406f00df1fbb8807625ed', 1, '', '', 1, 0, now(), 1, '1', now(), now(), '[1]');

INSERT IGNORE INTO operator
(id, create_by, create_time, update_by, update_time, operator_category_id, en_name, zh_name, description, icon_name, update_status, test_status, call_type, call_params)
VALUES(1, 1, now(), 1, now(), 1, 'doSeaTunnelStorage', 'seaTunnel存储', '通过seatunnel存储数据', 'ability-default', 'UPDATED', 'UNTESTED', 'LOCAL', '{"callType": "LOCAL", "callFunctionName": "doSeaTunnelStorage", "callFunctionPath": "com.trs.ai.ty.engine.operator.service.OperatorService"}');

INSERT IGNORE INTO operator_param_config
(id, operator_id, `position`, param_type, param_detail, cascade_field, is_group)
VALUES(1, 1, 'INPUT', 'SINGLE', '[{"enName": "inputDbType", "zhName": "数据库类型", "fieldType": "DB_TYPE", "valueType": "CONSTANT", "isMultiValue": false}]', NULL, 0);
INSERT IGNORE INTO operator_param_config
(id, operator_id, `position`, param_type, param_detail, cascade_field, is_group)
VALUES(2, 1, 'INPUT', 'SINGLE', '[{"enName": "storageId", "zhName": "存储id", "fieldType": "INT", "valueType": "CONSTANT", "isMultiValue": false, "fieldTypeName": "整型"}]', '[]', 0);
