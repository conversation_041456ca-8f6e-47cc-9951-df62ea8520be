CREATE TABLE IF NOT EXISTS `data_model_indicator_period_config`
(
    `period_type` varchar(50) NOT NULL COMMENT '周期类型',
    `config`      json        NOT NULL COMMENT 'JSON配置（包含统计范围和触发规则）',
    `update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
    PRIMARY KEY (`period_type`)
    ) COMMENT = '数据建模指标周期配置表';


INSERT IGNORE INTO data_model_indicator_period_config
(period_type, config, update_time)
VALUES ('DAILY', '{
  "trigger": {
    "time": "16:00:00"
  },
  "statRange": {
    "endTime": "16:00:00",
    "startTime": "16:00:00"
  }
}', '2025-05-20 23:59:59');
INSERT IGNORE INTO data_model_indicator_period_config
(period_type, config, update_time)
VALUES ('MONTHLY', '{
  "trigger": {
    "day": 21,
    "time": "16:00:00"
  },
  "statRange": {
    "endDay": "21",
    "endTime": "16:00:00",
    "startDay": 21,
    "startTime": "16:00:00"
  }
}', '2025-05-20 08:24:07');
INSERT IGNORE INTO data_model_indicator_period_config
(period_type, config, update_time)
VALUES ('QUARTERLY', '{
  "trigger": {
    "day": 21,
    "time": "16:00:00",
    "offsetMonths": 0
  },
  "statRange": {
    "endDay": "21",
    "endTime": "16:00:00",
    "startDay": 21,
    "startTime": "16:00:00",
    "startMonth": 12,
    "durationMonths": 3
  }
}', '2025-05-20 09:45:35');
INSERT IGNORE INTO data_model_indicator_period_config
(period_type, config, update_time)
VALUES ('SEMIANNUALLY', '{
  "trigger": {
    "day": 21,
    "time": "16:00:00",
    "offsetMonths": 0
  },
  "statRange": {
    "endDay": "21",
    "endTime": "16:00:00",
    "startDay": 21,
    "startTime": "16:00:00",
    "startMonth": 12,
    "durationMonths": 6
  }
}', '2025-05-20 09:45:37');
INSERT IGNORE INTO data_model_indicator_period_config
(period_type, config, update_time)
VALUES ('WEEKLY', '{
  "trigger": {
    "time": "16:00:00",
    "dayOfWeek": 4
  },
  "statRange": {
    "endTime": "16:00:00",
    "startTime": "16:00:00",
    "endDayOfWeek": 4,
    "startDayOfWeek": 4
  }
}', '2025-05-20 08:37:42');
INSERT IGNORE INTO data_model_indicator_period_config
(period_type, config, update_time)
VALUES ('YEARLY', '{
  "trigger": {
    "day": 21,
    "time": "16:00:00",
    "offsetMonths": 0
  },
  "statRange": {
    "endDay": "21",
    "endTime": "16:00:00",
    "startDay": 21,
    "startTime": "16:00:00",
    "startMonth": 12,
    "durationMonths": 12
  }
}', '2025-05-20 09:45:37');