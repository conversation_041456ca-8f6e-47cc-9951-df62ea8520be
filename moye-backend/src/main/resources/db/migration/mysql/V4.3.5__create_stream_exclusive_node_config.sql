CREATE TABLE IF NOT EXISTS `stream_exclusive_node_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_models` json NOT NULL COMMENT '数据建模id列表',
  `expect_node_count` int(11) NOT NULL COMMENT '期望节点数量',
  `priority` int(11) NOT NULL COMMENT '优先级',
  `create_by` int(11) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` int(11) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='流处理专属节点配置表';