CREATE TABLE IF NOT EXISTS `category_order_config`
(
    `id`          int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `category_id` int      DEFAULT NULL COMMENT '分类id，如业务分类Id等',
    `type`        varchar(20) NOT NULL COMMENT '类型：DATA_MODEL；BUSINESS_CATEGORY等',
    `config`      json        NOT NULL COMMENT '配置',
    `create_by`   int      DEFAULT NULL,
    `update_by`   int      DEFAULT NULL,
    `create_time` datetime DEFAULT NULL,
    `update_time` datetime DEFAULT NULL,
    PRIMARY KEY (`id`)
) COMMENT ='分类、数据建模顺序配置';