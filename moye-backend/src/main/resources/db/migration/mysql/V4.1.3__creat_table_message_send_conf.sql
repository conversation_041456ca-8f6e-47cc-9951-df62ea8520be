-- ----------------------------
-- Table structure for message_send_conf_http
-- ----------------------------
CREATE TABLE IF NOT EXISTS `notice_send_conf_http` (
                                          `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                          `name` varchar(255) NOT NULL COMMENT '策略名称',
                                          `send_role_ids` varchar(255) DEFAULT NULL COMMENT '推送的角色ID列表',
                                          `send_user_ids` longtext COMMENT '推送的用户ID列表',
                                          `message_type_ids` varchar(255) DEFAULT NULL COMMENT '消息类型ID',
                                          `business_ids` varchar(255) DEFAULT NULL COMMENT '业务ID',
                                          `state` int DEFAULT NULL COMMENT '启用状态：1启用，0不启用',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                          `update_by` int DEFAULT NULL COMMENT '更新人',
                                          `create_by` int DEFAULT NULL COMMENT '创建人id',
                                          `api_info` longtext COMMENT '发送的服务调用信息',
                                          `send_parameter` longtext COMMENT '发送服务参数',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- ----------------------------
-- Table structure for message_send_conf_inside
-- ----------------------------
CREATE TABLE IF NOT EXISTS `notice_send_conf_inside` (
                                            `id` int NOT NULL AUTO_INCREMENT COMMENT '内部消息主键',
                                            `name` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息名称',
                                            `send_user_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '接收用户id集合',
                                            `send_role_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '接收角色id集合',
                                            `message_type_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '消息类型id集合',
                                            `business_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '数据范围id集合',
                                            `state` tinyint(1) DEFAULT NULL COMMENT '是否启用 0：停用  1：启用',
                                            `create_by` int DEFAULT NULL COMMENT '创建人',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `update_by` int DEFAULT NULL COMMENT '更新人',
                                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;