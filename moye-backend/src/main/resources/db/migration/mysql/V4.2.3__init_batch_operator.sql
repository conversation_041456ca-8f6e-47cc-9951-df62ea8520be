INSERT IGNORE INTO batch_operator
(id, create_time, create_by, update_time, update_by, cn_name, icon, param_config, function_name, description, input_size, output_size)
VALUES(1, NULL, NULL, NULL, NULL, '输入', 'ability-default', '[{"name": "dbInfo", "type": "SINGLE", "params": [{"key": "tableInfo", "type": "tableList", "value": "", "displayName": "选择表"}, {"key": "dbInfo", "type": "dbList", "value": "", "displayName": "选择存储"}]}]', 'input', NULL, 0, 1);
INSERT IGNORE INTO batch_operator
(id, create_time, create_by, update_time, update_by, cn_name, icon, param_config, function_name, description, input_size, output_size)
VALUES(2, NULL, NULL, NULL, NULL, '输出', 'ability-default', '[{"name": "dbInfo", "type": "SINGLE", "params": [{"key": "dbInfo", "type": "dbList", "value": "", "displayName": "选择存储"}]}]', 'save', NULL, -1, 1);
INSERT IGNORE INTO batch_operator
(id, create_time, create_by, update_time, update_by, cn_name, icon, param_config, function_name, description, input_size, output_size)
VALUES(3, NULL, NULL, NULL, NULL, '排重', 'ability-default', '[{"name": "columns", "type": "SINGLE", "params": [{"key": "distinctColumn", "type": "MULTI_COLUMN", "value": null, "required": true, "displayName": "排重字段"}]}]', 'distinct', '选择多个字段进行排重，类似mysql的联合唯一约束。', 1, 1);
INSERT IGNORE INTO batch_operator
(id, create_time, create_by, update_time, update_by, cn_name, icon, param_config, function_name, description, input_size, output_size)
VALUES(4, NULL, NULL, NULL, NULL, '过滤', 'ability-default', '[{"name": "columns", "type": "LIST", "params": [{"key": "concat", "type": "SELECT", "value": [{"key": "and", "name": "and"}, {"key": "or", "name": "or"}], "displayName": "连接符"}, {"key": "column", "type": "COLUMN", "value": null, "required": true, "displayName": "字段"}, {"key": "operator", "type": "SELECT", "value": [{"key": ">", "name": ">"}, {"key": ">=", "name": ">="}, {"key": "<", "name": "<"}, {"key": "<=", "name": "<="}, {"key": "=", "name": "等于"}, {"key": "!=", "name": "不等于"}, {"key": "is null", "name": "为空"}, {"key": "not null", "name": "不为空"}, {"key": "in", "name": "包含（请输入英文逗号分割的字符串）"}, {"key": "not in", "name": "不包含（请输入英文逗号分割的字符串）"}, {"key": "like", "name": "模糊匹配"}, {"key": "not like", "name": "模糊不匹配"}], "required": true, "displayName": "运算符"}, {"key": "value", "type": "INPUT", "value": null, "required": true, "displayName": "值"}]}]', 'filter', '选择多个输入字段进行联合条件过滤', 1, 1);
INSERT IGNORE INTO batch_operator
(id, create_time, create_by, update_time, update_by, cn_name, icon, param_config, function_name, description, input_size, output_size)
VALUES(5, NULL, NULL, NULL, NULL, '码表回填', 'ability-default', '[{"name": "column", "type": "LIST", "params": [{"key": "column", "type": "COLUMN", "value": null, "required": true, "displayName": "回填字段"}, {"key": "dict", "type": "LIST", "value": [{"key": "code", "type": "INPUT", "value": null, "displayName": "码表键"}, {"key": "value", "type": "INPUT", "value": null, "displayName": "码表值"}], "required": true, "displayName": "码表"}]}]', 'backfill', '多个字段同时转换', 1, 1);
INSERT IGNORE INTO batch_operator
(id, create_time, create_by, update_time, update_by, cn_name, icon, param_config, function_name, description, input_size, output_size)
VALUES(6, NULL, NULL, NULL, NULL, '时间格式转换', 'ability-default', '[{"type": "LIST", "params": [{"key": "column", "type": "COLUMN", "value": "", "required": true, "displayName": "字段"}, {"key": "resourceType", "type": "SELECT", "value": [{"key": "date", "name": "date"}, {"key": "datetime", "name": "datetime"}, {"key": "string", "name": "string"}], "required": true, "displayName": "字段类型"}, {"key": "resourceFormat", "type": "INPUT", "value": null, "required": false, "displayName": "原格式"}, {"key": "targetFormat", "type": "INPUT", "value": null, "required": true, "displayName": "目标格式"}]}]', 'datetimeFormat', NULL, 1, 1);
INSERT IGNORE INTO batch_operator
(id, create_time, create_by, update_time, update_by, cn_name, icon, param_config, function_name, description, input_size, output_size)
VALUES(7, NULL, NULL, NULL, NULL, '浮点数取整', 'ability-default', '[{"name": "dict", "type": "LIST", "params": [{"key": "column", "type": "COLUMN", "value": null, "required": true, "displayName": "字段"}, {"key": "roundType", "type": "SELECT", "value": [{"key": "round", "name": "四舍五入"}, {"key": "ceil", "name": "向上取整"}, {"key": "floor", "name": "向下取整"}], "required": true, "displayName": "取整方式"}]}]', 'floatRound', '选择取整的字段，可以多个', 1, 1);
