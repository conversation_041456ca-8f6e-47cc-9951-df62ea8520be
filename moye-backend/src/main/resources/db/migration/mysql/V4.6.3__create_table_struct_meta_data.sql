CREATE TABLE IF NOT EXISTS `struct_meta_data` (
                                    `id` int NOT NULL AUTO_INCREMENT,
                                    `zh_name` varchar(100) NOT NULL,
                                    `en_name` varchar(50) NOT NULL,
                                    `description` text,
                                    `catalog_id` int NOT NULL,
                                    `create_by` varchar(50) DEFAULT NULL,
                                    `create_time` datetime DEFAULT NULL,
                                    `update_by` varchar(50) DEFAULT NULL,
                                    `update_time` datetime DEFAULT NULL,
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE IF NOT EXISTS `struct_meta_data_field` (
                                          `id` bigint NOT NULL AUTO_INCREMENT,
                                          `struct_meta_data_id` int NOT NULL COMMENT '目录id',
                                          `zh_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '中文名',
                                          `en_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '英文名',
                                          `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型',
                                          `type_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名字',
                                          `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '描述',
                                          `is_multi_value` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否允许多值。1：是；0：否；默认否',
                                          `is_nullable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可以为空 1-可为空 0-不能为空',
                                          `is_statistic` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为统计字段。1：是；0：否；默认否',
                                          `is_built_in` tinyint(1) NOT NULL COMMENT '内置类型：1普通类型，2系统内置',
                                          `advance_config` json DEFAULT NULL COMMENT '高级属性',
                                          `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                          `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                          `default_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE IF NOT EXISTS `struct_meta_data_catalog` (
                                            `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `create_by` int DEFAULT NULL COMMENT '创建人ID',
                                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                            `update_by` int DEFAULT NULL COMMENT '更新人ID',
                                            `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '名称',
                                            `pid` int DEFAULT NULL COMMENT '父ID',
                                            `description` text COMMENT '描述',
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='结构化元数据目录表';