DELIMITER //
DROP PROCEDURE IF EXISTS insertKnowledgeBaseAndFields //
CREATE PROCEDURE InsertKnowledgeBaseAndFields(
    IN p_type VARCHAR(20),
    IN p_zh_name VARCHAR(255),
    IN p_en_name VARCHAR(255),
    IN p_is_sys_source BIT(1),
    IN p_fields JSON
)
BEGIN
    DECLARE cur_base_id INT;
    DECLARE default_user_id INT DEFAULT 1;

    DECLARE childIndex INT DEFAULT 0;
    DECLARE childCount INT;
    DECLARE childExist INT;

    -- Check if the record with the given en_name already exists
    SELECT id INTO cur_base_id
    FROM knowledge_base
    WHERE en_name = p_en_name;

    -- If not exists, insert the record and get the new ID
    IF cur_base_id IS NULL THEN
        INSERT INTO knowledge_base (`type`, `zh_name`, `en_name`, `is_sys_source`, `create_by`, `create_time`, `update_by`, `update_time`)
        VALUES (p_type, p_zh_name, p_en_name, p_is_sys_source, default_user_id, NOW(), default_user_id, NOW());

        SET cur_base_id = LAST_INSERT_ID();
    END IF;


    SET childCount = JSON_LENGTH(p_fields);

    WHILE childIndex < childCount DO
        SELECT count(1) INTO childExist
        FROM knowledge_base_field
        WHERE `base_id` = cur_base_id AND `en_name` = JSON_UNQUOTE(JSON_EXTRACT(p_fields, CONCAT('$[', childIndex, '].en_name')));

        IF childExist = 0 THEN
            INSERT INTO knowledge_base_field (`base_id`, `zh_name`, `en_name`, `type`, `type_name`, `is_unique`, `is_multi_value`, `create_by`, `create_time`, `update_by`, `update_time`)
            VALUES (
                       cur_base_id,
                       JSON_UNQUOTE(JSON_EXTRACT(p_fields, CONCAT('$[', childIndex, '].zh_name'))),
                       JSON_UNQUOTE(JSON_EXTRACT(p_fields, CONCAT('$[', childIndex, '].en_name'))),
                       JSON_UNQUOTE(JSON_EXTRACT(p_fields, CONCAT('$[', childIndex, '].type'))),
                       JSON_UNQUOTE(JSON_EXTRACT(p_fields, CONCAT('$[', childIndex, '].type_name'))),
                       b'0',
                       b'0',
                       default_user_id,
                       NOW(),
                       default_user_id,
                       NOW()
                   );
        END IF;

        SET childIndex = childIndex + 1;
    END WHILE;
END//
DELIMITER ;


CALL InsertKnowledgeBaseAndFields(
        'ENTITY',
        '预警分类',
        'aity_nlpFocusClass',
        b'1',
        JSON_ARRAY(
                JSON_OBJECT('zh_name', '一级分类', 'en_name', 'class1', 'type', 'STRING', 'type_name', '字符串'),
                JSON_OBJECT('zh_name', '二级分类', 'en_name', 'class2', 'type', 'STRING', 'type_name', '字符串'),
                JSON_OBJECT('zh_name', '关键词', 'en_name', 'keyword', 'type', 'TEXT', 'type_name', '长文本')
            )
    );