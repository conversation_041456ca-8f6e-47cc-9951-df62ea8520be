DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='batch_operator' AND column_name='operator_type')
    THEN
        ALTER TABLE batch_operator ADD operator_type varchar(100) NULL COMMENT '批处理算子/流处理算子';
    END IF;
END $$
DELIMITER ;
CALL add_column;

DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='batch_arranged_operator' AND column_name='operator_type')
    THEN
        ALTER TABLE batch_arranged_operator ADD operator_type varchar(100) NULL COMMENT '批处理算子/流处理算子';
    END IF;
END $$
DELIMITER ;
CALL add_column;

DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='batch_arranged_operator' AND column_name='comp_conditions')
    THEN
        ALTER TABLE batch_arranged_operator ADD comp_conditions json NULL COMMENT '过滤条件';
    END IF;
END $$
DELIMITER ;
CALL add_column;

DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='stream_execute_operator' AND column_name='conditions')
    THEN
        ALTER TABLE stream_execute_operator ADD conditions varchar(1000) NULL COMMENT '过滤条件';
    END IF;
END $$
DELIMITER ;
CALL add_column;

DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='stream_execute_operator' AND column_name='actions')
    THEN
        ALTER TABLE stream_execute_operator ADD actions varchar(1000) NULL COMMENT '执行条件';
    END IF;
END $$
DELIMITER ;
CALL add_column;
