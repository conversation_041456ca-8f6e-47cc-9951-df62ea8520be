CALL addColumnIfNotExists('base_component_info', 'corn',
                          'ALTER TABLE `base_component_info` ADD COLUMN `corn` VARCHAR(50) COMMENT ''定期时间corn表达式'';');

CALL addColumnIfNotExists('base_component_info', 'component_type',
                          'ALTER TABLE `base_component_info` ADD COLUMN `component_type` VARCHAR(100) COMMENT ''类型'';');

CALL addColumnIfNotExists('base_component_info', 'request_url',
                          'ALTER TABLE `base_component_info` ADD COLUMN `request_url` VARCHAR(255) COMMENT ''请求地址'';');

CALL addColumnIfNotExists('base_component_info', 'request_body',
                          'ALTER TABLE `base_component_info` ADD COLUMN `request_body` text COMMENT ''请求体'';');