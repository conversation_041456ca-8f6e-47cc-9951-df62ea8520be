CREATE TABLE IF NOT EXISTS `data_model_indicator_config`
(
    `id`                      bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `data_model_id`           int      DEFAULT NULL COMMENT '数据建模id',
    `statistic_strategy_info` json     DEFAULT NULL COMMENT '统计字段',
    `conditions`              json     DEFAULT NULL COMMENT '业务逻辑条件(JSON格式)',
    `statistic_sql`           text COMMENT '统计SQL',
    `create_by`               bigint   DEFAULT NULL COMMENT '创建人',
    `create_time`             datetime DEFAULT NULL COMMENT '创建时间',
    `update_by`               bigint   DEFAULT NULL COMMENT '更新人',
    `update_time`             datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) COMMENT ='数据建模指标配置表';