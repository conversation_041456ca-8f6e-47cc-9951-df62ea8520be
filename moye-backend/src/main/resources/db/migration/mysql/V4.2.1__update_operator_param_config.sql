DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='operator_param_config' AND column_name='location')
    THEN
        ALTER TABLE operator_param_config ADD location varchar(100) NULL COMMENT '参数位置（能力中心算子用）';
    END IF;
END $$
DELIMITER ;
CALL add_column;