ALTER TABLE visual_analysis_chart MODIFY COLUMN type varchar(100) NULL;
DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='visual_analysis_chart' AND column_name='statistic_groups')
    THEN
        ALTER TABLE visual_analysis_chart ADD statistic_groups json NULL COMMENT '图表配置组';
    END IF;
END $$
DELIMITER ;
CALL add_column;
