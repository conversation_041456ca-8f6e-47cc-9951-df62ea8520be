-- `moye-v4`.t_xxl_job_remove_record definition

CREATE TABLE IF NOT EXISTS `xxl_job_remove_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `remove_time` datetime DEFAULT NULL COMMENT '删除时间',
  `xxl_job_id` int(11) DEFAULT NULL COMMENT 'xxl_job任务主键',
  `xxl_job_name` varchar(100) DEFAULT NULL COMMENT 'xxl_job任务名称',
  `xxl_job_app_name` varchar(100) DEFAULT NULL COMMENT 'xxl_job任务所属应用名称',
  `is_error` int(11) DEFAULT NULL COMMENT '删除时是否发生异常',
  `reason` longtext DEFAULT NULL COMMENT '原因：如果是异常，代表异常原因，否则代表正常删除原因',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8mb4 COMMENT='xxl-job任务删除记录';