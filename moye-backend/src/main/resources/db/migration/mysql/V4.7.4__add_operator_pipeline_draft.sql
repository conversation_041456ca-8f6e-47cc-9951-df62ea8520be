CREATE TABLE IF NOT EXISTS `operator_pipeline_draft`
(
    `id`                        bigint NOT NULL AUTO_INCREMENT,
    `data_model_id`             bigint NOT NULL,
    `canvas`                    json   DEFAULT NULL COMMENT '画布信息',
    `field_mapping`             json   DEFAULT NULL COMMENT '字段映射',
    `input_fields`              json   DEFAULT NULL COMMENT '输入字段',
    `data_source_connection_id` bigint DEFAULT NULL COMMENT '数据源连接id',
    `operators`                 json   DEFAULT NULL COMMENT '算子列表',
    PRIMARY KEY (`id`)
) COMMENT '算子编排草稿'