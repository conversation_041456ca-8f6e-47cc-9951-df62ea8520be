INSERT INTO `data_model_field`
(`data_model_id`, `zh_name`, `en_name`, `type`, `type_name`, `description`,
 `is_use_standard`, `is_multi_value`, `is_statistic`, `is_nullable`, `is_built_in`,
 `is_primary_key`, `is_increment`, `is_partition`, `advance_config`, `fields`,
 `create_by`, `create_time`, `update_by`, `update_time`, `default_value`, `is_title`)
SELECT
    dm.id,
    '数据中台文件名', 'trs_moye_ftp_file_name', 'STRING', '字符串', '数据中台内部专用字段',
    1, 0, 0, 0, 1, 0, 0, 0, '{"type": "STRING", "enumValues": []}', NULL,
    1160, '2024-10-17 09:26:47', 1160, '2024-10-17 09:26:47', NULL, 0
FROM `data_model` dm
         left join data_source_config dc on dc.data_model_id = dm.id
WHERE dc.settings ->>'$.connectionType' = 'SFTP' or dc.settings ->>'$.connectionType' = 'FTP'