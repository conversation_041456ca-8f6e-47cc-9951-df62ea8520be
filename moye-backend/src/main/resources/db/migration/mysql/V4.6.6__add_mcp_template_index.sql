DELIMITER $$
DROP PROCEDURE IF EXISTS `add_index` $$
CREATE PROCEDURE add_index()
BEGIN
    IF NOT EXISTS(SELECT *
                  FROM information_schema.INNODB_INDEXES
                  WHERE TABLE_ID = (select TABLE_ID
                                    from information_schema.INNODB_TABLES
                                    where name = concat((select database()), '/', 'mcp_prompt_template'))
                    and name = 'mcp_prompt_template_type_IDX')
    THEN
        CREATE UNIQUE INDEX mcp_prompt_template_type_IDX USING BTREE ON mcp_prompt_template (`type`);
    END IF;
END $$
DELIMITER ;
CALL add_index;