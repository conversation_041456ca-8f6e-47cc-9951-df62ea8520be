
CREATE TABLE  IF NOT EXISTS `data_process_monitor_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `data_model_id` int NOT NULL COMMENT '元数据id',
  `type` varchar(20) NOT NULL COMMENT '类型：DATA_PROCESS(数据处理)、DATA_STORAGE(数据存储)',
  `period_value` int NOT NULL COMMENT '周期',
  `period_unit` varchar(10) NOT NULL COMMENT '周期单位；分钟（minute），小时（hour），天（day）',
  `is_enable` tinyint(1) NOT NULL COMMENT '启用状态；1-启用，0-关闭',
  `schedule_status` varchar(20) NOT NULL COMMENT '调度状态：OPEN(开启),CLOSE(关闭)',
  `record_complete_logs` tinyint(1) NOT NULL COMMENT '是否记录完整日志',
  `xxl_job_id` int DEFAULT NULL,
  `create_by` int DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` int DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
);