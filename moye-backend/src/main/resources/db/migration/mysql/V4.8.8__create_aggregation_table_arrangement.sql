CREATE TABLE IF NOT EXISTS `aggregation_table_arrangement` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `data_model_id` int(11) DEFAULT NULL COMMENT '数据模型ID',
  `canvas` json DEFAULT NULL COMMENT '画布信息',
  `source_tables` json DEFAULT NULL COMMENT '来源表信息',
  `aggregation_table` json DEFAULT NULL COMMENT '聚合表',
  `join_relations` json DEFAULT NULL COMMENT '联表关系',
  `create_by` int(11) DEFAULT NULL COMMENT '创建人',
  `update_by` int(11) DEFAULT NULL COMMENT '最近更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '最近更新时间',
  PRIMARY KEY (`id`)
) COMMENT='聚合表编排信息';