-- ----------------------------
-- Table structure for notice
-- ----------------------------
CREATE TABLE IF NOT EXISTS `notice` (
                          `id` int NOT NULL AUTO_INCREMENT,
                          `title` varchar(255) DEFAULT NULL COMMENT '标题',
                          `content` text COMMENT '内容',
                          `notice_type` int DEFAULT NULL COMMENT '消息类型',
                          `publish_time` timestamp NULL DEFAULT NULL COMMENT '推送时间',
                          `notice_sub_type` varchar(100) DEFAULT NULL COMMENT '消息子类型',
                          `data_model_id` int DEFAULT NULL COMMENT '关联元数据id',
                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='站内消息表';