DELIMITER //
DROP PROCEDURE IF EXISTS upsertIndicatorDatabaseInfo //
CREATE PROCEDURE upsertIndicatorDatabaseInfo(
)
BEGIN
    DECLARE flag INT DEFAULT 0;
    SET flag = (SELECT COUNT(1) FROM `data_connection` WHERE `id` = -1);

    IF flag < 1 THEN
        INSERT INTO data_connection
        (id, name, connection_type, connection_params, is_source, is_test_success, certificate_id, create_by, create_time,
         update_by, update_time)
        VALUES (-1, '系统内置-指标库存储', 'CLICK_HOUSE', '{
          "host": "${clickhouse.host}",
          "port": ${clickhouse.port},
        "database": "${clickhouse.indicatorDatabase}",
        "password": "${clickhouse.password}",
        "protocol": "http",
        "username": "${clickhouse.username}",
        "connectionType": "CLICK_HOUSE"
        }', 0, 1, NULL, 1, '2025-05-20 15:42:44', 1, '2025-05-20 15:42:44');
    ELSE
        UPDATE `data_connection`
        SET connection_params = '{
          "host": "${clickhouse.host}",
          "port": ${clickhouse.port},
        "database": "${clickhouse.indicatorDatabase}",
        "password": "${clickhouse.password}",
        "protocol": "http",
        "username": "${clickhouse.username}",
        "connectionType": "CLICK_HOUSE"
        }'
        WHERE `id` = -1;
    END IF;
END //
DELIMITER ;

CALL upsertIndicatorDatabaseInfo();