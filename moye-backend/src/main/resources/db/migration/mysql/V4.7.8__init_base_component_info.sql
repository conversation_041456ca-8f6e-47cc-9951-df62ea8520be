INSERT INTO `base_component_info` VALUES ('moye', '每隔 40 分钟以及整点执行一次，并且在每次执行的分钟位固定为 20 秒(每个小时的40分和整点的20秒执行)', '中台所有页面功能', 'moye-backend后端服务');
INSERT INTO `base_component_info` VALUES ('moye-storage-engine', '每隔 40 分钟以及整点执行一次，并且在每次执行的分钟位固定为 20 秒(每个小时的40分和整点的20秒执行)', '贴源库/要素库数据存储,数据服务以及存储日志记录', '存储引擎, 用于存储数据以及日志, 提供数据服务');
INSERT INTO `base_component_info` VALUES ('moye-stream-engine', '每隔 40 分钟以及整点执行一次，并且在每次执行的分钟位固定为 20 秒(每个小时的40分和整点的20秒执行)', '流处理数据接入以及算子执行', '流处理引擎, 用于数据处理和算子执行');
INSERT INTO `base_component_info` VALUES ('moye-batch-engine', '每隔 40 分钟以及整点执行一次，并且在每次执行的分钟位固定为 20 秒(每个小时的40分和整点的20秒执行)', '批处理数据接入以及算子执行', '批处理引擎, 用于批量数据处理和算子执行');
INSERT INTO `base_component_info` VALUES ('moye-schedule-center', '每隔 40 分钟以及整点执行一次，并且在每次执行的分钟位固定为 20 秒(每个小时的40分和整点的20秒执行)', '任务的定时调度和执行', '任务调度中心, 用于定时任务的调度和执行');
INSERT INTO `base_component_info` VALUES ('moye-monitor-center', '每隔 40 分钟以及整点执行一次，并且在每次执行的分钟位固定为 20 秒(每个小时的40分和整点的20秒执行)', '中台的接入以及消费监控和告警功能', '监控中心, 用于监控中台服务的状态和性能');
INSERT INTO `base_component_info` VALUES ('KAFKA', '每隔 41 分钟以及整点执行一次，并且在每次执行的分钟位固定为 20 秒(每个小时的41分和整点的20秒执行)', '消息队列的接入和消费', '消息队列, 用于数据的异步传输和处理');
INSERT INTO `base_component_info` VALUES ('seatunnel', '每隔 41 分钟以及整点执行一次，并且在每次执行的分钟位固定为 0 秒(每个小时的41分和整点执行)', '数据接入', '数据接入工具, 用于从各种数据源接入数据');
INSERT INTO `base_component_info` VALUES ('Clickhouse', '每隔 40 分钟以及整点执行一次，并且在每次执行的分钟位固定为 40 秒(每个小时的40分和整点的40秒执行)', '日志/监控记录以及查询', '数据库, 用于存储和查询日志及监控数据');
INSERT INTO `base_component_info` VALUES ('MySql', '每隔 40 分钟以及整点执行一次，并且在每次执行的分钟位固定为 40 秒(每个小时的40分和整点的40秒执行)', '中台数据库查询', '数据库, 用于存储中台的结构化数据');
INSERT INTO `base_component_info` VALUES ('etcd', '每隔 41 分钟以及整点执行一次，并且在每次执行的分钟位固定为 40 秒(每个小时的41分和整点的40秒执行)', '配置中心的存储和管理', '系统配置中心, 用于存储和管理系统配置');