CREATE TABLE IF NOT EXISTS `data_model_indicator_field_config`
(
    `id`                  bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `data_model_id`       int          DEFAULT NULL COMMENT '数据建模id',
    `data_model_field_id` int          DEFAULT NULL COMMENT '数据建模字段id',
    `indicator_type`      varchar(64)  DEFAULT NULL COMMENT '指标类型（元指标、应用指标）',
    `is_enable`           tinyint(1)   DEFAULT NULL COMMENT '是否启用',
    `create_by`           bigint       DEFAULT NULL COMMENT '创建人',
    `create_time`         datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`           bigint       DEFAULT NULL COMMENT '更新人',
    `update_time`         datetime     DEFAULT NULL COMMENT '更新时间',
    `original_zh_name`    varchar(100) DEFAULT NULL COMMENT '原始字段名',
    `config`              text,
    PRIMARY KEY (`id`)
) COMMENT = '数据建模指标字段配置表';