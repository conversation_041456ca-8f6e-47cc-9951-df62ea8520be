CREATE TABLE IF NOT EXISTS `visual_analysis_category`
(
    `id`          int          NOT NULL AUTO_INCREMENT COMMENT '主键',
    `create_by`   int               DEFAULT NULL COMMENT '创建人',
    `create_time` timestamp    NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`   int               DEFAULT NULL COMMENT '更新人',
    `update_time` timestamp    NULL DEFAULT NULL COMMENT '更新时间',
    `name`        varchar(100) NOT NULL COMMENT '分类名称',
    `description` varchar(500)      DEFAULT NULL COMMENT '描述',
    PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `visual_analysis_subject`
(
    `id`              int          NOT NULL AUTO_INCREMENT COMMENT '主键',
    `create_by`       int               DEFAULT NULL COMMENT '创建人',
    `create_time`     timestamp    NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`       int               DEFAULT NULL COMMENT '更新人',
    `update_time`     timestamp    NULL DEFAULT NULL COMMENT '更新时间',
    `name`            varchar(100) NOT NULL COMMENT '主题名称',
    `description`     varchar(500)      DEFAULT NULL COMMENT '描述',
    `position_config` json              DEFAULT NULL COMMENT '图表位置信息',
    `is_publish`      tinyint           DEFAULT '0' COMMENT '是否发布',
    `category_id`     int          NOT NULL COMMENT '分类id',
    PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `visual_analysis_chart`
(
    `id`            int          NOT NULL AUTO_INCREMENT COMMENT '主键',
    `create_by`     int               DEFAULT NULL COMMENT '创建人',
    `create_time`   timestamp    NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`     int               DEFAULT NULL COMMENT '更新人',
    `update_time`   timestamp    NULL DEFAULT NULL COMMENT '更新时间',
    `subject_id`    int          NOT NULL COMMENT '主题id',
    `chart_title`   varchar(100) NOT NULL COMMENT '标题',
    `type`          varchar(100) NOT NULL COMMENT '表类型',
    `data_model_id` int          NOT NULL COMMENT '建模id',
    `storage_id`    int          NOT NULL COMMENT '存储id',
    `ui_schema`     json              DEFAULT NULL COMMENT 'ui配置',
    `data_schema`   json              DEFAULT NULL COMMENT '数据配置',
    `search_schema` json              DEFAULT NULL COMMENT '生成查询配置',
    PRIMARY KEY (`id`)
);