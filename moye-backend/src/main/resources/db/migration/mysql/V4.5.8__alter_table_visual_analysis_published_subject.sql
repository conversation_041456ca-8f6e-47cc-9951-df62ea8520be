CALL addColumnIfNotExists('visual_analysis_published_subject',
'publish_location',
'ALTER TABLE visual_analysis_published_subject
ADD COLUMN publish_location varchar(20) NOT NULL COMMENT ''发布位置（home: 首页; analysis: 主题分析页）'';');

-- 删除索引
DELIMITER $$

CREATE PROCEDURE DropIndexIfExists()
BEGIN
    -- 判断索引是否存在
    SET @dbName = DATABASE();  -- 获取当前数据库名
    SET @tableName = 'visual_analysis_published_subject';
    SET @indexName = 'visual_analysis_published_subject_UN';

SELECT COUNT(*) INTO @indexExists
FROM information_schema.statistics
WHERE table_schema = @dbName
  AND table_name = @tableName
  AND index_name = @indexName;

-- 动态执行删除索引（仅当索引存在时）
IF @indexExists > 0 THEN
        SET @sql = CONCAT('ALTER TABLE ', @tableName, ' DROP INDEX ', @indexName);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SELECT 'Index dropped successfully.' AS message;
ELSE
SELECT 'Index does not exist, skip dropping.' AS message;
END IF;
END
$$

DELIMITER ;

-- 调用存储过程执行删除
CALL DropIndexIfExists();

-- 删除存储过程（可选）
DROP PROCEDURE IF EXISTS DropIndexIfExists;

-- 创建新的索引

DELIMITER $$

CREATE PROCEDURE AddUniqueConstraintIfNotExists()
BEGIN
    -- 定义变量（表名、索引名、数据库名）
    SET @dbName = DATABASE();  -- 当前数据库名
    SET @tableName = 'visual_analysis_published_subject';
    SET @constraintName = 'uniq_subject_location';

    -- 检查唯一约束/索引是否存在
SELECT COUNT(*) INTO @constraintExists
FROM information_schema.statistics
WHERE table_schema = @dbName
  AND table_name = @tableName
  AND index_name = @constraintName;

-- 动态执行添加约束（仅当不存在时）
IF @constraintExists = 0 THEN
        SET @sql = CONCAT(
            'ALTER TABLE ', @tableName,
            ' ADD CONSTRAINT ', @constraintName,
            ' UNIQUE (subject_id, publish_location);'
        );
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SELECT 'Unique constraint added successfully.' AS message;
ELSE
SELECT 'Constraint already exists, skip adding.' AS message;
END IF;
END
$$

DELIMITER ;

-- 调用存储过程执行添加
CALL AddUniqueConstraintIfNotExists();

-- 删除存储过程（可选）
DROP PROCEDURE IF EXISTS AddUniqueConstraintIfNotExists;

UPDATE visual_analysis_published_subject
SET publish_location = 'VISUAL_ANALYSIS'
WHERE publish_location IS NULL;  -- 确保覆盖旧数据
