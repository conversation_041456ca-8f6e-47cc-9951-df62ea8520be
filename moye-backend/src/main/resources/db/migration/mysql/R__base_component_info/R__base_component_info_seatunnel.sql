DELIMITER //
DROP PROCEDURE IF EXISTS upsertBaseComponentInfo //
CREATE PROCEDURE upsertBaseComponentInfo (
    IN p_component_name VARCHAR(50),
    IN p_auto_check_time VARCHAR(255),
    IN p_influence_scope VARCHAR(255),
    IN p_description VARCHAR(255),
    IN p_corn VARCHAR(50),
    IN p_component_type VARCHAR(100),
    IN p_request_url VARCHAR(255),
    IN p_request_body TEXT
)
BEGIN
    DECLARE flag INT DEFAULT 0;
    SET flag = (SELECT COUNT(1) FROM `base_component_info` WHERE `component_name` = p_component_name);

    IF flag < 1 THEN
        INSERT `base_component_info` (`component_name`, `auto_check_time`, `influence_scope`, `description`, `corn`, `component_type`, `request_url`, `request_body`)
        VALUES (p_component_name, p_auto_check_time, p_influence_scope, p_description, p_corn, p_component_type, p_request_url, p_request_body);
    ELSE
        UPDATE `base_component_info`
        SET `auto_check_time` = p_auto_check_time, `influence_scope` = p_influence_scope, `description` = p_description, `corn` = p_corn, `component_type` = p_component_type, `request_url` = p_request_url, `request_body` = p_request_body
        WHERE `component_name` = p_component_name;
    END IF;
END //
DELIMITER ;

CALL upsertBaseComponentInfo(
    'seatunnel',
    '每隔 41 分钟以及整点执行一次，并且在每次执行的分钟位固定为 0 秒(每个小时的41分和整点执行)',
    '数据接入',
    '数据接入工具, 用于从各种数据源接入数据',
    '0 0/41 * * * ?',
    'BASIC_COMPONENT',
    '',
    ''
);
