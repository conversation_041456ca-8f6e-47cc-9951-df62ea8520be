DELIMITER //
DROP PROCEDURE IF EXISTS upsertBaseComponentInfo //
CREATE PROCEDURE upsertBaseComponentInfo (
    IN p_component_name VARCHAR(50),
    IN p_auto_check_time VARCHAR(255),
    IN p_influence_scope VARCHAR(255),
    IN p_description VARCHAR(255),
    IN p_corn VARCHAR(50),
    IN p_component_type VARCHAR(100),
    IN p_request_url VARCHAR(255),
    IN p_request_body TEXT
)
BEGIN
    DECLARE flag INT DEFAULT 0;
    SET flag = (SELECT COUNT(1) FROM `base_component_info` WHERE `component_name` = p_component_name);

    IF flag < 1 THEN
        INSERT `base_component_info` (`component_name`, `auto_check_time`, `influence_scope`, `description`, `corn`, `component_type`, `request_url`, `request_body`)
        VALUES (p_component_name, p_auto_check_time, p_influence_scope, p_description, p_corn, p_component_type, p_request_url, p_request_body);
    ELSE
        UPDATE `base_component_info`
        SET `auto_check_time` = p_auto_check_time, `influence_scope` = p_influence_scope, `description` = p_description, `corn` = p_corn, `component_type` = p_component_type, `request_url` = p_request_url, `request_body` = p_request_body
        WHERE `component_name` = p_component_name;
    END IF;
END //
DELIMITER ;

CALL upsertBaseComponentInfo(
    'trs-nlp-emotion-classify',
    '从整点开始每30分钟的第30秒执行一次（每小时的整点的30秒和30分的30秒执行）',
    'nlp相关算子: 正负面识别',
    '文本情感分析nlp组件，识别文本中表达的正向、负向或中性情绪',
    '30 0/30 * * * ?',
    'NLP_COMPONENT',
    'http://trs-nlp-emotion-classify-svc:8083/nlp/emotion',
    '{"text": "脚本里面其他部分，默认情况下，都不需要动，除非有GPU卡特殊的情况下，或者调优，才会考虑。对应的参数说明如下："}'
);
