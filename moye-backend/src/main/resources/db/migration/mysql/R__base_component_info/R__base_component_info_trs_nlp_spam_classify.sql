DELIMITER //
DROP PROCEDURE IF EXISTS upsertBaseComponentInfo //
CREATE PROCEDURE upsertBaseComponentInfo (
    IN p_component_name VARCHAR(50),
    IN p_auto_check_time VARCHAR(255),
    IN p_influence_scope VARCHAR(255),
    IN p_description VARCHAR(255),
    IN p_corn VARCHAR(50),
    IN p_component_type VARCHAR(100),
    IN p_request_url VARCHAR(255),
    IN p_request_body TEXT
)
BEGIN
    DECLARE flag INT DEFAULT 0;
    SET flag = (SELECT COUNT(1) FROM `base_component_info` WHERE `component_name` = p_component_name);

    IF flag < 1 THEN
        INSERT `base_component_info` (`component_name`, `auto_check_time`, `influence_scope`, `description`, `corn`, `component_type`, `request_url`, `request_body`)
        VALUES (p_component_name, p_auto_check_time, p_influence_scope, p_description, p_corn, p_component_type, p_request_url, p_request_body);
    ELSE
        UPDATE `base_component_info`
        SET `auto_check_time` = p_auto_check_time, `influence_scope` = p_influence_scope, `description` = p_description, `corn` = p_corn, `component_type` = p_component_type, `request_url` = p_request_url, `request_body` = p_request_body
        WHERE `component_name` = p_component_name;
    END IF;
END //
DELIMITER ;

CALL upsertBaseComponentInfo(
    'trs-nlp-spam-classify',
    '从整点开始每30分钟的第40秒执行一次（每小时的整点的40秒和30分的40秒执行）',
    'nlp相关算子: 垃圾文识别',
    '文本内容分析nlp组件，分析文本内容的质量，识别是否为垃圾文',
    '40 0/30 * * * ?',
    'NLP_COMPONENT',
    'http://nlp-spam-classify-svc:8903/nlp/spam',
    '{"text":"浙江省舟山市普陀区螺门村的村民想到北京旅游，被做贼心虚的螺门村官拦截，以为是去上访告状。在杭州火车东站大厅广众之下强行拖走。这是做了多大的见不得人的事呀，要这样做"}'
);
