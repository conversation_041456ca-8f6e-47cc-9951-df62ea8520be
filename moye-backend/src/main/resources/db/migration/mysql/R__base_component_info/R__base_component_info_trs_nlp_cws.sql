DELIMITER //
DROP PROCEDURE IF EXISTS upsertBaseComponentInfo //
CREATE PROCEDURE upsertBaseComponentInfo (
    IN p_component_name VARCHAR(50),
    IN p_auto_check_time VARCHAR(255),
    IN p_influence_scope VARCHAR(255),
    IN p_description VARCHAR(255),
    IN p_corn VARCHAR(50),
    IN p_component_type VARCHAR(100),
    IN p_request_url VARCHAR(255),
    IN p_request_body TEXT
)
BEGIN
    DECLARE flag INT DEFAULT 0;
    SET flag = (SELECT COUNT(1) FROM `base_component_info` WHERE `component_name` = p_component_name);

    IF flag < 1 THEN
        INSERT `base_component_info` (`component_name`, `auto_check_time`, `influence_scope`, `description`, `corn`, `component_type`, `request_url`, `request_body`)
        VALUES (p_component_name, p_auto_check_time, p_influence_scope, p_description, p_corn, p_component_type, p_request_url, p_request_body);
    ELSE
        UPDATE `base_component_info`
        SET `auto_check_time` = p_auto_check_time, `influence_scope` = p_influence_scope, `description` = p_description, `corn` = p_corn, `component_type` = p_component_type, `request_url` = p_request_url, `request_body` = p_request_body
        WHERE `component_name` = p_component_name;
    END IF;
END //
DELIMITER ;

CALL upsertBaseComponentInfo(
    'trs-nlp-cws',
    '从整点开始每30分钟的第20秒执行一次（每小时的整点的20秒和30分的20秒执行）',
    'nlp相关算子: 实体识别',
    '中文文本处理nlp组件，实现文本到词语序列的自动化切分，为实体识别提供基础数据结构支持',
    '20 0/30 * * * ?',
    'NLP_COMPONENT',
    'http://trs-nlp-cws-svc:8800/nlp/cws/event',
    '{"text": ["私家车撞上军用装甲车//@倔犟的秦人:##私家车撞上军用装甲车##新浪看点# 《11月24日,重庆一私家车撞上装甲车。网友:第一次见装甲车开双闪》"]}'
);
