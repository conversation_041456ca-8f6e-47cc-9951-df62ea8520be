CREATE TABLE IF NOT EXISTS `data_connection_monitor_config`
(
    `id`            INT AUTO_INCREMENT,
    `connection_id` BIGINT NOT NULL COMMENT '数据连接ID',
    `enabled`       TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `xxl_job_id`    INT COMMENT 'xxl-job任务ID',
    `create_by`     INT COMMENT '创建人',
    `create_time`   DATETIME COMMENT '创建时间',
    `update_by`     INT COMMENT '更新人',
    `update_time`   DATETIME COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='数据连接监控配置';
