CREATE TABLE IF NOT EXISTS `ability` (
    `id` int NOT NULL AUTO_INCREMENT,
    `en_name` varchar(255) NOT NULL COMMENT '英文名',
    `zh_name` varchar(255) DEFAULT NULL COMMENT '中文名',
    `description` text COMMENT '能力描述',
    `type` varchar(50) NOT NULL COMMENT '能力类型',
    `path` varchar(255) NOT NULL COMMENT '能力路径',
    `operator_category_id` int NOT NULL DEFAULT '1' COMMENT '算子业务分类ID',
    `icon_name` varchar(50) NOT NULL DEFAULT 'ability-default' COMMENT '图标名称',
    `update_status` varchar(50) NOT NULL DEFAULT 'UPDATED' COMMENT '更新状态',
    `test_status` varchar(50) NOT NULL DEFAULT 'UNTESTED' COMMENT '测试状态',
    `http_request_config` text COMMENT 'HTTP请求配置',
    `input_schema` text NOT NULL COMMENT '输入参数',
    `output_schema` text NOT NULL COMMENT '输出参数',
    `create_by` int DEFAULT NULL COMMENT '创建人',
    `update_by` int DEFAULT NULL COMMENT '更新人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `input_size` int DEFAULT '1' COMMENT '输入节点个数限制',
    `output_size` int DEFAULT '1' COMMENT '输出节点个数限制',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '能力表';

DROP TABLE IF EXISTS `operator`;

CREATE TABLE IF NOT EXISTS `operator` (
    `id` int NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL COMMENT '算子名称',
    `pipeline_id` int NOT NULL COMMENT '编排id',
    `conditions` text COMMENT '条件',
    `input_fields` text NOT NULL COMMENT '输入字段',
    `output_fields` text NOT NULL COMMENT '输出字段',
    `ability_id` int DEFAULT NULL COMMENT '能力id',
    `input_bind` text NOT NULL COMMENT '输入绑定',
    `output_bind` text NOT NULL COMMENT '输出绑定',
    `enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
    `storage_id` int DEFAULT NULL COMMENT '存储算子-存储点id',
    `data_model_id` int DEFAULT NULL COMMENT '建模id',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '编排算子表';

CREATE TABLE IF NOT EXISTS `operator_pipeline` (
    `id` int NOT NULL AUTO_INCREMENT,
    `data_model_id` int NOT NULL,
    `canvas` text,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '编排表';