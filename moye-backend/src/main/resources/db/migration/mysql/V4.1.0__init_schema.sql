--
-- Table structure for table `auth_certificate_kerberos`
--
CREATE TABLE IF NOT EXISTS `auth_certificate_kerberos`
(
    `id`          int                                                           NOT NULL AUTO_INCREMENT COMMENT '主键',
    `krb5_path`   varchar(1024)                                                 NOT NULL COMMENT 'krb5配置文件路径',
    `principal`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名称',
    `keytab_path` varchar(1024)                                                 NOT NULL COMMENT '密钥文件地址',
    `create_by`   bigint   DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by`   bigint   DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `in_use`      tinyint  DEFAULT '0' COMMENT '当前凭证是否被使用',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

--
-- Table structure for table `batch_arranged_operator`
--
CREATE TABLE IF NOT EXISTS `batch_arranged_operator`
(
    `id`             int  NOT NULL AUTO_INCREMENT,
    `arrangement_id` int  NOT NULL COMMENT '任务配置id',
    `operator_id`    int  NOT NULL COMMENT '能力id',
    `parent_ids`     json NOT NULL COMMENT '父节点id（order字段），json数组',
    `parameters`     json         DEFAULT NULL COMMENT '参数',
    `output_columns` json         DEFAULT NULL COMMENT '输出数据结构',
    `function_name`  varchar(100) DEFAULT NULL COMMENT '能力名称',
    `need_save`      tinyint      DEFAULT NULL COMMENT '是否需要落盘',
    `data_model_id`  int          DEFAULT NULL COMMENT '元数据id',
    `display_id`     bigint       DEFAULT NULL COMMENT '算子编排中算子顺序（前端生成）',
    `using_fields`   json         DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `batch_arrangement`
--
CREATE TABLE IF NOT EXISTS `batch_arrangement`
(
    `id`               int          NOT NULL AUTO_INCREMENT,
    `create_by`        int      DEFAULT NULL,
    `create_time`      datetime DEFAULT NULL,
    `update_by`        int      DEFAULT NULL,
    `update_time`      datetime DEFAULT NULL,
    `data_model_id`    int          NOT NULL COMMENT '元数据id',
    `display_type`     varchar(100) NOT NULL COMMENT '展示类型 画布/代码',
    `arrangement`      json     DEFAULT NULL COMMENT 'dag',
    `code_sub_tasks`   json     DEFAULT NULL COMMENT '代码',
    `canvas`           text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '画布信息',
    `is_updated_tasks` tinyint  DEFAULT '0' COMMENT '是否已更新任务 1: 已更新 0: 未更新(暂时针对代码模式)',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `batch_operator`
--
CREATE TABLE IF NOT EXISTS `batch_operator`
(
    `id`            int          NOT NULL AUTO_INCREMENT,
    `create_time`   datetime     DEFAULT NULL COMMENT '创建时间',
    `create_by`     int          DEFAULT NULL COMMENT '创建人',
    `update_time`   datetime     DEFAULT NULL COMMENT '修改时间',
    `update_by`     int          DEFAULT NULL COMMENT '修改人',
    `cn_name`       varchar(100) NOT NULL,
    `icon`          varchar(255) NOT NULL,
    `param_config`  json         DEFAULT NULL COMMENT '参数配置',
    `function_name` varchar(100) DEFAULT NULL COMMENT '方法名',
    `description`   varchar(500) DEFAULT NULL COMMENT '描述',
    `input_size`    int          DEFAULT NULL COMMENT '输入节点个数限制',
    `output_size`   int          DEFAULT NULL COMMENT '输出节点个数限制',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `business_category`
--
CREATE TABLE IF NOT EXISTS `business_category`
(
    `id`          int                                                          NOT NULL AUTO_INCREMENT,
    `zh_name`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '业务名称',
    `en_name`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '英文缩写',
    `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '描述',
    `create_by`   int                                                          DEFAULT NULL COMMENT '创建人id',
    `update_by`   int                                                          DEFAULT NULL COMMENT '更新人id',
    `update_time` datetime                                                     DEFAULT NULL COMMENT '更新时间',
    `create_time` datetime                                                     DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='业务分类表';


--
-- Table structure for table `data_connection`
--
CREATE TABLE IF NOT EXISTS `data_connection`
(
    `id`                bigint                                                       NOT NULL AUTO_INCREMENT COMMENT 'id',
    `name`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '连接名',
    `connection_type`   varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
    `connection_params` json     DEFAULT NULL COMMENT '连接信息',
    `is_source`         tinyint(1)                                                   NOT NULL COMMENT 'true为数据源 false是数据存储',
    `is_test_success`   tinyint(1)                                                   NOT NULL COMMENT '是否测试通过',
    `certificate_id`    bigint   DEFAULT NULL COMMENT '证书id',
    `create_by`         bigint   DEFAULT NULL COMMENT '创建人',
    `create_time`       datetime DEFAULT NULL COMMENT '创建时间',
    `update_by`         bigint   DEFAULT NULL COMMENT '更新人',
    `update_time`       datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `data_model`
--
CREATE TABLE IF NOT EXISTS `data_model`
(
    `id`                    bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `create_mode`           varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '创建模式： 数据接入、逆向建模、视图',
    `execute_status`        varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '任务调度状态：1开启，2关闭',
    `zh_name`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '元数据中文名',
    `en_name`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '元数据英文名',
    `business_category_id`  int                                                           DEFAULT NULL COMMENT '业务分类id',
    `layer`                 varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '分层',
    `description`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '说明',
    `is_arranged`           tinyint(1)                                                    DEFAULT '0' COMMENT '是否治理,枚举',
    `create_by`             bigint                                                        DEFAULT NULL COMMENT '创建人',
    `create_time`           datetime                                                      DEFAULT NULL COMMENT '创建时间',
    `update_by`             bigint                                                        DEFAULT NULL COMMENT '更新人',
    `update_time`           datetime                                                      DEFAULT NULL COMMENT '更新时间',
    `meta_data_standard_id` bigint                                                        DEFAULT NULL COMMENT '元数据标准编号',
    `is_sync_field`         tinyint(1)                                                    DEFAULT NULL COMMENT '是否同步字段',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `data_model_execute_config`
--
CREATE TABLE IF NOT EXISTS `data_model_execute_config`
(
    `id`             bigint NOT NULL AUTO_INCREMENT,
    `data_model_id`  bigint NOT NULL,
    `execute_params` json     DEFAULT NULL COMMENT '执行参数',
    `increment_info` json     DEFAULT NULL COMMENT '增量信息',
    `spark_config`   json     DEFAULT NULL COMMENT 'spark配置，批处理使用',
    `create_by`      bigint   DEFAULT NULL COMMENT '创建人',
    `create_time`    datetime DEFAULT NULL COMMENT '创建时间',
    `update_by`      bigint   DEFAULT NULL COMMENT '更新人',
    `update_time`    datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `data_model_field`
--
CREATE TABLE IF NOT EXISTS `data_model_field`
(
    `id`              bigint                                                        NOT NULL AUTO_INCREMENT,
    `data_model_id`   bigint                                                        NOT NULL COMMENT '元数据id',
    `zh_name`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin        NOT NULL COMMENT '中文名',
    `en_name`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin        NOT NULL COMMENT '英文名',
    `type`            varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '属性类型',
    `type_name`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '属性类型名字',
    `description`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '系统属性描述',
    `is_use_standard` tinyint(1)                                                    NOT NULL COMMENT '类别：1：标准属性；2：自定义属性',
    `is_multi_value`  tinyint(1)                                                    NOT NULL DEFAULT '0' COMMENT '是否允许多值。1：是；0：否；默认否',
    `is_statistic`    tinyint(1)                                                    NOT NULL DEFAULT '0' COMMENT '是否为统计字段。1：是；0：否；默认否',
    `is_nullable`     tinyint(1)                                                    NOT NULL DEFAULT '1' COMMENT '是否可以为空 1-可为空 0-不能为空',
    `is_built_in`     tinyint(1)                                                    NOT NULL COMMENT '内置类型：1普通类型，2系统内置',
    `is_primary_key`  tinyint(1)                                                    NOT NULL COMMENT '是否为主键',
    `is_increment`    tinyint(1)                                                    NOT NULL DEFAULT '0' COMMENT '是否增量字段',
    `is_partition`    tinyint(1)                                                    NOT NULL DEFAULT '0' COMMENT '是否分区字段',
    `advance_config`  json                                                                   DEFAULT NULL COMMENT '高级设置',
    `fields`          text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '下属字段详情，json结构',
    `create_by`       bigint                                                                 DEFAULT NULL COMMENT '创建人',
    `create_time`     datetime                                                               DEFAULT NULL COMMENT '创建时间',
    `update_by`       bigint                                                                 DEFAULT NULL COMMENT '更新人',
    `update_time`     datetime                                                               DEFAULT NULL COMMENT '更新时间',
    `default_value`   varchar(100)                                                           DEFAULT NULL COMMENT '默认值',
    `is_title`        tinyint(1)                                                             DEFAULT NULL COMMENT '是标题字段',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `data_model_monitor_config`
--
CREATE TABLE IF NOT EXISTS `data_model_monitor_config`
(
    `id`            bigint                                                       NOT NULL AUTO_INCREMENT,
    `version`       varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置版本号',
    `data_model_id` bigint                                                       NOT NULL COMMENT '元数据id',
    `period_value`  int                                                          NOT NULL COMMENT '周期',
    `period_unit`   varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '周期单位；分钟（minute），小时（hour），天（day）',
    `is_enable`     tinyint(1)                                                   NOT NULL COMMENT '启用状态；1-启用，0-关闭',
    `type`          varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型；积压（behind）、\n波动（fluctuation）、\n断流（cutoff）',
    `is_inuse`      tinyint(1)                                                   NOT NULL COMMENT '正在使用；1-正在使用，0-未使用；判断是否使正在使用的配置，一个元数据每个类型的配置都会有1条记录',
    `threshold`     int      DEFAULT NULL COMMENT '阈值',
    `create_by`     bigint   DEFAULT NULL COMMENT '创建人',
    `create_time`   datetime DEFAULT NULL COMMENT '创建时间',
    `update_by`     bigint   DEFAULT NULL COMMENT '更新人',
    `update_time`   datetime DEFAULT NULL COMMENT '更新时间',
    `xxl_job_id`    int      DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `data_model_schedule_config`
--
CREATE TABLE IF NOT EXISTS `data_model_schedule_config`
(
    `id`                   bigint                                                       NOT NULL AUTO_INCREMENT,
    `data_model_id`        bigint                                                       NOT NULL,
    `execute_mode`         varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '执行方式',
    `schedule_info`        json                                                         NOT NULL COMMENT '调度信息',
    `create_by`            bigint     DEFAULT NULL COMMENT '创建人',
    `create_time`          datetime   DEFAULT NULL COMMENT '创建时间',
    `update_by`            bigint     DEFAULT NULL COMMENT '更新人',
    `update_time`          datetime   DEFAULT NULL COMMENT '更新时间',
    `is_at_execute_update` tinyint(1) DEFAULT NULL COMMENT '在执行时更新调度配置',
    `xxl_job_id`           int        DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `data_service`
--
CREATE TABLE IF NOT EXISTS `data_service`
(
    `id`                bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `code`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '数据服务编码',
    `name`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
    `description`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '描述',
    `connection_id`     bigint                                                        NOT NULL COMMENT '数据连接id',
    `storage_id`        bigint   DEFAULT NULL COMMENT '数据存储id',
    `category_id`       bigint                                                        NOT NULL COMMENT '分类id',
    `create_mode`       varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '服务模式：向导/代码',
    `publish_status`    varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '发布状态',
    `ability_center_id` int      DEFAULT NULL COMMENT '发布后在能力中心的id',
    `health_status`     varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '健康状态',
    `create_by`         int      DEFAULT NULL COMMENT '创建人',
    `update_by`         int      DEFAULT NULL COMMENT '更新人',
    `create_time`       datetime DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `data_service_category`
--
CREATE TABLE IF NOT EXISTS `data_service_category`
(
    `id`          bigint NOT NULL AUTO_INCREMENT,
    `name`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据服务分类名称',
    `create_by`   int                                                          DEFAULT NULL COMMENT '创建人id',
    `update_by`   int                                                          DEFAULT NULL COMMENT '更新人id',
    `create_time` datetime                                                     DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime                                                     DEFAULT NULL COMMENT '更新时间',
    `pid`         int                                                          DEFAULT NULL COMMENT '父id',
    `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '描述',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `data_service_config`
--
CREATE TABLE IF NOT EXISTS `data_service_config`
(
    `id`              bigint NOT NULL AUTO_INCREMENT COMMENT '配置id',
    `data_service_id` bigint                                                       DEFAULT NULL COMMENT '数据服务id',
    `type`            varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '配置类型：查询、计数、更新、统计',
    `params`          json                                                         DEFAULT NULL COMMENT '数据服务具体配置',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `data_source_config`
--
CREATE TABLE IF NOT EXISTS `data_source_config`
(
    `id`              bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键',
    `zh_name`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '数据表中文名称（如果是要素库就是贴源库的名称）',
    `en_name`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '数据表英文名',
    `connection_id`   bigint DEFAULT NULL COMMENT '连接id',
    `settings`        json   DEFAULT NULL COMMENT '具体数据选择配置',
    `data_model_id`   int                                                           NOT NULL COMMENT '数据建模id',
    `source_model_id` int    DEFAULT NULL COMMENT '要素库、主题库、专题库所依赖的数据建模ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `data_source_engine_config`
--
CREATE TABLE IF NOT EXISTS `data_source_engine_config`
(
    `id`             int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `data_source_id` int NOT NULL COMMENT '数据源id',
    `node_number`    int NOT NULL COMMENT '节点数量',
    `order`          int NOT NULL COMMENT '优先级',
    `create_by`      int      DEFAULT NULL COMMENT '创建人',
    `create_time`    datetime DEFAULT NULL COMMENT '创建时间',
    `update_by`      int      DEFAULT NULL COMMENT '修改人',
    `update_time`    datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `data_standard_field`
--
CREATE TABLE IF NOT EXISTS `data_standard_field`
(
    `id`             bigint                                                        NOT NULL AUTO_INCREMENT,
    `category_id`    bigint                                                        NOT NULL COMMENT '目录id',
    `zh_name`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '系统属性中文名',
    `en_name`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin        NOT NULL COMMENT '系统属性英文名',
    `type`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '系统属性类型',
    `type_name`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '系统属性名字',
    `description`    text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         NOT NULL COMMENT '系统属性描述',
    `is_multi_value` tinyint(1)                                                    NOT NULL DEFAULT '0' COMMENT '是否允许多值。1：是；0：否；默认否',
    `is_nullable`    tinyint(1)                                                    NOT NULL DEFAULT '1' COMMENT '是否可以为空 1-可为空 0-不能为空',
    `is_statistic`   tinyint(1)                                                    NOT NULL DEFAULT '0' COMMENT '是否为统计字段。1：是；0：否；默认否',
    `is_built_in`    tinyint(1)                                                    NOT NULL COMMENT '内置类型：1普通类型，2系统内置',
    `advance_config` json                                                                   DEFAULT NULL COMMENT '高级属性',
    `create_by`      bigint                                                                 DEFAULT NULL COMMENT '创建人',
    `create_time`    datetime                                                               DEFAULT NULL COMMENT '创建时间',
    `update_by`      bigint                                                                 DEFAULT NULL COMMENT '更新人',
    `update_time`    datetime                                                               DEFAULT NULL COMMENT '更新时间',
    `default_value`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `data_storage`
--
CREATE TABLE IF NOT EXISTS `data_storage`
(
    `id`                  bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键',
    `en_name`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '数据表名称',
    `zh_name`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '表注释',
    `data_model_id`       bigint                                                        NOT NULL COMMENT '关联元数据id',
    `connection_id`       bigint                                                        NOT NULL COMMENT '存储id',
    `create_table_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '建表状态',
    `field_ids`           json                                                          DEFAULT NULL COMMENT '字段id列表',
    `settings`            json                                                          DEFAULT NULL COMMENT '建表设置：例如 es 分片数量，hive存储格式',
    `create_time`         datetime                                                      DEFAULT NULL COMMENT '创建时间',
    `update_by`           bigint                                                        DEFAULT NULL COMMENT '更新人',
    `create_by`           bigint                                                        DEFAULT NULL COMMENT '创建人',
    `update_time`         datetime                                                      DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `departments`
--
CREATE TABLE IF NOT EXISTS `departments`
(
    `id`          int                                                           NOT NULL AUTO_INCREMENT COMMENT '部门编号',
    `name`        varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '部门名称',
    `pid`         int                                                           NOT NULL COMMENT '父级树形地址，第一级为0',
    `create_by`   bigint   DEFAULT NULL COMMENT '创建者',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by`   bigint   DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `INDEX_parent_tree_path` (`pid`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb3 COMMENT ='部门表，（组织）';


--
-- Table structure for table `field_standard_category`
--
CREATE TABLE IF NOT EXISTS `field_standard_category`
(
    `id`          int NOT NULL AUTO_INCREMENT,
    `create_time` datetime                                                      DEFAULT NULL,
    `create_by`   int                                                           DEFAULT NULL,
    `update_time` datetime                                                      DEFAULT NULL,
    `update_by`   int                                                           DEFAULT NULL,
    `name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `pid`         int                                                           DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='标准字段目录表';


--
-- Table structure for table `job_task_relation`
--
CREATE TABLE IF NOT EXISTS `job_task_relation`
(
    `id`          int       NOT NULL AUTO_INCREMENT,
    `task_id`     int            DEFAULT NULL COMMENT '元数据id',
    `job_id`      bigint         DEFAULT NULL COMMENT '存储引擎jobId',
    `job_name`    varchar(255)   DEFAULT NULL COMMENT '任务名称',
    `create_by`   int            DEFAULT NULL,
    `create_time` timestamp NULL DEFAULT NULL,
    `update_by`   int            DEFAULT NULL,
    `update_time` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `meta_data_standard`
--
CREATE TABLE IF NOT EXISTS `meta_data_standard`
(
    `id`                            int          NOT NULL AUTO_INCREMENT,
    `zh_name`                       varchar(100) NOT NULL,
    `en_name`                       varchar(50)  NOT NULL,
    `type`                          varchar(20)  NOT NULL,
    `description`                   text,
    `meta_data_standard_catalog_id` int          NOT NULL,
    `vid_type`                      varchar(50) DEFAULT NULL,
    `create_by`                     varchar(50) DEFAULT NULL,
    `create_time`                   datetime    DEFAULT NULL,
    `update_by`                     varchar(50) DEFAULT NULL,
    `update_time`                   datetime    DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `meta_data_standard_catalog`
--
CREATE TABLE IF NOT EXISTS `meta_data_standard_catalog`
(
    `id`          int NOT NULL AUTO_INCREMENT COMMENT '目录编号',
    `name`        varchar(100) DEFAULT NULL COMMENT '名称',
    `description` text COMMENT '描述',
    `pid`         int          DEFAULT '0' COMMENT '目录父编号；第一级目录编号为0',
    `create_by`   varchar(50)  DEFAULT NULL,
    `create_time` datetime     DEFAULT NULL,
    `update_by`   varchar(50)  DEFAULT NULL,
    `update_time` datetime     DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='元数据标准--目录';


--
-- Table structure for table `meta_data_standard_field`
--
CREATE TABLE IF NOT EXISTS `meta_data_standard_field`
(
    `id`                    int          NOT NULL AUTO_INCREMENT,
    `meta_data_standard_id` int          NOT NULL,
    `zh_name`               varchar(100) NOT NULL,
    `en_name`               varchar(50)  NOT NULL,
    `description`           text,
    `type`                  varchar(20)  NOT NULL,
    `fields`                longtext     NOT NULL,
    `primary_key`           varchar(50) DEFAULT NULL,
    `create_by`             varchar(50) DEFAULT NULL,
    `create_time`           varchar(50) DEFAULT NULL,
    `update_by`             varchar(50) DEFAULT NULL,
    `update_time`           varchar(50) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `operator`
--
CREATE TABLE IF NOT EXISTS `operator`
(
    `id`                   int                                                            NOT NULL AUTO_INCREMENT COMMENT '主键',
    `create_by`            int                                                            NOT NULL COMMENT '创建人',
    `create_time`          timestamp                                                      NOT NULL COMMENT '创建时间',
    `update_by`            int                                                            NOT NULL COMMENT '更新人',
    `update_time`          timestamp                                                      NOT NULL COMMENT '更新时间',
    `operator_category_id` int                                                            NOT NULL COMMENT '算子业务分类id',
    `en_name`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '英文名',
    `zh_name`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '中文名',
    `description`          varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '描述',
    `icon_name`            varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '图标',
    `update_status`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '更新状态',
    `test_status`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '测试状态',
    `call_type`            varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '调用类型',
    `call_params`          json                                                           NOT NULL COMMENT '调用参数',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='算子表';


--
-- Table structure for table `operator_business_category`
--
CREATE TABLE IF NOT EXISTS `operator_business_category`
(
    `id`          int                                                           NOT NULL AUTO_INCREMENT COMMENT '主键',
    `create_by`   int                                                           NOT NULL COMMENT '创建人',
    `create_time` timestamp                                                     NOT NULL COMMENT '创建时间',
    `update_by`   int                                                           NOT NULL COMMENT '更新人',
    `update_time` timestamp                                                     NOT NULL COMMENT '更新时间',
    `en_abbr`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '英文缩写',
    `zh_name`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '中文名',
    `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '描述',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='算子业务分类';


--
-- Table structure for table `operator_param_config`
--
CREATE TABLE IF NOT EXISTS `operator_param_config`
(
    `id`            int                                                           NOT NULL AUTO_INCREMENT COMMENT '主键',
    `operator_id`   int                                                           NOT NULL COMMENT '算子id',
    `position`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参数位置 input/output',
    `param_type`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参数类型 单字段/键值对',
    `param_detail`  json                                                          NOT NULL COMMENT '字段详情',
    `cascade_field` json                                                                   DEFAULT NULL COMMENT '级联字段信息',
    `is_group`      tinyint(1)                                                    NOT NULL DEFAULT '0' COMMENT '是否多组',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='算子输入输出字段配置信息';


--
-- Table structure for table `role`
--
CREATE TABLE IF NOT EXISTS `role`
(
    `id`          int                                                           NOT NULL AUTO_INCREMENT,
    `name`        varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
    `type`        varchar(50)                                                   NOT NULL DEFAULT '3' COMMENT '角色类型：1表示超管，2表示管理员，3表示用户定义的角色',
    `operations`  text COMMENT '用户的权限列表(给前端显示用)',
    `credentials` varchar(1024)                                                          DEFAULT NULL COMMENT '凭证字段',
    `create_time` timestamp                                                     NULL     DEFAULT NULL,
    `update_time` timestamp                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `create_by`   int                                                                    DEFAULT NULL,
    `update_by`   int                                                                    DEFAULT NULL COMMENT '修改人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb3;


--
-- Table structure for table `role_module`
--
CREATE TABLE IF NOT EXISTS `role_module`
(
    `id`         int                                                         NOT NULL AUTO_INCREMENT,
    `role_id`    int                                                         NOT NULL,
    `module_id`  varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
    `module_pid` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = latin1;


--
-- Table structure for table `role_operation`
--
CREATE TABLE IF NOT EXISTS `role_operation`
(
    `id`               int NOT NULL AUTO_INCREMENT COMMENT '关联表id',
    `role_id`          int                                                           DEFAULT NULL COMMENT '角色id',
    `operation`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作权限',
    `parent_operation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '父级操作权限',
    `module_id`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模块ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;


--
-- Table structure for table `stream_arranged_operator`
--
CREATE TABLE IF NOT EXISTS `stream_arranged_operator`
(
    `id`             int NOT NULL AUTO_INCREMENT COMMENT '主键',
    `data_model_id`  int NOT NULL COMMENT '数据建模id',
    `arrangement_id` int NOT NULL COMMENT '编排id',
    `operator_id`    int NOT NULL COMMENT '算子id',
    `en_name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '算子英文名',
    `conditions`     json                                                          DEFAULT NULL COMMENT '筛选条件',
    `enabled`        tinyint                                                       DEFAULT NULL COMMENT '是否开启',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='存储编排过的算子详情，输出输出参数、过滤条件等';


--
-- Table structure for table `stream_arranged_operator_param`
--
CREATE TABLE IF NOT EXISTS `stream_arranged_operator_param`
(
    `id`                   int NOT NULL AUTO_INCREMENT COMMENT '主键',
    `arranged_operator_id` int NOT NULL COMMENT '编排算子id',
    `param_detail`         json         DEFAULT NULL COMMENT '参数详情（字段配置及填入的值）',
    `store_mode`           varchar(100) DEFAULT NULL COMMENT '存放方式（concat；replace）',
    `data_model_id`        bigint       DEFAULT NULL COMMENT '建模id',
    `position`             varchar(100) DEFAULT NULL COMMENT '位置',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='编排算子参数详情';


--
-- Table structure for table `stream_arrangement`
--
CREATE TABLE IF NOT EXISTS `stream_arrangement`
(
    `id`            int       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `create_by`     int            DEFAULT NULL COMMENT '创建人',
    `create_time`   timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`     int            DEFAULT NULL COMMENT '更新人',
    `update_time`   timestamp NULL DEFAULT NULL COMMENT '更新时间',
    `data_model_id` int            DEFAULT NULL COMMENT '数据建模id',
    `arrangement`   json           DEFAULT NULL COMMENT '编排信息',
    `canvas`        json           DEFAULT NULL COMMENT '画布信息',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='流处理算子编排，用于返回给前端';


--
-- Table structure for table `stream_execute_operator`
--
CREATE TABLE IF NOT EXISTS `stream_execute_operator`
(
    `id`                 int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `zh_name`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '规则名称',
    `rule_express`       longtext COMMENT '规则执行表达式',
    `status`             int                                                           DEFAULT NULL COMMENT '规则状态，1：启用，0：停用',
    `execute_order`      int                                                           DEFAULT NULL COMMENT '排序参数',
    `data_model_id`      int                                                           DEFAULT NULL COMMENT '数据源id',
    `operator_id`        int                                                           DEFAULT NULL COMMENT '规则库中规则id',
    `storage`            longtext COMMENT '规则操作输出信息',
    `tenant_id`          int                                                           DEFAULT NULL COMMENT '租户id',
    `arrangement_status` tinyint                                                       DEFAULT '1' COMMENT '算子编排开启关闭，1启用，0关闭',
    `execute_config`     json                                                          DEFAULT NULL COMMENT '算子编排-算子执行配置，json格式',
    `type`               varchar(40)                                                   DEFAULT NULL COMMENT '算子类型：operator算子，table表',
    `en_name`            varchar(255)                                                  DEFAULT NULL COMMENT '算子英文名',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `sys_push_mqserver`
--
CREATE TABLE IF NOT EXISTS `sys_push_mqserver`
(
    `id`          int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `tenant_id`   int          DEFAULT NULL COMMENT '租户id',
    `tenant_name` varchar(255) DEFAULT NULL COMMENT '租户名称',
    `type`        varchar(32)  DEFAULT NULL COMMENT 'MQ类型；rocketmq、kafka等',
    `server_addr` varchar(255) DEFAULT NULL COMMENT 'MQ服务地址',
    `create_by`   int          DEFAULT NULL COMMENT '创建人',
    `create_time` datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`   int          DEFAULT NULL COMMENT '修改人',
    `update_time` datetime     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `sys_push_topic_config`
--
CREATE TABLE IF NOT EXISTS `sys_push_topic_config`
(
    `id`          int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `tenant_id`   int          DEFAULT NULL COMMENT '租户id',
    `tenant_name` varchar(255) DEFAULT NULL COMMENT '租户名称',
    `type`        varchar(32)  DEFAULT NULL COMMENT 'MQ类型；rocketmq、kafka等',
    `server_addr` varchar(255) DEFAULT NULL COMMENT 'MQ服务地址',
    `address`     varchar(255) DEFAULT NULL COMMENT '地址',
    `topic`       varchar(64)  DEFAULT NULL COMMENT 'MQ主题',
    `group`       varchar(64)  DEFAULT NULL COMMENT '消费者分组',
    `enable`      int          DEFAULT NULL COMMENT '启用状态；1启用，0关闭',
    `create_by`   int          DEFAULT NULL COMMENT '创建人',
    `create_time` datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`   int          DEFAULT NULL COMMENT '修改人',
    `update_time` datetime     DEFAULT NULL COMMENT '更新时间',
    `label`       varchar(64)  DEFAULT NULL COMMENT '标签',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `t_entity`
--
CREATE TABLE IF NOT EXISTS `t_entity`
(
    `id`                 int          NOT NULL AUTO_INCREMENT COMMENT '实体编号',
    `zh_name`            varchar(255) NOT NULL COMMENT '实体中文名称',
    `en_name`            varchar(255) NOT NULL COMMENT '实体英文名称',
    `color`              varchar(255) NOT NULL COMMENT '实体颜色',
    `parent_entity_id`   int          NOT NULL COMMENT '父级实体编号',
    `is_sys_source`      int                   DEFAULT '0' COMMENT '判断是否是系统资源：   1：是；0：否',
    `recent_backup_time` datetime              DEFAULT NULL COMMENT '最近备份时间',
    `is_backup`          int          NOT NULL DEFAULT '0' COMMENT '是否备份 1：是； 0：否',
    `tenant_id`          int                   DEFAULT NULL COMMENT '租户id',
    `field_list`         text COMMENT '属性列表',
    `create_by`          int                   DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime              DEFAULT NULL COMMENT '创建日期',
    `update_by`          int                   DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime              DEFAULT NULL COMMENT '更新日期',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


--
-- Table structure for table `user_log`
--
CREATE TABLE IF NOT EXISTS `user_log`
(
    `id`          int NOT NULL AUTO_INCREMENT,
    `ip`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
    `login_time`  datetime                                                      DEFAULT NULL,
    `logout_time` datetime                                                      DEFAULT NULL,
    `user_id`     int                                                           DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

--
-- Table structure for table `users`
--
CREATE TABLE IF NOT EXISTS `users`
(
    `id`               int                                                           NOT NULL AUTO_INCREMENT,
    `name`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '名称',
    `type`             varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT 'PERSONAL' COMMENT '用户类型（PERSONAL：个人用户，ENTERPRISE：企业用户）',
    `account`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '账号。登陆用',
    `password`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码',
    `department_id`    int                                                           NOT NULL COMMENT '部门id',
    `telephone`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '手机号码',
    `email`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '邮箱',
    `is_enable`        int                                                                    DEFAULT '1' COMMENT '用户状态。锁定为0，解锁为1',
    `login_failed_num` int                                                           NOT NULL DEFAULT '0' COMMENT '已登录失败次数',
    `pwd_update_time`  datetime                                                               DEFAULT '2022-11-11 11:11:11' COMMENT '修改密码时间',
    `create_by`        int                                                                    DEFAULT NULL,
    `update_by`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL,
    `update_time`      datetime                                                               DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_time`      datetime                                                               DEFAULT NULL,
    `role_ids`         json                                                                   DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

