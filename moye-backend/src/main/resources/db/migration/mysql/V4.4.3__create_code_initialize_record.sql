CREATE TABLE  IF NOT EXISTS  `code_initialize_record` (
    `id` INT AUTO_INCREMENT COMMENT '主键',
    `name` VARCHAR(64) COMMENT '名称',
    `order` INT COMMENT '执行顺序',
    `bean_class` VARCHAR(255) COMMENT 'bean类型',
    `bean_method` VARCHAR(128) COMMENT 'bean方法',
    `execute_time` DATETIME COMMENT '执行时间',
    `execute_duration` BIGINT COMMENT '执行时长，单位：毫秒',
    `execute_result` VARCHAR(20) COMMENT '执行结果',
    `details` JSON COMMENT '执行详情',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT='代码初始化记录';