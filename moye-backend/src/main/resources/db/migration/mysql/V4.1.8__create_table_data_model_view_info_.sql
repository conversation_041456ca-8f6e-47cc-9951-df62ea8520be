CREATE TABLE IF NOT EXISTS `data_model_view_info`
(
    `id`             bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `data_model_id`  bigint  DEFAULT NULL COMMENT '元数据id',
    `connection_id`  bigint  DEFAULT NULL COMMENT '数据库连接id',
    `tables_fields`  longtext DEFAULT NULL COMMENT '表和字段信息 json结构[{"tableName":"my_table","tableZhName":"中文表名","fields":[{"fieldName":"my_field1","filedType":"int"}]}]',
    `join_relations` longtext DEFAULT NULL COMMENT '表联合查询的映射关系 json结构[{"sourceField":"主表连接字段英文名","sourceTable":"主表英文名","joinType":"left","targetField":"连接表连接字段英文名","targetTable": "连接表英文名"}]',
    `sql`            longtext DEFAULT NULL COMMENT '生成联合查询sql',
    `create_by`      int  DEFAULT NULL COMMENT '创建人',
    `create_time`    datetime DEFAULT NULL COMMENT '创建日期',
    `update_by`      int  DEFAULT NULL COMMENT '更新人',
    `update_time`    datetime DEFAULT NULL COMMENT '更新日期',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='视图元数据的视图信息表';