CREATE TABLE  IF NOT EXISTS `app_config` (
  `type` varchar(20) NOT NULL COMMENT '类型；每个类型应该都是唯一的，例如系统名称',
  `config` json NOT NULL COMMENT '配置信息',
  `create_by` int DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_by` int DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci  COMMENT='前端配置表；用于保存前端的一些配置信息';

INSERT IGNORE INTO app_config (`type`, config, create_by, create_time, update_by, update_time) VALUES('SYSTEM_INFO', '{"type": "SYSTEM_INFO", "systemName": "TRS数据中台"}', 1361, '2024-12-05 15:18:17', 1361, '2024-12-05 15:18:17');
