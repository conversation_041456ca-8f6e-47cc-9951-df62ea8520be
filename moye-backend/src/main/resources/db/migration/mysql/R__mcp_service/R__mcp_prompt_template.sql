INSERT IGNORE INTO mcp_prompt_template
    (`type`, template)
VALUES ('INDICATOR_SQL', '你的任务是：根据用户提供的任务描述，和以下的任务说明，生成一个统计数据的sql模板。

任务说明：
1. 生成的sql模板需要符合以下规则：
  a. 时间范围条件的值使用占位符，开始时间用#{统计开始时间}，结束时间用#{统计结束时间}
  b. 其他筛选条件统一使用占位符#{业务逻辑条件}；条件前面要加and
  c. 除了业务需要的字段外，还需要额外统计范围开始时间：#{统计开始时间} AS statistic_start_time、统计范围结束时间：#{统计结束时间} AS statistic_end_time、入库时间：#{入库时间} as insert_time的字段作为审计字段
  d. 查询字段需要取英文别名；分组字段的别名必须使用原字段名
  e. 分组字段只能使用下面给出的字段，不需要对时间字段进行分组
2. 返回结果使用markdown格式的sql代码块包裹，输出结果前需要校验sql格式是否合法，下面是参考输出格式（必须注意换行）：
```sql
SELECT
    notice_sub_type,
    COUNT(*) AS count,
    #{统计开始时间} AS statistic_start_time,
    #{统计结束时间} AS statistic_end_time,
    #{入库时间} AS insert_time
FROM notice
WHERE
    publish_time >= #{统计开始时间}
    AND publish_time <= #{统计结束时间}
    AND #{业务逻辑条件}
GROUP BY notice_sub_type
```
');
INSERT IGNORE INTO mcp_prompt_template
    (`type`, template)
VALUES ('FIELD_TRANSLATE', '你的任务是：根据用户提供的表信息，和以下的任务说明，对表中的字段名进行翻译并提供合适的中文名和描述
任务说明：
1. 生成的字段信息需要符合以下规则：
  a. 字段信息由以下属性组成：enName（英文名）, zhName（中文名）, type（字段类型）, description（字段描述）
  b. 字段的类型有以下几种：STRING, INT, LONG, DOUBLE, DATE, DATETIME, BOOLEAN
  c. 根据字段的英文名和提供的示例数据，生成字段的中文名和描述
  d. 如果字段已经用英文名作为中文名，需要生成中文的中文名并进行替换
3. 返回结果使用markdown格式的json代码块包裹，输出结果前需要校验json格式是否合法，参考输出格式：
```json
[
        {
            "enName":"name",
            "zhName":"姓名",
            "type":"STRING",
            "description":"人员姓名"
        }
    ]
```
');
INSERT IGNORE INTO mcp_prompt_template
    (`type`, template)
VALUES ('SQL_FIELD_TRANSLATE', '你的任务是：根据用户提供的sql，和以下的任务说明，将sql的字段翻译成系统可以使用的字段信息
任务说明：
1. 生成的字段信息需要符合以下规则：
  a. 字段信息由以下属性组成：enName（英文名）, zhName（中文名）, type（字段类型）, description（字段描述）
  b. 字段的类型有以下几种：STRING, INT, LONG, DOUBLE, DATE, DATETIME, BOOLEAN
  c. 根据sql所查询的表提供的字段描述和sql查询方式，生成字段的中文名和描述
3. 返回结果使用markdown格式的json代码块包裹，输出结果前需要校验json格式是否合法，参考输出格式：
```json
[
        {
            "enName":"xxx",
            "zhName":"xxx",
            "type":"STRING",
            "description":"xxx"
        }
    ]
```
');