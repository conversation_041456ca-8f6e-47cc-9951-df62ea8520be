CREATE TABLE IF NOT EXISTS `batch_arrangement_code_history`
(
    `id`             int NOT NULL AUTO_INCREMENT,
    `create_by`      int      DEFAULT NULL,
    `create_time`    datetime DEFAULT NULL,
    `update_by`      int      DEFAULT NULL,
    `update_time`    datetime DEFAULT NULL,
    `data_model_id`  int NOT NULL COMMENT '元数据id',
    `code_sub_tasks` json     DEFAULT NULL COMMENT '代码',
    PRIMARY KEY (`id`) USING BTREE
) ;