<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.xxljob.dao.XxlJobRemoveRecordMapper">
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.trs.ai.moye.xxljob.entity.XxlJobRemoveRecord">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="remove_time" jdbcType="TIMESTAMP" property="removeTime"/>
        <result column="xxl_job_id" jdbcType="INTEGER" property="xxlJobId"/>
        <result column="xxl_job_name" jdbcType="VARCHAR" property="xxlJobName"/>
        <result column="xxl_job_app_name" jdbcType="VARCHAR" property="xxlJobAppName"/>
        <result column="is_error" jdbcType="INTEGER" property="isError"/>
        <result column="reason" jdbcType="LONGVARCHAR" property="reason"/>
    </resultMap>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        *
        from xxl_job_remove_record
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectByIdCollection" resultMap="BaseResultMap">
        select
        *
        from xxl_job_remove_record
        <where>
            <choose>
                <when test="idCollection == null or idCollection.size == 0">
                    and false
                </when>
                <otherwise>
                    and id in
                    <foreach close=")" collection="idCollection" item="id" open="(" separator=",">
                        #{id,jdbcType=INTEGER}
                    </foreach>
                </otherwise>
            </choose>
        </where>
    </select>
    <select id="selectAllXxlJobId" resultType="integer">
        (SELECT xxl_job_id FROM data_model_schedule_config WHERE xxl_job_id IS NOT NULL
         UNION
         SELECT JSON_UNQUOTE(JSON_EXTRACT(schedule_info, '$.realTimeMonitorXxlJobId'))
         FROM data_model_schedule_config
         WHERE JSON_EXTRACT(schedule_info, '$.realTimeMonitorXxlJobId') IS NOT NULL)
        UNION
        (SELECT xxl_job_id FROM data_model_monitor_config WHERE is_inuse AND xxl_job_id IS NOT NULL)
    </select>
</mapper>