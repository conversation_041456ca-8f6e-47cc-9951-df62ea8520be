<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.knowledgebase.dao.dynamicrepository.KnowledgeBaseDataMapper">

    <update id="dropTable">
        DROP TABLE IF EXISTS `${tableName}`
    </update>

    <update id="createTable">
        CREATE TABLE `${tableName}`
        (
        `id` int(11) NOT NULL AUTO_INCREMENT
        <if test="columns != null and columns.size() > 0">
            <foreach collection="columns" item="column" open="," separator=",">
                `${column.name}` ${column.typeAndLength} ${column.isNull} COMMENT #{column.comment} ${column.unique}
            </foreach>
        </if>
        , PRIMARY KEY (id) USING BTREE
        )
        ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    </update>

    <update id="changeTableName">
        alter table ${oldTableName} RENAME to ${newTableName}
    </update>

    <update id="addTableColumn">
        ALTER TABLE `${tableName}`
        <foreach collection="columns" item="column" separator=",">
            ADD COLUMN `${column.name}` ${column.typeAndLength} ${column.isNull} ${column.unique}
        </foreach>
    </update>

    <update id="changeTableColumn">
        ALTER TABLE `${tableName}`
            CHANGE COLUMN `${oldZhName}` `${column.name}` ${column.typeAndLength} ${column.isNull} ${column.unique}
    </update>

    <insert id="insertEntityData">
        insert into `${tableName}`
        <foreach collection="columns" item="column" open="(" separator="," close=")">
            `${column.name}`
        </foreach>
        value
        <foreach collection="columns" item="column" open="(" separator="," close=")">
            #{column.value}
        </foreach>
    </insert>

    <insert id="insertTableData" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into `${param.tableName}`
        <foreach collection="param.keyValueList" item="keyValue" open="(" separator="," close=")">
            `${keyValue.name}`
        </foreach>
        values
        <foreach collection="param.keyValueList" item="keyValue" open="(" separator="," close=")">
            #{keyValue.value}
        </foreach>
    </insert>
    <insert id="insertEntityCommon">
        REPLACE INTO `${tableName}`
        <foreach collection="titles" item="title" open="(" separator="," close=")">
            `${title}`
        </foreach>
        VALUES
        <foreach collection="resultList" item="list" separator=",">
            <foreach collection="list" item="c" open="(" close=")" separator=",">
                #{c}
            </foreach>
        </foreach>
    </insert>

    <update id="updateTableData">
        update `${tableName}` set
        <foreach collection="columns" item="column" separator=",">
            `${column.name}` = #{column.value}
        </foreach>
        where id = #{dataId}
    </update>
    <update id="copyTable">
        CREATE TABLE IF NOT EXISTS `${backupsTableName}`
        SELECT *
        FROM `${tableName}`;
        ALTER TABLE `${backupsTableName}`
            MODIFY COLUMN `id` int (11) NOT NULL AUTO_INCREMENT FIRST,
            ADD PRIMARY KEY (`id`);
    </update>

    <delete id="deleteTableData">
        delete
        from `${tableName}`
        where id = #{dataId}
    </delete>

    <select id="selectTableDataList" resultType="map">
        select
        *
        from
        `${tableName}`
        <where>
            <if test="searchable != null and searchable.fields != null and searchable.keyword != null and searchable.keyword != ''">
                <foreach collection="searchable.fields" item="field" open="AND (" separator="or" close=")">
                    (`${field}` = #{searchable.keyword} or `${field}` like concat( '%',#{searchable.keyword}, '%' ))
                </foreach>
            </if>
        </where>
        <if test="sortable != null and sortable.field != null and sortable.field != ''">
            order by `${sortable.field}` ${sortable.order}
        </if>
    </select>


    <select id="selectTableDataByIds" resultType="map">
        select * from `${tableName}`
        <foreach collection="ids" item="id" open="where id in (" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectOneByColNameAndValue" resultType="map">
        select *
        from `${tableName}`
        where `${columnName}` = #{columnValue} limit 1
    </select>


    <delete id="deleteTableColumns">
        <foreach collection="attributeZhNames" item="attributeZhName" separator=";">
            <![CDATA[
            ALTER TABLE `${entityTableName}`
            DROP COLUMN `${attributeZhName}`
        ]]>
        </foreach>
    </delete>

    <select id="existTable" resultType="boolean">
        select count(table_name) > 0
        from information_schema.TABLES
        where table_name = #{tableName}
    </select>
    <select id="selectTableData" resultType="java.util.Map">
        select *
        from `${tableName}`
    </select>

    <delete id="deleteTableDataByIds">
        delete from `${tableName}`
        <foreach collection="ids" item="id" open="where id in (" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getConditionPageList" resultType="map">
        select
        *
        from
        `${tableName}`
        <where>
            ${conditions}
        </where>
        <if test="sortable != null and sortable.field != null and sortable.field != ''">
            order by `${sortable.field}` ${sortable.order}
        </if>
    </select>

    <select id="getMultiSearchPageList" resultType="map">
        select
        *
        from
        `${tableName}`
        <where>
            <if test="searchable != null and searchable.size() > 0">
                <foreach collection="searchable" item="sc" open="" separator="" close="">
                    <if test="sc != null and sc.fields != null and sc.fields.size() > 0">
                        <foreach collection="sc.fields" item="field" open="AND (" separator="and" close=")">
                            (`${field}` = #{sc.keyword})
                        </foreach>
                    </if>
                </foreach>
            </if>
        </where>
        <if test="sortable != null and sortable.field != null and sortable.field != ''">
            order by `${sortable.field}` ${sortable.order}
        </if>
    </select>


    <select id="selectBaseDataCount" resultType="java.lang.Integer">
        select count(*)
        from `${tableName}`
    </select>

    <select id="selectAttrData" resultType="java.lang.String">
        select DISTINCT `${attrName}`
        from `${tableName}`
        <where>
            <if test="keyword != null and keyword != ''">
                and `${attrName}` like concat( '%', #{keyword}, '%' )
            </if>
            <if test="situation != null and situation != ''">
                and `situation` = #{situation}
            </if>
            and `${attrName}` is not null and `${attrName}` != ''
        </where>
        <if test="sortable != null and sortable.field != null and sortable.field != ''">
            order by `${sortable.field}` ${sortable.order}
        </if>
    </select>

    <select id="selectAccountOfMediaFieldValue" resultType="java.lang.String">
        select distinct `account`
        from `${tableName}`
        <where>
            <if test="situation != null and situation != ''">
                and `situation` = #{situation}
            </if>
            <if test="nature != null and nature != ''">
                and `nature` = #{nature}
            </if>
            <if test="level != null and level != ''">
                and `level` = #{level}
            </if>
            <if test="region != null and region != ''">
                and `region` like concat( '%', #{region}, '%' )
            </if>
        </where>
    </select>

    <select id="selectDataRank" resultType="java.lang.String">
        select `${searchField}`
        from `${tableName}`
        <where>
            and `${searchField}` in
            <foreach collection="keywords" item="word" open="(" separator="," close=")">
                #{word}
            </foreach>
        </where>
        <if test="sortable != null and sortable.field != null and sortable.field != ''">
            order by `${sortable.field}` ${sortable.order}
        </if>
    </select>
</mapper>