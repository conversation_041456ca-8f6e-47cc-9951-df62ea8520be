<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.monitor.dao.BacklogPeriodMapper">
  <resultMap id="BaseResultMap" type="com.trs.ai.moye.monitor.entity.LagPeriod">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <result column="data_model_id" property="dataModelId" jdbcType="INTEGER"/>
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
    <result column="check_count" property="checkCount" jdbcType="INTEGER"/>
    <result column="peak_lag" property="peakLag" jdbcType="BIGINT"/>
    <result column="peak_time" property="peakTime" jdbcType="TIMESTAMP"/>
    <result column="storage_time" property="storageTime" jdbcType="TIMESTAMP"/>
  </resultMap>
</mapper>