<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.monitor.dao.BatchTaskRecordAccessMapper">

  <select id="selectBatchTaskAccessCount" resultType="java.lang.Long">

    select sum(storage_success_count) from batch_task_record where layer = #{layer};
  </select>
</mapper>