<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.backstage.dao.OperateLogMapper">

    <resultMap id="BaseResultMap" type="com.trs.moye.base.common.log.operate.OperateLog">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="operate_time" property="operateTime" jdbcType="TIMESTAMP"/>
        <result column="ip" property="ip" jdbcType="VARCHAR"/>
        <result column="module" property="module" jdbcType="VARCHAR"/>
        <result column="operate_type" property="operateType" jdbcType="VARCHAR"/>
        <result column="api_name" property="apiName" jdbcType="VARCHAR"/>
        <result column="api_path" property="apiPath" jdbcType="VARCHAR"/>
        <result column="operate_result" property="operateResult" jdbcType="VARCHAR"/>
        <result column="details" property="details" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="response_uration" property="responseDuration" jdbcType="INTEGER"/>
    </resultMap>
    <select id="pageList" resultMap="BaseResultMap">
        select 
        * 
        from operate_log
        <where>
            <if test="operateType != null">
                and operate_type = #{operateType}
            </if>
            <if test="operateResult != null">
                and operate_result = #{operateResult}
            </if>
            <if test="startTime != null and startTime != ''">
                and operate_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and operate_time &lt;= #{endTime}
            </if>
            <if test="searchParams != null and searchParams.fields != null and searchParams.keyword != null and searchParams.keyword != ''">
                <foreach collection="searchParams.fields" item="key" open="and (" separator=" or " close=")">
                    ${key} like CONCAT('%', #{searchParams.keyword}, '%')
                </foreach>
            </if>
        </where>
        order by operate_time desc
    </select>
</mapper>