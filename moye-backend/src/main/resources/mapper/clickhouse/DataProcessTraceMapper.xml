<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.monitor.dao.DataProcessTraceMapper">
    <select id="selectNodeAccessCountList" resultType="com.trs.ai.moye.monitor.entity.NodeAccessCountInfo">
        SELECT
        data_source_id as dataModelId,
        COUNT(*) AS accessCount
        FROM data_process_trace
        WHERE parent_processing_node = 'DATA_ACCESS' and storage_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        GROUP BY data_source_id
    </select>
</mapper>