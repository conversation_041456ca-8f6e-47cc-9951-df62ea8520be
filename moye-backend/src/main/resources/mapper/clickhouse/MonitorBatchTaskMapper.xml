<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.trs.ai.moye.monitor.dao.MonitorBatchTaskMapper">

  <!-- 首页展示的前六条数据，根据时间以及monitorValue的值倒序排序 -->
  <select id="selectHomePageList" resultType="com.trs.moye.base.monitor.entity.MonitorBatchTask">
    SELECT *
    FROM (
           SELECT
             id,
             data_model_id   AS dataModelId,
             data_model_name AS dataModelName,
             config_id       AS configId,
             config_version  AS configVersion,
             monitor_time    AS monitorTime,
             storage_time    AS storageTime,
             monitor_value   AS monitorValue,
             monitor_detail  AS monitorDetail,
             layer
           FROM (
                  SELECT
                    *,
                    ROW_NUMBER() OVER (
                PARTITION BY data_model_id
                ORDER BY monitor_time DESC
            ) AS rn_time
                  FROM t_monitor_batch_task
                )
           WHERE rn_time = 1
         ) AS latest_records
    ORDER BY monitorValue DESC
      LIMIT 6;
  </select>

  <!-- 统计页面列表,根据时间倒序，需要根据dataModelId分组，对monitorValue求和,也要根据data_model_name进行模糊收缩 -->
  <select id="monitorStatisticsList" resultType="com.trs.ai.moye.monitor.response.MonitorBatchTaskResponse">
    SELECT
    data_model_id AS id,
    COUNT(*) AS amount,
    SUM(monitor_value) AS increment,
    MIN(monitor_time) AS startTime,
    MAX(monitor_time) AS endTime
    FROM t_monitor_batch_task
    <where>
      layer = #{layer}
      <if test="request != null">
        <bind name="dataModelName" value="request.dataModelName"/>
        <if test="dataModelName != null and dataModelName != ''">
          AND data_model_name LIKE concat('%', #{dataModelName,jdbcType=VARCHAR}, '%')
        </if>
      </if>
    </where>
    GROUP BY data_model_id
    <choose>
      <when test="sortable == null">
        ORDER BY endTime DESC
      </when>
      <otherwise>
        ORDER BY ${sortable.field} ${sortable.order}
      </otherwise>
    </choose>
  </select>

  <!-- 元数据监控详情列表 -->
  <select id="monitorDetailTable" resultType="com.trs.moye.base.monitor.entity.MonitorBatchTask">
    SELECT
    id,
    data_model_id AS dataModelId,
    data_model_name AS dataModelName,
    config_id AS configId,
    config_version AS configVersion,
    monitor_time AS monitorTime,
    storage_time AS storageTime,
    monitor_value AS monitorValue,
    monitor_detail AS monitorDetail,
    layer
    FROM t_monitor_batch_task
    <where>
      <if test="dataModelId != null">
        AND data_model_id = #{dataModelId}
      </if>
      <if test="request != null">
        <bind name="configId" value="request.configId"/>
        <bind name="beginDateTime" value="timeParam.minTime"/>
        <bind name="endDateTime" value="timeParam.maxTime"/>
        <if test="configId != null">
          AND config_id = #{configId}
        </if>
        <if test="beginDateTime != null">
          AND monitor_time &gt;= #{beginDateTime}
        </if>
        <if test="endDateTime != null">
          AND monitor_time &lt;= #{endDateTime}
        </if>
      </if>
    </where>
    ORDER BY monitor_time DESC
  </select>
  <select id="selectTrend" resultType="com.trs.ai.moye.monitor.response.MonitorTrendResponse">
    SELECT
      monitor_time AS time,
      monitor_value AS value
    FROM t_monitor_batch_task
    WHERE data_model_id = #{dataModelId}
      AND config_id = #{configId}
    ORDER BY monitor_time DESC
      LIMIT #{count}
  </select>
  <select id="selectTrendByTimeRange" resultType="com.trs.ai.moye.monitor.response.MonitorTrendResponse">
    SELECT
      monitor_time AS time,
      monitor_value AS value
    FROM t_monitor_batch_task
    WHERE data_model_id = #{dataModelId}
      AND monitor_time &gt;= #{beginTime}
      AND monitor_time &lt;= #{endTime}
    ORDER BY monitor_time ASC
  </select>
</mapper>