<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.monitor.dao.DataAccessTraceMapper">
    <select id="selectAccessAllCount" resultType="com.trs.ai.moye.monitor.response.statistics.StatisticalResult">
        SELECT
        sum(total_count) AS totalCount,
        sum(fail_count) AS exceptionCount,
        sum(success_count) AS normalCount,
        sumIf(total_count, toStartOfDay(time) = toStartOfDay(now())) AS thisDodTotalAmount,
        sumIf(fail_count, toStartOfDay(time) = toStartOfDay(now())) AS thisDodExceptionAmount,
        sumIf(success_count, toStartOfDay(time) = toStartOfDay(now())) AS thisDodNormalAmount,
        sumIf(total_count, toStartOfDay(time) >= toStartOfDay(date_sub(now(), INTERVAL 7 DAY))
        AND toStartOfDay(time) &lt; toStartOfDay(now())) AS thisWowTotalAmount,
        sumIf(total_count, toStartOfDay(time) >= toStartOfDay(date_sub(now(), INTERVAL 14 DAY))
        AND toStartOfDay(time) &lt; toStartOfDay(date_sub(now(), INTERVAL 7 DAY))) AS lastWowTotalAmount,
        sumIf(fail_count, toStartOfDay(time) >= toStartOfDay(date_sub(now(), INTERVAL 7 DAY))
        AND toStartOfDay(time) &lt; toStartOfDay(now())) AS thisWowExceptionAmount,
        sumIf(fail_count, toStartOfDay(time) >= toStartOfDay(date_sub(now(), INTERVAL 14 DAY))
        AND toStartOfDay(time) &lt; toStartOfDay(date_sub(now(), INTERVAL 7 DAY))) AS lastWowExceptionAmount,
        sumIf(success_count, toStartOfDay(time) >= toStartOfDay(date_sub(now(), INTERVAL 7 DAY))
        AND toStartOfDay(time) &lt; toStartOfDay(now())) AS thisWowNormalAmount,
        sumIf(success_count, toStartOfDay(time) >= toStartOfDay(date_sub(now(), INTERVAL 14 DAY))
        AND toStartOfDay(time) &lt; toStartOfDay(date_sub(now(), INTERVAL 7 DAY))) AS lastWowNormalAmount
        FROM home_page_ods_storage_statistics
    </select>

    <select id="selectCountByDay"
        resultType="com.trs.ai.moye.monitor.response.statistics.ProcessSegmentationStatistics">
        <!--        select count(data_model_id) as processed,toStartOfDay(access_time) AS dateTime-->
        <!--        from data_access_trace-->
        <!--        where start_time between #{startTime} and #{endTime}-->
        <!--        <if test="taskId != null ">-->
        <!--            and data_model_id = #{taskId}-->
        <!--        </if>-->
        <!--        group by dateTime order by dateTime with fill from toDateTime(#{startTime})-->
        <!--        to toDateTime(#{endTime}) step 86400-->
        select sum(read_success_count+read_fail_count) as processed,toStartOfDay(start_time) AS dateTime
        from storage_task
        where
        layer_id = 1
        and
        start_time between toDateTime(#{startTime}) and toDateTime(#{endTime})
        <if test="taskId != null ">
            and data_model_id = #{taskId}
        </if>
        group by dateTime order by dateTime with fill from toDateTime(#{startTime})
        to toDateTime(#{endTime}) step 86400
    </select>

    <select id="selectCountByHour"
        resultType="com.trs.ai.moye.monitor.response.statistics.ProcessSegmentationStatistics">
        select sum(arraySum(
        arrayMap(
        x -> JSONExtractInt(x, 'successCount'),
        JSONExtractArrayRaw(write_count_info)
        )
        )) AS processed, toStartOfHour(start_time) as dateTime
        from storage_task
        where
        layer_id = 1
        and
        start_time between toDateTime(#{startTime}) and toDateTime(#{endTime})
        <if test="taskId != null ">
            and data_model_id = #{taskId}
        </if>
        group by dateTime order by dateTime
    </select>

    <select id="getDataAccessCountByModelIdAndTimeParams" resultType="java.lang.Long">
        select sum(read_success_count)
        from storage_task
        where
        layer_id = 1
        <if test="dataModelId != null">
            and data_model_id = #{dataModelId}
        </if>
        <if test="startTime != null">
            and start_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and start_time &lt;= #{endTime}
        </if>
    </select>

    <select id="selectAccessTodayCount" resultType="java.lang.Long">
        select sum(read_success_count) + sum(read_fail_count)
        from storage_task
        where toStartOfDay(start_time) = toStartOfDay(now())
          and layer_id = 1;
    </select>


    <select id="selectAccessCountByModelLayerId" resultType="java.lang.Long">
        select sum(read_success_count) + sum(read_fail_count)
        from storage_task
        where layer_id = #{layerId};
    </select>


    <select id="selectPage" resultType="com.trs.ai.moye.monitor.entity.DataAccessTrace">
        select * from data_access_trace
        <where>
            `batch_no` = #{batchNo}
            <if test="searchParams!=null and searchParams.fields!=null and searchParams.keyword!=null and searchParams.keyword != ''">
                and `data` like CONCAT('%', #{searchParams.keyword}, '%')
            </if>
            <if test="isError!=null">
                and `is_error` = #{isError}
            </if>
            <if test="storageName !=null and storageName != ''">
                and `storage_name` = #{storageName}
            </if>
        </where>
    </select>


    <sql id="SortCond">
        <if test="sortParams != null and sortParams.field != null">
            order by
            <if test="sortParams.field.toString.equals('accessCount')">
                accessCount
            </if>
            <if test="sortParams.field.toString.equals('errorCount')">
                `read_fail_count`
            </if>
            <if test="sortParams.field.toString.equals('successCount')">
                `read_success_count`
            </if>
            <if test="@java.util.Objects@nonNull(sortParams.order) and sortParams.order.toString.equals('ASC')">
                asc
            </if>
            <if test="@java.util.Objects@nonNull(sortParams.order) and sortParams.order.toString.equals('DESC')">
                desc
            </if>
        </if>
    </sql>

    <select id="selectErrorByBatchNo" resultType="com.trs.ai.moye.monitor.entity.DataAccessTrace">
        select * from data_access_trace where batch_no = #{batchNo} and is_error = true
    </select>
</mapper>