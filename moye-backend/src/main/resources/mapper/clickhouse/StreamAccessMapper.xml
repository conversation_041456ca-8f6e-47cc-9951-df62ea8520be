<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.homepage.dao.StreamAccessMapper">

  <!-- 基础统计映射 -->
  <resultMap id="baseStatMap" type="com.trs.ai.moye.homepage.entity.BaseStat">
    <result property="total" column="total" javaType="long"/>
    <result property="normal" column="normal" javaType="long"/>
    <result property="abnormal" column="abnormal" javaType="long"/>
  </resultMap>


  <select id="selectAccessStatsByLayerId" resultType="com.trs.ai.moye.homepage.entity.BaseStat">
    SELECT
    SUM(read_success_count + read_fail_count) AS total,
    SUM(read_success_count) AS normal,
    SUM(read_fail_count) AS abnormal
    FROM storage_task
    WHERE
    layer_id = #{layerId}
    AND
    <if test="startTime != null">
      start_time &gt; #{startTime}
      AND
    </if>
    start_time &lt;= #{endTime}
  </select>
  <select id="selectStorageStatsByLayerId" resultType="com.trs.ai.moye.homepage.entity.BaseStat">
    SELECT
    SUM(JSONExtractInt(elem, 'successCount') + JSONExtractInt(elem, 'failCount')) as total,
    SUM(JSONExtractInt(elem, 'successCount')) AS normal,
    SUM(JSONExtractInt(elem, 'failCount')) AS abnormal
    FROM storage_task
    ARRAY JOIN JSONExtractArrayRaw(write_count_info) AS elem
    WHERE
    layer_id = #{layerId}
    AND
    <if test="startTime != null">
      start_time &gt; #{startTime}
      AND
    </if>
    start_time &lt;= #{endTime}
  </select>
</mapper>