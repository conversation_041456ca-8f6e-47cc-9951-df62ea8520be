<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.monitor.dao.DataConnectionHealthRecordMapper">

    <select id="detailList" resultType="com.trs.ai.moye.monitor.entity.DataConnectionHealthRecord">
        select * from data_connection_health_record
        where connection_id = #{connectionId}
        <if test="beginTime != null">
            and detection_time >= toDateTime(#{beginTime})
        </if>
        <if test="endTime != null">
            and detection_time &lt;= toDateTime(#{endTime})
        </if>
        <if test="isError!=null">
            and is_error = #{isError}
        </if>
        order by detection_time desc
    </select>

    <select id="selectHealthStatistics" resultType="com.trs.ai.moye.monitor.dto.DataConnectionHealthStatisticsDto">
        SELECT
            connection_id as connectionId,
            COUNT(*) as detectionCount,
            SUM(CASE WHEN is_error = true THEN 1 ELSE 0 END) as errorCount,
            MAX(detection_time) as lastDetectionTime,
            argMax(is_error, detection_time) as lastIsError,
            MAX(CASE WHEN is_error = 1 THEN detection_time END) as lastErrorTime,
            argMax(error_message, CASE WHEN is_error = 1 THEN detection_time ELSE toDateTime(0) END) as lastErrorMessage
        FROM data_connection_health_record dch
        GROUP BY connection_id
    </select>

</mapper>