<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.trs.ai.moye.homepage.dao.HomePageDwdStatisticsMapper">

  <select id="selectAll" resultType="com.trs.ai.moye.homepage.entity.HomePageDwdStatistics">
      select * from home_page_dwd_statistics
      <where>
          <if test="type!=null and type!=''">
              type = #{type,jdbcType=VARCHAR}
          </if>
          <if test="dataModelIds!=null and dataModelIds.size() > 0">
              and data_model_id in
              <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
                  #{dataModelId}
              </foreach>
          </if>
          <if test="startTime != null and startTime != ''">
              and time &gt;= toDate(#{startTime})
          </if>
          <if test="endTime != null and endTime !=''">
              and time &lt;= toDate(#{endTime})
          </if>
      </where>
  </select>

  <select id="selectSuccessLineByDay" resultType="com.trs.ai.moye.monitor.response.statistics.ProcessSegmentationStatistics">
      select sum(success_count) as processed,toUnixTimestamp(toDateTime(time)) as date_time
      from home_page_dwd_statistics
      where  type = #{type}
      <if test="startTime != null">
          and time >= toDate(#{startTime})
      </if>
      <if test="endTime != null">
          and time &lt;= toDate(#{endTime})
      </if>
      <if test="dataModelIds!=null and dataModelIds.size() > 0">
          and data_model_id in
          <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
              #{dataModelId}
          </foreach>
      </if>
      group by time
  </select>

  <select id="selectFailLineByDay"
        resultType="com.trs.ai.moye.monitor.response.statistics.ProcessSegmentationStatistics">
      select sum(fail_count) as processed,toUnixTimestamp(toDateTime(time)) as date_time
      from home_page_dwd_statistics
      where
      type = #{type}
      <if test="startTime != null">
      and time >= toDate(#{startTime})
        </if>
      <if test="endTime != null">
          and time &lt;= toDate(#{endTime})
      </if>
      <if test="dataModelIds!=null and dataModelIds.size() > 0">
          and data_model_id in
          <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
              #{dataModelId}
          </foreach>
      </if>
      group by time
    </select>

  <select id="selectSuccessLineByHour"
        resultType="com.trs.ai.moye.monitor.response.statistics.ProcessSegmentationStatistics">
      select SUM(CASE WHEN is_error = 0 THEN 1 ELSE 0 END) AS processed, toHour(start_time) as dateTime
      from batch_task_record btr
      where layer='DWD' and start_time >= toStartOfDay(today())
      <if test="dataModelIds!=null and dataModelIds.size() > 0">
          and task_id in
          <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
              #{dataModelId}
          </foreach>
      </if>
      AND start_time &lt; toStartOfDay(today() + 1) group by dateTime
    </select>

  <select id="selectBatchFailLineByHour"
        resultType="com.trs.ai.moye.monitor.response.statistics.ProcessSegmentationStatistics">
      select SUM(CASE WHEN is_error = 1 THEN 1 ELSE 0 END) AS processed, toHour(start_time) as dateTime
      from batch_task_record btr
      where layer='DWD' and start_time >= toStartOfDay(today())
        AND start_time &lt; toStartOfDay(today() + 1)
      <if test="dataModelIds!=null and dataModelIds.size() > 0">
          and task_id in
          <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
              #{dataModelId}
          </foreach>
      </if>
      group by dateTime
    </select>

  <select id="selectAllLineByDay"
        resultType="com.trs.ai.moye.monitor.response.statistics.ProcessSegmentationStatistics">
      select sum(total_count) as processed,toUnixTimestamp(toDateTime(time)) as date_time
      from home_page_dwd_statistics
      <where>
          type = #{type}
          <if test="startTime != null">
              and time >= toDate(#{startTime})
          </if>
          <if test="endTime != null">
              and time &lt;= toDate(#{endTime})
          </if>
          <if test="dataModelIds!=null and dataModelIds.size() > 0">
              and data_model_id in
              <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
                  #{dataModelId}
              </foreach>
          </if>
      </where>
      group by time
    </select>

  <select id="selectStreamLineByHour"
        resultType="com.trs.ai.moye.monitor.response.statistics.ProcessSegmentationStatistics">
      select count(*) as processed, toHour(start_time) as date_time from data_process_record dpr
      where  start_time >= toStartOfDay(today())
      AND start_time &lt; toStartOfDay(today() + 1)
      <if test="dataModelIds!=null and dataModelIds.size() > 0">
          and data_model_id in
          <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
              #{dataModelId}
          </foreach>
      </if>
      group by date_time
    </select>

  <select id="selectStreamBarChartByDay" resultType="com.trs.ai.moye.monitor.response.statistics.StreamTaskBarChart">
      select sum(total_count) as `count`,data_model_id ,data_model_name
      from home_page_dwd_statistics hpds where type = #{type}
      <if test="startTime != null">
          and time >= toDate(#{startTime})
      </if>
      <if test="endTime != null">
          and time &lt;= toDate(#{endTime})
      </if>
     <if test="dataModelIds != null and dataModelIds.size() >0">
         and data_model_id in
         <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
             #{dataModelId}
         </foreach>
     </if>
      group by data_model_id ,data_model_name order by `count` desc limit #{top}
    </select>

  <select id="selectStreamBarChartByHour" resultType="com.trs.ai.moye.monitor.response.statistics.StreamTaskBarChart">
      select  data_model_id ,data_source_name as data_model_name, count(*) as `count`
      from data_process_record
      where start_time >= toStartOfDay(today())
        AND start_time &lt; toStartOfDay(today() + 1)
      group by data_model_id ,data_source_name order by `count` desc limit #{top};
    </select>

  <select id="selectTotalStorageByDay" resultType="java.lang.Long">
      select sum(success_count) from home_page_dwd_statistics
      where type = #{type}
      <if test="startTime != null">
          and time >= toDate(#{startTime})
      </if>
      <if test="endTime != null">
          and time &lt;= toDate(#{endTime})
      </if>
      <if test="dataModelIds != null and dataModelIds.size() >0">
          and data_model_id in
          <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
               #{dataModelId}
          </foreach>
      </if>
  </select>

  <select id="selectTotalStorageByHour" resultType="java.lang.Long">
      select
          sum(arraySum(
              arrayMap(
                  x -> JSONExtractInt(x, 'successCount'),
                  JSONExtractArrayRaw(write_count_info)
              )
              )) AS success_count
      from storage_task
      where start_time >= toStartOfDay(today())
        AND start_time &lt; toStartOfDay(today() + 1) and layer_id = 2
      <if test="dataModelIds != null and dataModelIds.size()>0">
            and data_model_id in
            <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
                #{dataModelId}
            </foreach>
      </if>
    </select>

  <select id="selectStorageLineByHour"
        resultType="com.trs.ai.moye.monitor.response.statistics.ProcessSegmentationStatistics">
      select sum(arraySum(
      arrayMap(
      x -> JSONExtractInt(x, 'successCount'),
      JSONExtractArrayRaw(write_count_info)
      )
      ))  as processed, toHour(start_time) as dateTime from storage_task st
      where start_time >= toStartOfDay(today())
      AND start_time &lt; toStartOfDay(today() + 1) and layer_id = 2
      <if test="dataModelIds != null and dataModelIds.size()>0">
          and data_model_id in
          <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
              #{dataModelId}
          </foreach>
      </if>
      group by dateTime
    </select>

  <select id="selectOperatorExecuteCount" resultType="java.lang.Long">
      select sum(operator_count) as `count` from home_page_dwd_statistics
      where type = 'STREAM'
    </select>

  <select id="selectMaxSyncTime" resultType="java.time.LocalDateTime">
      select max(sync_time) as syncTime from home_page_dwd_statistics
  </select>

  <update id="updateData">
          UPDATE home_page_dwd_statistics
          SET
          success_count = #{originalData.successCount},
          fail_count = #{originalData.failCount},
          total_count = #{originalData.totalCount},
          sync_time = toTimezone(toDateTime(#{originalData.syncTime}),'Asia/Shanghai')
          WHERE data_model_id = #{originalData.dataModelId}
          and toDate(time) = toDate(#{originalData.time})
          and type = #{originalData.type}
    </update>

  <select id="selectTotalCount" resultType="java.lang.Long">
        select sum(success_count) from  home_page_dwd_statistics where type = 'STORAGE'
    </select>

  <select id="selectByDataModelId" resultType="long">
      select sum(success_count) from home_page_dwd_statistics where type = #{type} and data_model_id = #{dataModelId}
      <if test="startTime != null">
          and time >= toDate(#{startTime})
      </if>
      <if test="endTime != null">
          and time &lt;= toDate(#{endTime})
      </if>
      <if test="null != storageId">
          and storage_id = #{storageId}
      </if>
    </select>
</mapper>