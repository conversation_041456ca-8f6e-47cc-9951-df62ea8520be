<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.trs.ai.moye.homepage.dao.HomePageOdsStorageStatisticsMapper">
    <select id="selectAll" resultType="com.trs.ai.moye.homepage.entity.HomePageOdsStorageStatistics">
        select * from home_page_ods_storage_statistics
        <where>
            data_model_id != -1
            <if test="type!=null and type!=''">
                and type = #{type,jdbcType=VARCHAR}
            </if>
            <if test="dataModelIds!=null and dataModelIds.size() > 0">
                and data_model_id in
                <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
                    #{dataModelId}
                </foreach>
            </if>
            <if test="startTime != null and startTime != ''">
                and time &gt;= toDate(#{startTime})
            </if>
            <if test="endTime != null and endTime !=''">
                and time &lt;= toDate(#{endTime})
            </if>
        </where>
    </select>

    <select id="selectMaxSyncTime" resultType="java.time.LocalDateTime">
        select max(sync_time) as syncTime
        from home_page_ods_storage_statistics
    </select>

    <update id="updateData">
        UPDATE home_page_ods_storage_statistics
        SET success_count = #{originalData.successCount},
            fail_count    = #{originalData.failCount},
            total_count   = #{originalData.totalCount},
            sync_time     = toTimezone(toDateTime(#{originalData.syncTime}),'Asia/Shanghai'),
            storage_name  = #{originalData.storageName}
        WHERE data_model_id = #{originalData.dataModelId}
          and toDate(time) = toDate(#{originalData.time})
    </update>

    <select id="selectTotalCount" resultType="com.trs.ai.moye.monitor.response.statistics.DataStatisticsResponse">
        select sum(total_count) as total, sum(success_count) as normal, sum(fail_count) as exception
        from home_page_ods_storage_statistics
    </select>

    <select id="selectTotalCountToday" resultType="com.trs.ai.moye.monitor.response.statistics.DataStatisticsResponse">
        select sum(total_count) as total, sum(success_count) as normal, sum(fail_count) as exception
        from home_page_ods_storage_statistics where time = toDate(now())
    </select>

    <select id="selectCountByDay"
        resultType="com.trs.ai.moye.monitor.response.statistics.ProcessSegmentationStatistics">
        select sum(success_count) as processed,toStartOfDay(time) AS dateTime
        from home_page_ods_storage_statistics
        where
        time between toDateTime(#{startTime}) and toDateTime(#{endTime})
        <if test="taskId != null ">
            and data_model_id = #{taskId}
        </if>
        group by dateTime order by dateTime
    </select>

    <select id="getTotalCountByTimeParams" resultType="java.lang.Long">
        select sum(total_count) from home_page_ods_storage_statistics
        <where>
            <if test="startTime != null">
                and time >= toDateTime(#{startTime})
            </if>
            <if test="endTime != null">
                and time &lt;= toDateTime(#{endTime})
            </if>
            <if test="accessStatus != null">
                <if test="accessStatus == true">
                    AND success_count > 0
                </if>
                <if test="accessStatus == false">
                    AND fail_count = 0
                </if>
            </if>
        </where>
    </select>

    <select id="getPage" resultType="com.trs.ai.moye.monitor.entity.AccessTaskUnit">
        select data_model_id as id, data_model_name as zhName,
        sum(total_count) as accessCount, sum(fail_count) as errorCount, sum(success_count) as successCount,
        storage_name as storageLocation from home_page_ods_storage_statistics
        <where>
            <if test="keyword != null">
                and data_model_name like concat ('%',#{keyword},'%')
            </if>
            <if test="startTime != null">
                and time >= toDateTime(#{startTime})
            </if>
            <if test="endTime != null">
                and time &lt;= toDateTime(#{endTime})
            </if>
            <if test="errorFlag != null and errorFlag == true">
                and fail_count > 0
            </if>
            <if test="errorFlag != null and errorFlag == false">
                and fail_count = 0
            </if>
        </where>
        group by data_model_id,data_model_name,storage_name
        <include refid="SortCond">
        </include>
    </select>

    <sql id="SortCond">
        <if test="sortParams != null and sortParams.field != null">
            order by
            <if test="sortParams.field.toString.equals('accessCount')">
                accessCount
            </if>
            <if test="sortParams.field.toString.equals('errorCount')">
                errorCount
            </if>
            <if test="sortParams.field.toString.equals('successCount')">
                successCount
            </if>
            <if test="@java.util.Objects@nonNull(sortParams.order) and sortParams.order.toString.equals('ASC')">
                asc
            </if>
            <if test="@java.util.Objects@nonNull(sortParams.order) and sortParams.order.toString.equals('DESC')">
                desc
            </if>
        </if>
    </sql>

    <select id="selectByDataModelId" resultType="com.trs.ai.moye.data.model.response.DataLineageCountVO">
        select sum(total_count) as totalCount, sum(success_count) as successCount, sum(fail_count) as failCount
            from home_page_ods_storage_statistics
        where data_model_id = #{dataModelId}
        <if test="storageId != null">
            and storage_id = #{storageId}
        </if>
        <if test="startTime != null">
            and time >= toDateTime(#{startTime})
        </if>
        <if test="endTime != null">
            and time &lt;= toDateTime(#{endTime})
        </if>
    </select>
</mapper>