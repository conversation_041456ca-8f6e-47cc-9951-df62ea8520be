<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.backstage.dao.ApiLogMapper">

    <resultMap id="BaseResultMap" type="com.trs.moye.base.common.log.api.ApiLog">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="external_log_id" property="externalLogId" jdbcType="VARCHAR"/>
        <result column="application_name" property="applicationName" jdbcType="VARCHAR"/>
        <result column="api_name" property="apiName" jdbcType="VARCHAR"/>
        <result column="api_path" property="apiPath" jdbcType="VARCHAR"/>
        <result column="request_parameters" property="requestParameters" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="request_time" property="requestTime" jdbcType="TIMESTAMP"/>
        <result column="response_status" property="responseStatus" jdbcType="VARCHAR"/>
        <result column="response_duration" property="responseDuration" jdbcType="INTEGER"/>
        <result column="response_details" property="responseDetails" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="connection_type" property="connectionType" jdbcType="VARCHAR"/>
        <result column="connection_name" property="connectionName" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="pageList" resultMap="BaseResultMap">
        select 
        * 
        from api_log
        <where>
            <if test="externalLogId != null and externalLogId != ''">
                and external_log_id = #{externalLogId}
            </if>
            <if test="connectionType != null">
                and connection_type = #{connectionType}
            </if>
            <if test="responseStatus != null">
                and response_status = #{responseStatus}
            </if>
            <if test="startTime != null and startTime != ''">
                and request_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and request_time &lt;= #{endTime}
            </if>
            <if test="searchParams != null and searchParams.fields != null and searchParams.keyword != null and searchParams.keyword != ''">
                <foreach collection="searchParams.fields" item="key" open="and (" separator=" or " close=")">
                    ${key} like CONCAT('%', #{searchParams.keyword}, '%')
                </foreach>
            </if>
        </where>
        order by request_time desc
    </select>
  <select id="selectByLogId" resultType="com.trs.moye.base.common.log.api.ApiLog">

  </select>
</mapper>