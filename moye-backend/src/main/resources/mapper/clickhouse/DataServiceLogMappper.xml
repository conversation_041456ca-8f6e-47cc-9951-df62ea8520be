<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.data.service.dao.DataServiceLogMapper">
  <resultMap id="BaseResultMap" type="com.trs.moye.base.common.log.data.service.DataServiceLog">        <!-- 主键映射 -->
    <id property="logId" column="log_id"/>

    <!-- 普通字段映射 -->
    <result property="serviceName" column="service_name"/>
    <!-- dataModelId在表中无对应字段，需确认表结构是否缺失 -->
    <result property="dataModelId" column="data_model_id"/>
    <result property="businessName" column="business_name"/>
    <result property="dbType" column="db_type"/>
    <result property="storageName" column="storage_name"/>

    <!-- 使用自定义Jackson类型处理器处理Map字段 -->
    <result property="requestParameters" column="request_parameters"
      typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>

    <!-- 时间字段映射，MyBatis通常自动处理LocalDateTime -->
    <result property="requestTime" column="request_time"/>

    <!-- 枚举类型映射，存储枚举名称 -->
    <result property="responseStatus" column="response_status" jdbcType="VARCHAR"/>

    <!-- 响应时长 -->
    <result property="responseDuration" column="response_duration"/>
  </resultMap>

  <select id="getPageList" resultMap="BaseResultMap">
    select
    *
    from data_service_log
    <where>
      and service_id = #{serviceId}
      <if test="responseStatus != null">
        and response_status = #{responseStatus}
      </if>
      <if test="startTime != null and startTime != ''">
        and request_time &gt;= #{startTime}
      </if>
      <if test="endTime != null and endTime != ''">
        and request_time &lt;= #{endTime}
      </if>
      <if
        test="searchParams != null and searchParams.fields != null and searchParams.keyword != null and searchParams.keyword != ''">
        <foreach collection="searchParams.fields" item="key" open="and (" separator=" or " close=")">
          request_parameters like CONCAT('%', #{searchParams.keyword}, '%')
        </foreach>
      </if>
    </where>
    order by request_time desc
  </select>
  <select id="selectByLogId" resultMap="BaseResultMap">
    select
    *
    from data_service_log
    where log_id = #{logId}
  </select>
  <select id="getIncrement" resultType="java.lang.Long">
    select
      count(1)
    from data_service_log
    <where>
      <if test="startTime!=null">
        request_time &gt; #{startTime} and
      </if>
      request_time &lt;= #{endTime}
    </where>
  </select>
</mapper>