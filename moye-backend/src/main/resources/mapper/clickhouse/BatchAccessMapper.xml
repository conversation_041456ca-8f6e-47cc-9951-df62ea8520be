<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.homepage.dao.BatchAccessMapper">

  <!-- 基础统计映射 -->
  <resultMap id="baseStatMap" type="com.trs.ai.moye.homepage.entity.BaseStat">
    <result property="total" column="total" javaType="long"/>
    <result property="normal" column="normal" javaType="long"/>
    <result property="abnormal" column="abnormal" javaType="long"/>
  </resultMap>


  <select id="selectStorageStatsByLayerId" resultType="com.trs.ai.moye.homepage.entity.BaseStat">
    SELECT
    SUM(storage_success_count) AS total,
    SUM(storage_success_count) AS normal
    FROM batch_task_record
    WHERE
    layer = #{layer}
    AND
    <if test="startTime != null">
      start_time &gt; #{startTime}
      AND
    </if>
    start_time &lt;= #{endTime}
  </select>
</mapper>