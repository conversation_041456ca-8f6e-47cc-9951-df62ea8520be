<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.backstage.dao.ApiLogTraceMapper">
    <resultMap id="ApiLogTraceResultMap" type="com.trs.moye.base.common.log.api.ApiLogTrace">
        <id property="id" column="id" jdbcType="INTEGER" />
        <result property="logId" column="log_id" jdbcType="INTEGER" />
        <result property="node" column="node" jdbcType="VARCHAR" />
        <result column="application_name" property="applicationName" jdbcType="VARCHAR"/>
        <result property="createTimestamp" column="create_timestamp" jdbcType="INTEGER" />
        <result property="executeDuration" column="execute_duration" jdbcType="INTEGER" />
        <result property="executeResult" column="execute_result" jdbcType="VARCHAR" />
        <result property="inputDetails" column="input_details" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler" />
        <result property="outputDetails" column="output_details" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler" />

    </resultMap>
    <select id="selectByLogId" resultMap="ApiLogTraceResultMap">
        select * from api_log_trace where log_id = #{logId, jdbcType=INTEGER} order by create_timestamp asc;
    </select>
</mapper>