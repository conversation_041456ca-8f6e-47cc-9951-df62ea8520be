<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.homepage.dao.DashBoardMapper">

  <insert id="insertData">
    INSERT INTO dashboard_data (id ,category, metric, value, created_at)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.id} ,#{item.category}, #{item.metric}, #{item.value}, #{item.createdAt})
    </foreach>
  </insert>

  <select id="getDashBoardDataLast" resultType="com.trs.ai.moye.homepage.entity.DashBoardData">
    WITH latest_data AS (
      SELECT
        category,
        metric,
        argMax(value, created_at) AS latest_value, -- 获取最新的 value
        max(created_at) AS latest_created_at      -- 获取最新的 created_at
      FROM
        dashboard_data
      GROUP BY
        category, metric                          -- 按 category 和 metric 分组
    )
    SELECT
      category,
      metric,
      latest_value as value,
      latest_created_at as created_at
    FROM
      latest_data
    ORDER BY
      category,
      metric;
  </select>
  <select id="getExecutedOperatorTotalCount" resultType="java.lang.Long">
    select sum(executed_operator_count)
    from data_process_record;
  </select>

  <select id="getExecutedOperatorIncrementCount" resultType="java.lang.Long">
    select sum(executed_operator_count)
    from data_process_record
    <where>
      <if test="startTime != null">
        start_time &gt; #{startTime} and
      </if>
        start_time &lt;= #{endTime}
    </where>
  </select>
  <select id="getDataModelCount" resultType="java.lang.Long">
    select value
    from dashboard_data
    where category = #{category}
      and metric = #{metric}
    ORDER BY created_at DESC limit 1;
  </select>
</mapper>