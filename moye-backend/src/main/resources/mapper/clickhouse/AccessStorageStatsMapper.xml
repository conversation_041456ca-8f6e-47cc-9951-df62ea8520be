<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.trs.ai.moye.homepage.dao.StorageStatsMapper">

  <!-- 基础统计映射 -->
  <resultMap id="baseStatMap" type="com.trs.ai.moye.homepage.entity.BaseStat">
    <result property="total" column="total" javaType="long"/>
    <result property="normal" column="normal" javaType="long"/>
    <result property="abnormal" column="abnormal" javaType="long"/>
  </resultMap>


  <insert id="insertStats">
    <!-- 插入统计数据 -->
    insert into storage_task_aggregated (stat_type, total_count, normal_count, abnormal_count,last_stat_time)
    values
    <foreach collection="stats" item="item" separator=",">
      (#{item.statType}, #{item.total}, #{item.normal}, #{item.abnormal},#{item.lastStatTime})
    </foreach>
  </insert>
  <update id="updateStats">
    INSERT INTO storage_task_aggregated
    (stat_type, total_count, normal_count, abnormal_count, last_stat_time)
    VALUES (
             #{newStat.statType, typeHandler=org.apache.ibatis.type.EnumTypeHandler},
             #{newStat.total},
             #{newStat.normal},
             #{newStat.abnormal},
             #{newStat.lastStatTime,jdbcType=DATE}
           )
  </update>

  <select id="selectStatsByType" resultType="com.trs.ai.moye.homepage.entity.BaseStat">
    select total_count    as total,
           normal_count   as normal,
           abnormal_count as abnormal,
           last_stat_time as lastStatTime
    from storage_task_aggregated FINAL
    where stat_type = #{statType};
  </select>

  <select id="selectAccessStats" resultMap="baseStatMap">
    SELECT
    SUM(read_success_count + read_fail_count) AS total,
    SUM(read_success_count) AS normal,
    SUM(read_fail_count) AS abnormal
    FROM storage_task
    WHERE
    <if test="startTime != null">
      start_time &gt; #{startTime}
      AND
    </if>
    start_time &lt;= #{endTime}
  </select>
  <select id="selectLastStatTime" resultType="java.time.LocalDateTime">
    SELECT
      MAX(last_stat_time)
    FROM storage_task_aggregated
  </select>

  <select id="selectStatList" resultType="com.trs.ai.moye.homepage.entity.AggregatedStat">
    select total_count as total,
           normal_count as normal,
           abnormal_count as abnormal,
           stat_type as statType,
           last_stat_time as lastStatTime
    from storage_task_aggregated;
  </select>
  <select id="selectTaskStats" resultType="com.trs.ai.moye.homepage.entity.BaseStat">
    select
    count(*) as total,
    countIf(execution_status = 'FINISHED') as abnormal,
    countIf(execution_status = 'EXECUTION_FAILED') as normal
    from storage_task
    where
    <if test="startTime != null">
      start_time &gt; #{startTime}
      and
    </if>
    start_time &lt;= #{endTime}
    and layer_id = 1
  </select>

  <select id="selectTaskStatsCurrent" resultType="com.trs.ai.moye.homepage.entity.BaseStat">
    select
    count(*) as total,
    countIf(execution_status = 'FINISHED') as abnormal,
    countIf(execution_status = 'EXECUTION_FAILED') as normal
    from storage_task
    where
      start_time &gt; #{startTime}
    and layer_id = 1
  </select>
  <select id="selectLagStats" resultType="com.trs.ai.moye.homepage.entity.BaseStat">
    select sum(lag) as total
    from t_monitor_ods_lag
    where
    <if test="startTime != null">
      monitor_time &gt; #{startTime}
      AND
    </if>
    monitor_time &lt;= #{endTime}
  </select>
  <select id="selectFluctuationStats" resultType="com.trs.ai.moye.homepage.entity.BaseStat">
    select count(*) as total
    from t_monitor_ods_fluctuation
    where
    <if test="startTime != null">
      monitor_time &gt; #{startTime}
      and
    </if>
    monitor_time &lt;= #{endTime}
    and is_threshold_exceeded = 1
  </select>
  <select id="selectCutoffStats" resultType="com.trs.ai.moye.homepage.entity.BaseStat">
    select count(*) as total
    from t_monitor_ods_cutoff
    where
    <if test="startTime != null">
      monitor_time &gt; #{startTime}
      AND
    </if>
    monitor_time &lt;= #{endTime}
    and is_cutoff = 1
  </select>

</mapper>