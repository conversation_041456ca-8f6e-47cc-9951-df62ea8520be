<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.trs.ai.moye.homepage.dao.HomePageOdsScheduleStatisticsMapper">

  <select id="selectAll" resultType="com.trs.ai.moye.homepage.entity.HomePageOdsScheduleStatistics">
      select * from home_page_ods_schedule_statistics
      <where>
          <if test="type!=null and type!=''">
              type = #{type,jdbcType=VARCHAR}
          </if>
          <if test="dataModelIds!=null and dataModelIds.size() > 0">
              and data_model_id in
              <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
                  #{dataModelId}
              </foreach>
          </if>
          <if test="startTime != null and startTime != ''">
              and time &gt;= toDate(#{startTime})
          </if>
          <if test="endTime != null and endTime !=''">
              and time &lt;= toDate(#{endTime})
          </if>
      </where>
  </select>

    <select id="selectMaxSyncTime" resultType="java.time.LocalDateTime">
        select max(sync_time) as syncTime from home_page_ods_schedule_statistics
    </select>

  <update id="updateData">
      UPDATE home_page_ods_schedule_statistics
      SET
          success_count = #{originalData.successCount},
          fail_count = #{originalData.failCount},
          total_count = #{originalData.totalCount},
          sync_time = toTimezone(toDateTime(#{originalData.syncTime}),'Asia/Shanghai')
      WHERE data_model_id = #{originalData.dataModelId}
        and toDate(time) = toDate(#{originalData.time})
    </update>

  <select id="taskList" resultType="com.trs.ai.moye.monitor.response.statistics.TaskStatisticsListResponse">
      select data_model_id,data_model_name as name,access_type as mode,sum(success_count) as successCount,sum(fail_count) as errorCount,
          sum(total_count) as accessCount
      from home_page_ods_schedule_statistics
      <where>
          <if test="keyword != null">
              and data_model_name like concat ('%',#{keyword},'%')
          </if>
          <if test="startTime != null">
              and time >= toDateTime(#{startTime})
          </if>
          <if test="endTime != null">
              and time &lt;= toDateTime(#{endTime})
          </if>
          <if test="errorFlag != null and errorFlag == true">
              and fail_count > 0
          </if>
          <if test="errorFlag != null and errorFlag == false">
              and fail_count = 0
          </if>
          <if test="mode != null">
              and access_type = #{mode}
          </if>
      </where>
      group by data_model_id ,name,mode
      <include refid="SortCond"/>
  </select>

    <sql id="SortCond">
        <if test="sortRequest != null and sortRequest.field != null">
            order by
            <if test="sortRequest.field.toString.equals('accessCount')">
                accessCount
            </if>
            <if test="sortRequest.field.toString.equals('successCount')">
                successCount
            </if>
            <if test="sortRequest.field.toString.equals('errorCount')">
                errorCount
            </if>
            <if test="@java.util.Objects@nonNull(sortRequest.order) and sortRequest.order.toString.equals('ASC')">
                asc
            </if>
            <if test="@java.util.Objects@nonNull(sortRequest.order) and sortRequest.order.toString.equals('DESC')">
                desc
            </if>
        </if>
    </sql>

  <select id="selectAmount" resultType="java.lang.Long">
      select sum(total_count) from home_page_ods_schedule_statistics
      <where>
          <if test="keyword != null">
              and data_model_name like concat ('%',#{keyword},'%')
          </if>
          <if test="startTime != null">
              and time >= toDateTime(#{startTime})
          </if>
          <if test="endTime != null">
              and time &lt;= toDateTime(#{endTime})
          </if>
          <if test="errorFlag != null and errorFlag == true">
              and fail_count > 0
          </if>
          <if test="errorFlag != null and errorFlag == false">
              and fail_count = 0
          </if>
          <if test="mode != null">
              and access_type = #{mode}
          </if>
      </where>
  </select>
</mapper>