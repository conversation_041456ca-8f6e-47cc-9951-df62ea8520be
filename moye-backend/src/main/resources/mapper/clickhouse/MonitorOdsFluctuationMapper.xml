<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.monitor.dao.MonitorOdsFluctuationMapper">
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.trs.moye.base.monitor.entity.MonitorOdsFluctuation">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="data_model_id" jdbcType="INTEGER" property="dataModelId"/>
        <result column="data_model_name" jdbcType="VARCHAR" property="dataModelName"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
        <result column="source_sub_type" jdbcType="VARCHAR" property="sourceSubType"/>
        <result column="config_id" jdbcType="INTEGER" property="configId"/>
        <result column="config_version" jdbcType="VARCHAR" property="configVersion"/>
        <result column="monitor_time" jdbcType="TIMESTAMP" property="monitorTime"/>
        <result column="storage_time" jdbcType="TIMESTAMP" property="storageTime"/>
        <result column="total" jdbcType="BIGINT" property="total"/>
        <result column="increment" jdbcType="BIGINT" property="increment"/>
        <result column="average" jdbcType="BIGINT" property="average"/>
        <result column="fluctuation_type" jdbcType="INTEGER" property="fluctuationType"/>
        <result column="fluctuation" jdbcType="INTEGER" property="fluctuation"/>
        <result column="is_threshold_exceeded" jdbcType="TINYINT" property="isThresholdExceeded"/>
    </resultMap>
    <!-- 属性列表 -->
    <sql id="Base_Column_List">
        <trim suffixOverrides=",">
            id,
            data_model_id,
            data_model_name,
            source_type,
            source_sub_type,
            config_id,
            config_version,
            monitor_time,
            storage_time,
            total,
            `increment`,
            average,
            fluctuation_type,
            fluctuation,
            is_threshold_exceeded,
        </trim>
    </sql>

    <select id="getLatestFluctuationMonitor" resultMap="BaseResultMap">
        SELECT
        data_model_id,
        source_type,
        MAX(storage_time) AS storageTime,
        arrayElement(
        arrayReverseSort(groupArray((storage_time, fluctuation))), 1
        ).2 AS fluctuation -- 获取时间逆序排序后的第一个 fluctuation 值
        FROM t_monitor_ods_fluctuation
        WHERE is_threshold_exceeded = 1
          and layer = #{layer}
        <if test="sourceType != null and sourceType != ''">
            and source_type = #{sourceType,jdbcType=VARCHAR}
        </if>
        <if test="dataModelIds != null">
            and data_model_id in
            <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
                #{dataModelId}
            </foreach>
        </if>
        GROUP BY data_model_id, source_type
        ORDER BY storageTime DESC;
    </select>

    <select id="odsMonitorHomePageStatisticsCount" resultType="java.lang.Long">
        select
        count(*)
        from t_monitor_ods_fluctuation
        <where>
            layer = #{layer}
            <if test="dataModelIds != null">
                and data_model_id in
                <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
                #{dataModelId}
                </foreach>
            </if>
            <if test="sourceType != null and sourceType != ''">
                and source_type = #{sourceType,jdbcType=VARCHAR}
            </if>
            and is_threshold_exceeded = 1
        </where>
    </select>

    <select id="odsMonitorStatisticsList" resultType="com.trs.ai.moye.monitor.response.OdsMonitorStatisticsResponse">
        <bind name="searchParams" value="request.searchParams"/>
        <bind name="sortParams" value="request.sortParams"/>
        SELECT
        data_model_id as id,
        count(*) as amount,
        count(CASE WHEN is_threshold_exceeded = 1 THEN 1 END) as `increment`,
        round(`increment` / amount * 100, 2) as proportion,
        min(monitor_time) as startTime,
        max(monitor_time) as endTime
        FROM t_monitor_ods_fluctuation
        <where>
            layer = #{layer}
            <bind name="sourceType" value="request.sourceType"/>
            <bind name="sourceSubType" value="request.sourceSubType"/>
            <bind name="dataModelName" value="request.dataModelName"/>
            <if test="sourceType != null and sourceType != ''">
                and source_type = #{sourceType,jdbcType=VARCHAR}
            </if>
            <if test="sourceSubType != null and sourceSubType != ''">
                and source_sub_type = #{sourceSubType,jdbcType=VARCHAR}
            </if>
            <if test="dataModelName != null and dataModelName != ''">
                and data_model_name like concat('%', #{dataModelName,jdbcType=VARCHAR}, '%')
            </if>
        </where>
        GROUP BY id
        <choose>
            <when test="sortParams == null">
                ORDER BY amount DESC, endTime DESC
            </when>
            <otherwise>
                ORDER BY ${sortParams.field} ${sortParams.order}, endTime DESC
            </otherwise>
        </choose>
    </select>

    <select id="odsMonitorDetailTable" resultMap="BaseResultMap">
        <bind name="searchParams" value="request.searchParams"/>
        <bind name="sortParams" value="request.sortParams"/>
        select
        <include refid="Base_Column_List"/>
        from t_monitor_ods_fluctuation
        <where>
            <if test="dataModelId != null">
                and data_model_id = #{dataModelId,jdbcType=INTEGER}
            </if>
            <bind name="configId" value="request.configId"/>
            <bind name="isThresholdExceeded" value="request.isFluctuation"/>
            <if test="configId != null">
                and config_id = #{configId,jdbcType=INTEGER}
            </if>
            <if test="isThresholdExceeded != null">
                and is_threshold_exceeded = #{isThresholdExceeded,jdbcType=INTEGER}
            </if>
            <if test="request.timeRangeParams!= null">
                <bind name="beginTime" value="request.timeRangeParams.minTime"/>
                <bind name="endTime" value="request.timeRangeParams.maxTime"/>
                <if test="beginTime != null">
                    and monitor_time &gt;= #{beginTime}
                </if>
                <if test="endTime != null">
                    and monitor_time &lt;= #{endTime}
                </if>
            </if>
        </where>
        ORDER BY monitor_time DESC
    </select>

    <select id="selectLatestByMetaDataIdAndConfigVersion" resultMap="BaseResultMap">
        select *
        from t_monitor_ods_fluctuation
        where data_model_id = #{dataModelId}
          and config_id = #{configId}
        order by monitor_time ASC
        limit ${count}
    </select>

  <select id="selectLatestByDataModelId"
    resultMap="BaseResultMap">
      <bind name="searchParams" value="request.searchParams"/>
      <bind name="sortParams" value="request.sortParams"/>
      select
      <include refid="Base_Column_List"/>
      from t_monitor_ods_fluctuation
      <where>
          <if test="dataModelId != null">
              and data_model_id = #{dataModelId,jdbcType=INTEGER}
          </if>
          <bind name="configId" value="request.configId"/>
          <bind name="isThresholdExceeded" value="request.isFluctuation"/>
          <if test="configId != null">
              and config_id = #{configId,jdbcType=INTEGER}
          </if>
          <if test="isThresholdExceeded != null">
              and is_threshold_exceeded = #{isThresholdExceeded,jdbcType=INTEGER}
          </if>
          <if test="request.timeRangeParams!= null">
              <bind name="beginTime" value="request.timeRangeParams.minTime"/>
              <bind name="endTime" value="request.timeRangeParams.maxTime"/>
              <if test="beginTime != null">
                  and monitor_time &gt;= #{beginTime}
              </if>
              <if test="endTime != null">
                  and monitor_time &lt;= #{endTime}
              </if>
          </if>
      </where>
      ORDER BY monitor_time ASC
  </select>
</mapper>
