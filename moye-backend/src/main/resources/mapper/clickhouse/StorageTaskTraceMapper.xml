<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.monitor.dao.StorageTaskTraceMapper">


    <select id="selectByBatchNo" resultType="com.trs.ai.moye.data.model.entity.StorageTaskTrace">
        select *
        from storage_task_trace
        where batch_no = #{batchNo}
    </select>
    <select id="selectByBatchNoAndNode" resultType="com.trs.ai.moye.data.model.entity.StorageTaskTrace">

        select *
        from storage_task_trace
        where batch_no = #{batchNo}
          and node = #{node}
    </select>
    <select id="selectById" resultType="com.trs.ai.moye.data.model.entity.StorageTaskTrace">
        select * from storage_task_trace where id = #{id}
    </select>


</mapper>