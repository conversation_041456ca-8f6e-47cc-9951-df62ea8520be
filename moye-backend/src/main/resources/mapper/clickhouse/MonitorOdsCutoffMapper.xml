<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.monitor.dao.MonitorOdsCutoffMapper">
  <!-- 结果集映射 -->
  <resultMap id="BaseResultMap" type="com.trs.moye.base.monitor.entity.MonitorOdsCutoff">
    <result column="id" jdbcType="BIGINT" property="id"/>
    <result column="data_model_id" jdbcType="INTEGER" property="dataModelId"/>
    <result column="data_model_name" jdbcType="VARCHAR" property="dataModelName"/>
    <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
    <result column="source_sub_type" jdbcType="VARCHAR" property="sourceSubType"/>
    <result column="config_id" jdbcType="INTEGER" property="configId"/>
    <result column="config_version" jdbcType="VARCHAR" property="configVersion"/>
    <result column="monitor_time" jdbcType="TIMESTAMP" property="monitorTime"/>
    <result column="storage_time" jdbcType="TIMESTAMP" property="storageTime"/>
    <result column="monitor_value" jdbcType="BIGINT" property="monitorValue"/>
    <result column="increment_column" jdbcType="VARCHAR" property="incrementColumn"/>
    <result column="increment_value" jdbcType="VARCHAR" property="incrementValue"/>
    <result column="monitor_detail" jdbcType="VARCHAR" property="monitorDetail"/>
    <result column="is_cutoff" jdbcType="TINYINT" property="isCutoff"/>
  </resultMap>
  <!-- 属性列表 -->
  <sql id="Base_Column_List">
    <trim suffixOverrides=",">
      id,
      data_model_id,
      data_model_name,
      source_type,
      source_sub_type,
      config_id,
      config_version,
      monitor_time,
      storage_time,
      monitor_value,
      increment_column,
      increment_value,
      monitor_detail,
      is_cutoff,
    </trim>
  </sql>
  <!-- 普通插入属性列表，注意与Base_Column_List属性列表一一对应 -->
  <sql id="Insert_Property_List">
    <trim suffixOverrides=",">
      #{data.id,jdbcType=BIGINT},
      #{data.dataModelId,jdbcType=INTEGER},
      #{data.dataModelName,jdbcType=VARCHAR},
      #{data.sourceType,jdbcType=VARCHAR},
      #{data.sourceSubType,jdbcType=VARCHAR},
      #{data.configId,jdbcType=INTEGER},
      #{data.configVersion,jdbcType=VARCHAR},
      #{data.monitorTime,jdbcType=TIMESTAMP},
      #{data.storageTime,jdbcType=TIMESTAMP},
      #{data.monitorValue,jdbcType=BIGINT},
      #{data.incrementColumn,jdbcType=VARCHAR},
      #{data.incrementValue,jdbcType=VARCHAR},
      #{data.monitorDetail,jdbcType=VARCHAR},
      #{data.isCutoff,jdbcType=TINYINT},
    </trim>
  </sql>

  <!-- 普通修改属性列表（非主键属性） -->
  <sql id="Update_Property_List">
    <trim suffixOverrides=",">
      id = #{data.id,jdbcType=BIGINT},
      data_model_id = #{data.dataModelId,jdbcType=INTEGER},
      data_model_name = #{data.dataModelName,jdbcType=VARCHAR},
      source_type = #{data.sourceType,jdbcType=VARCHAR},
      source_sub_type = #{data.sourceSubType,jdbcType=VARCHAR},
      config_id = #{data.configId,jdbcType=INTEGER},
      config_version = #{data.configVersion,jdbcType=VARCHAR},
      monitor_time = #{data.monitorTime,jdbcType=TIMESTAMP},
      storage_time = #{data.storageTime,jdbcType=TIMESTAMP},
      monitor_value = #{data.monitorValue,jdbcType=BIGINT},
      increment_column = #{data.incrementColumn,jdbcType=VARCHAR},
      increment_value = #{data.incrementValue,jdbcType=VARCHAR},
      monitor_detail = #{data.monitorDetail,jdbcType=VARCHAR},
      is_cutoff = #{data.isCutoff,jdbcType=TINYINT},
    </trim>
  </sql>

  <!-- 动态修改属性列表（非主键属性） -->
  <sql id="Update_Selective_Property_List">
    <trim suffixOverrides=",">
      <if test="data.id != null">
        id = #{data.id,jdbcType=BIGINT},
      </if>
      <if test="data.dataModelId != null">
        data_model_id = #{data.dataModelId,jdbcType=INTEGER},
      </if>
      <if test="data.dataModelName != null">
        data_model_name = #{data.dataModelName,jdbcType=VARCHAR},
      </if>
      <if test="data.sourceType != null">
        source_type = #{data.sourceType,jdbcType=VARCHAR},
      </if>
      <if test="data.sourceSubType != null">
        source_sub_type = #{data.sourceSubType,jdbcType=VARCHAR},
      </if>
      <if test="data.configId != null">
        config_id = #{data.configId,jdbcType=INTEGER},
      </if>
      <if test="data.configVersion != null">
        config_version = #{data.configVersion,jdbcType=VARCHAR},
      </if>
      <if test="data.monitorTime != null">
        monitor_time = #{data.monitorTime,jdbcType=TIMESTAMP},
      </if>
      <if test="data.storageTime != null">
        storage_time = #{data.storageTime,jdbcType=TIMESTAMP},
      </if>
      <if test="data.monitorValue != null">
        monitor_value = #{data.monitorValue,jdbcType=BIGINT},
      </if>
      <if test="data.incrementColumn != null">
        increment_column = #{data.incrementColumn,jdbcType=VARCHAR},
      </if>
      <if test="data.incrementValue != null">
        increment_value = #{data.incrementValue,jdbcType=VARCHAR},
      </if>
      <if test="data.monitorDetail != null">
        monitor_detail = #{data.monitorDetail,jdbcType=VARCHAR},
      </if>
      <if test="data.isCutoff != null">
        is_cutoff = #{data.isCutoff,jdbcType=TINYINT},
      </if>
    </trim>
  </sql>

  <!-- 动态查询属性列表（所有属性） -->
  <sql id="Select_Selective_Property_List">
    <trim suffixOverrides=",">
      <if test="query.id != null">
        and id = #{query.id,jdbcType=BIGINT}
      </if>
      <if test="query.dataModelId != null">
        and data_model_id = #{query.dataModelId,jdbcType=INTEGER}
      </if>
      <if test="query.dataModelName != null">
        and data_model_name = #{query.dataModelName,jdbcType=VARCHAR}
      </if>
      <if test="query.sourceType != null">
        and source_type = #{query.sourceType,jdbcType=VARCHAR}
      </if>
      <if test="query.sourceSubType != null">
        and source_sub_type = #{query.sourceSubType,jdbcType=VARCHAR}
      </if>
      <if test="query.configId != null">
        and config_id = #{query.configId,jdbcType=INTEGER}
      </if>
      <if test="query.configVersion != null">
        and config_version = #{query.configVersion,jdbcType=VARCHAR}
      </if>
      <if test="query.monitorTime != null">
        and monitor_time = #{query.monitorTime,jdbcType=TIMESTAMP}
      </if>
      <if test="query.storageTime != null">
        and storage_time = #{query.storageTime,jdbcType=TIMESTAMP}
      </if>
      <if test="query.monitorValue != null">
        and monitor_value = #{query.monitorValue,jdbcType=BIGINT}
      </if>
      <if test="query.incrementColumn != null">
        and increment_column = #{query.incrementColumn,jdbcType=VARCHAR}
      </if>
      <if test="query.incrementValue != null">
        and increment_value = #{query.incrementValue,jdbcType=VARCHAR}
      </if>
      <if test="query.monitorDetail != null">
        and monitor_detail = #{query.monitorDetail,jdbcType=VARCHAR}
      </if>
      <if test="query.isCutoff != null">
        and is_cutoff = #{query.isCutoff,jdbcType=TINYINT}
      </if>
    </trim>
  </sql>

  <insert id="insert">
    insert into t_monitor_ods_cutoff
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Base_Column_List"/>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Insert_Property_List"/>
    </trim>
  </insert>
  <insert id="insertBatch">
    insert into t_monitor_ods_cutoff
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Base_Column_List"/>
    </trim>
    values
    <foreach collection="dataCollection" item="data" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <include refid="Insert_Property_List"/>
      </trim>
    </foreach>
  </insert>
  <update id="updateSelective">
    update t_monitor_ods_cutoff
    <set>
      <include refid="Update_Selective_Property_List"/>
    </set>
    <where>
      <include refid="Select_Selective_Property_List"/>
    </where>
  </update>
  <delete id="deleteSelective">
    delete from t_monitor_ods_cutoff
    <where>
      <include refid="Select_Selective_Property_List"/>
    </where>
  </delete>
  <delete id="deleteById">
    delete from t_monitor_ods_cutoff
    <where>
      and id = #{id,jdbcType=BIGINT}
    </where>
  </delete>
  <delete id="deleteBydataModelId">
    delete from t_monitor_ods_cutoff
    <where>
      and data_model_id = #{dataModelId,jdbcType=INTEGER}
    </where>
  </delete>
  <delete id="deleteByConfigId">
    delete from t_monitor_ods_cutoff
    <where>
      and config_id = #{configId,jdbcType=INTEGER}
    </where>
  </delete>
  <delete id="deleteBySourceCategory">
    delete from t_monitor_ods_cutoff
    <where>
      and source_type = #{sourceType,jdbcType=VARCHAR}
    </where>
  </delete>
  <delete id="deleteBySourceActualType">
    delete from t_monitor_ods_cutoff
    <where>
      and source_sub_type = #{sourceSubType,jdbcType=VARCHAR}
    </where>
  </delete>
  <delete id="deleteByIdCollection">
    delete from t_monitor_ods_cutoff
    <where>
      <choose>
        <when test="idCollection == null or idCollection.size == 0">
          and false
        </when>
        <otherwise>
          and id in
          <foreach close=")" collection="idCollection" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </delete>
  <delete id="deleteBydataModelIdCollection">
    delete from t_monitor_ods_cutoff
    <where>
      <choose>
        <when test="dataModelIdCollection == null or dataModelIdCollection.size == 0">
          and false
        </when>
        <otherwise>
          and data_model_id in
          <foreach close=")" collection="dataModelIdCollection" item="dataModelId" open="(" separator=",">
            #{dataModelId,jdbcType=INTEGER}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </delete>
  <delete id="deleteByConfigIdCollection">
    delete from t_monitor_ods_cutoff
    <where>
      <choose>
        <when test="configIdCollection == null or configIdCollection.size == 0">
          and false
        </when>
        <otherwise>
          and config_id in
          <foreach close=")" collection="configIdCollection" item="configId" open="(" separator=",">
            #{configId,jdbcType=INTEGER}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </delete>
  <delete id="deleteBySourceCategoryCollection">
    delete from t_monitor_ods_cutoff
    <where>
      <choose>
        <when test="sourceTypeCollection == null or sourceTypeCollection.size == 0">
          and false
        </when>
        <otherwise>
          and source_type in
          <foreach close=")" collection="sourceTypeCollection" item="sourceType" open="("
            separator=",">
            #{sourceType,jdbcType=VARCHAR}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </delete>
  <delete id="deleteBySourceActualTypeCollection">
    delete from t_monitor_ods_cutoff
    <where>
      <choose>
        <when test="sourceSubTypeCollection == null or sourceSubTypeCollection.size == 0">
          and false
        </when>
        <otherwise>
          and source_sub_type in
          <foreach close=")" collection="sourceSubTypeCollection" item="sourceSubType" open="("
            separator=",">
            #{sourceSubType,jdbcType=VARCHAR}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </delete>
  <select id="listSelective" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_monitor_ods_cutoff
    <where>
      <include refid="Select_Selective_Property_List"/>
    </where>
  </select>
  <select id="getById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_monitor_ods_cutoff
    <where>
      and id = #{id,jdbcType=BIGINT}
    </where>
  </select>
  <select id="listBydataModelId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_monitor_ods_cutoff
    <where>
      and data_model_id = #{dataModelId,jdbcType=INTEGER}
    </where>
  </select>
  <select id="listByConfigId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_monitor_ods_cutoff
    <where>
      and config_id = #{configId,jdbcType=INTEGER}
    </where>
  </select>
  <select id="listBySourceCategory" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_monitor_ods_cutoff
    <where>
      and source_type = #{sourceType,jdbcType=VARCHAR}
    </where>
  </select>
  <select id="listBySourceActualType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_monitor_ods_cutoff
    <where>
      and source_sub_type = #{sourceSubType,jdbcType=VARCHAR}
    </where>
  </select>
  <select id="listByIdCollection" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_monitor_ods_cutoff
    <where>
      <choose>
        <when test="idCollection == null or idCollection.size == 0">
          and false
        </when>
        <otherwise>
          and id in
          <foreach close=")" collection="idCollection" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </select>
  <select id="listBydataModelIdCollection" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_monitor_ods_cutoff
    <where>
      <choose>
        <when test="dataModelIdCollection == null or dataModelIdCollection.size == 0">
          and false
        </when>
        <otherwise>
          and data_model_id in
          <foreach close=")" collection="dataModelIdCollection" item="dataModelId" open="(" separator=",">
            #{dataModelId,jdbcType=INTEGER}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </select>
  <select id="listByConfigIdCollection" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_monitor_ods_cutoff
    <where>
      <choose>
        <when test="configIdCollection == null or configIdCollection.size == 0">
          and false
        </when>
        <otherwise>
          and config_id in
          <foreach close=")" collection="configIdCollection" item="configId" open="(" separator=",">
            #{configId,jdbcType=INTEGER}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </select>
  <select id="listBySourceCategoryCollection" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_monitor_ods_cutoff
    <where>
      <choose>
        <when test="sourceTypeCollection == null or sourceTypeCollection.size == 0">
          and false
        </when>
        <otherwise>
          and source_type in
          <foreach close=")" collection="sourceTypeCollection" item="sourceType" open="("
            separator=",">
            #{sourceType,jdbcType=VARCHAR}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </select>
  <select id="listBySourceActualTypeCollection" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_monitor_ods_cutoff
    <where>
      <choose>
        <when test="sourceSubTypeCollection == null or sourceSubTypeCollection.size == 0">
          and false
        </when>
        <otherwise>
          and source_sub_type in
          <foreach close=")" collection="sourceSubTypeCollection" item="sourceSubType" open="("
            separator=",">
            #{sourceSubType,jdbcType=VARCHAR}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </select>
  <select id="listQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_monitor_ods_cutoff
    <where>
      <if test="dataModelName != null and dataModelName != ''">
        and data_model_name = #{dataModelName,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

  <!-- 以上代码由MbgCode自动生成2024/8/28 下午4:49 -->


  <!-- 以下为您的代码 -->
  <select id="odsMonitorHomePage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_monitor_ods_cutoff
    <where>
      <if test="sourceType != null and sourceType != ''">
        and source_type = #{sourceType,jdbcType=VARCHAR}
      </if>
      and is_cutoff = 1
    </where>
    order by monitor_time desc
    limit 6
  </select>
  <select id="odsMonitorHomePageStatisticsCount" resultType="java.lang.Long">
    select
    count(*)
    from t_monitor_ods_cutoff
    <where>
      layer = #{layer}
      <if test="sourceType != null and sourceType != ''">
        and source_type = #{sourceType,jdbcType=VARCHAR}
      </if>
      <if test="dataModelIds != null">
        and data_model_id in
        <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
          #{dataModelId,jdbcType=INTEGER}
        </foreach>
      </if>
      and is_cutoff = 1
    </where>
  </select>

  <select id="odsMonitorStatisticsList" resultType="com.trs.ai.moye.monitor.response.OdsMonitorStatisticsResponse">
    select
    data_model_id as id,
    count(*) as amount,
    sumIf(is_cutoff = 1, 1) as `increment`,
    round(`increment` / amount * 100, 2) as proportion,
    min(monitor_time) as startTime,
    max(monitor_time) as endTime
    from t_monitor_ods_cutoff
    <where>
      layer = #{layer}
      <if test="request != null">
        <bind name="sourceType" value="request.sourceType"/>
        <bind name="isCutoff" value="request.isCutoff"/>
        <bind name="sourceSubType" value="request.sourceSubType"/>
        <bind name="dataModelName" value="request.dataModelName"/>
        <bind name="dataModelIds" value="request.dataModelIds"/>
        <if test="dataModelIds != null">
          and data_model_id in
          <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
            #{dataModelId,jdbcType=INTEGER}
          </foreach>
        </if>
        <if test="isCutoff != null">
          and is_cutoff = #{isCutoff,jdbcType=INTEGER}
        </if>
        <if test="sourceType != null and sourceType != ''">
          and source_type = #{sourceType,jdbcType=VARCHAR}
        </if>
        <if test="sourceSubType != null and sourceSubType != ''">
          and source_sub_type = #{sourceSubType,jdbcType=VARCHAR}
        </if>
        <if test="dataModelName != null and dataModelName != ''">
          and data_model_name like concat('%', #{dataModelName,jdbcType=VARCHAR}, '%')
        </if>
      </if>
    </where>
    group BY data_model_id
    <choose>
      <when test="sortable == null">
        ORDER BY endTime DESC
      </when>
      <otherwise>
        ORDER BY ${sortable.field} ${sortable.order}
      </otherwise>
    </choose>
  </select>

  <select id="odsMonitorDetailTable" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_monitor_ods_cutoff
    <where>
      <if test="dataModelId != null">
        and data_model_id = #{dataModelId,jdbcType=INTEGER}
      </if>
      <if test="request != null">
        <bind name="isCutoff" value="request.isCutoff"/>
        <bind name="configId" value="request.configId"/>
        <bind name="beginDateTime" value="timeParam.minTime"/>
        <bind name="endDateTime" value="timeParam.maxTime"/>
        <if test="isCutoff != null">
          and is_cutoff = #{isCutoff,jdbcType=INTEGER}
        </if>
        <if test="configId != null">
          and config_id = #{configId,jdbcType=INTEGER}
        </if>
        <if test="beginDateTime != null">
          and monitor_time &gt;= #{beginDateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endDateTime != null">
          and monitor_time &lt;= #{endDateTime,jdbcType=TIMESTAMP}
        </if>
      </if>
    </where>
    ORDER BY monitor_time DESC
  </select>

  <select id="odsMonitorDetailPeriods" resultType="com.trs.ai.moye.monitor.response.MonitorEventPeriodResponse">
    WITH periods AS (
    SELECT
    min(monitor_time) AS startTime,
    max(monitor_time) AS endTime,
    count() AS monitorCount,
    any(is_cutoff) AS isCutoff,
    group_id
    FROM (
    SELECT
    sum(group_mark) OVER w_sum AS group_id,
    monitor_time,
    is_cutoff
    FROM (
    SELECT
    monitor_time,
    is_cutoff,
    if(is_cutoff != lagInFrame(is_cutoff, 1) over w_lag, 1, 0) AS group_mark
    FROM t_monitor_ods_cutoff
    WHERE data_model_id = #{dataModelId,jdbcType=INTEGER}
    WINDOW w_lag AS (ORDER BY monitor_time ROWS BETWEEN 1 PRECEDING AND 1 PRECEDING)
    )
    WINDOW w_sum AS (ORDER BY monitor_time ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
    )
    GROUP BY group_id
    ),
    max_group AS (SELECT max(group_id) as maxGroupId FROM periods)
    SELECT startTime, monitorCount, CASE WHEN group_id = maxGroupId THEN NULL ELSE endTime END AS endTime
    FROM periods, max_group
    WHERE isCutoff = 1
    <if test="request != null and request.timeRangeParams != null">
      <bind name="beginDateTime" value="request.timeRangeParams.minTime"/>
      <bind name="endDateTime" value="request.timeRangeParams.maxTime"/>
      <if test="beginDateTime != null">
        and startTime &gt;= #{beginDateTime}
      </if>
      <if test="endDateTime != null">
        and startTime &lt;= #{endDateTime}
      </if>
    </if>
    ORDER BY startTime DESC
  </select>

  <select id="selectMonitorDetailList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_monitor_ods_cutoff
    <where>
      <if test="dataModelId != null">
        and data_model_id = #{dataModelId,jdbcType=INTEGER}
      </if>
      <if test="request != null">
        <bind name="isCutoff" value="request.isCutoff"/>
        <bind name="configId" value="request.configId"/>
        <bind name="beginDateTime" value="timeParam.minTime"/>
        <bind name="endDateTime" value="timeParam.maxTime"/>
        <if test="isCutoff != null">
          and is_cutoff = #{isCutoff,jdbcType=INTEGER}
        </if>
        <if test="configId != null">
          and config_id = #{configId,jdbcType=INTEGER}
        </if>
        <if test="beginDateTime != null">
          and monitor_time &gt;= #{beginDateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endDateTime != null">
          and monitor_time &lt;= #{endDateTime,jdbcType=TIMESTAMP}
        </if>
      </if>
    </where>
    ORDER BY monitor_time ASC
  </select>
</mapper>