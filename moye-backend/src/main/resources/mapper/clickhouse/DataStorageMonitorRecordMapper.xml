<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.monitor.dao.DataStorageMonitorRecordMapper">
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.trs.moye.base.monitor.entity.DataStorageMonitorRecord">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="data_model_id" jdbcType="INTEGER" property="dataModelId"/>
        <result column="data_model_name" jdbcType="VARCHAR" property="dataModelName"/>
        <result column="data_storage_connection_id" jdbcType="INTEGER" property="dataStorageConnectionId"/>
        <result column="topic" jdbcType="VARCHAR" property="topic"/>
        <result column="group" jdbcType="VARCHAR" property="group"/>
        <result column="schedule_time" jdbcType="TIMESTAMP" property="scheduleTime"/>
        <result column="monitor_time" jdbcType="TIMESTAMP" property="monitorTime"/>
        <result column="current_offset" jdbcType="BIGINT" property="currentOffset"/>
        <result column="total_offset" jdbcType="BIGINT" property="totalOffset"/>
        <result column="access_count" jdbcType="BIGINT" property="accessCount"/>
        <result column="process_count" jdbcType="BIGINT" property="processCount"/>
        <result column="lag_count" jdbcType="BIGINT" property="lagCount"/>
        <result column="lag_change_count" jdbcType="BIGINT" property="lagChangeCount"/>
        <result column="access_tps" jdbcType="DECIMAL" property="accessTps"/>
        <result column="process_tps" jdbcType="DECIMAL" property="processTps"/>
        <result column="storage_time" jdbcType="TIMESTAMP" property="storageTime"/>
        <result column="data_quality" jdbcType="VARCHAR" property="dataQuality"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>
    <select id="selectLatestByDataModelId" resultMap="BaseResultMap">
        select
        *
        from data_storage_monitor_record
        <where>
            and data_model_id = #{dataModelId,jdbcType=INTEGER}
            and data_storage_connection_id = #{dataStorageConnectionId,jdbcType=INTEGER}
        </where>
        ORDER BY monitor_time DESC
        LIMIT 1
    </select>
    <!-- 根据要素库ID查询监控信息 -->
    <select id="pageList" resultMap="BaseResultMap">
        SELECT
        *
        FROM data_storage_monitor_record
        <where>
            data_model_id = #{dataModelId,jdbcType=INTEGER}
            and data_storage_connection_id = #{dataStorageConnectionId,jdbcType=INTEGER}
            <if test="timeRangeParams.minTime != null">
                AND monitor_time &gt;= #{timeRangeParams.minTime, jdbcType=TIMESTAMP}
            </if>
            <if test="timeRangeParams.maxTime != null">
                AND monitor_time &lt;= #{timeRangeParams.maxTime, jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY monitor_time DESC
    </select>
    <select id="trendChart" resultMap="BaseResultMap">
        SELECT
        *
        FROM data_storage_monitor_record
        <where>
            data_model_id = #{dataModelId,jdbcType=INTEGER}
            and data_storage_connection_id = #{dataStorageConnectionId,jdbcType=INTEGER}
            <if test="timeRangeParams.minTime != null">
                AND monitor_time &gt;= #{timeRangeParams.minTime, jdbcType=TIMESTAMP}
            </if>
            <if test="timeRangeParams.maxTime != null">
                AND monitor_time &lt;= #{timeRangeParams.maxTime, jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY monitor_time DESC
    </select>
</mapper>