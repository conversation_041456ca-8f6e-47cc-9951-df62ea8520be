<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.monitor.dao.MonitorOdsLagMapper">
    <resultMap id="MonitorOdsLagResultMap" type="com.trs.moye.base.monitor.entity.MonitorOdsLag">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="data_model_id" jdbcType="INTEGER" property="dataModelId"/>
        <result column="data_model_name" jdbcType="VARCHAR" property="dataModelName"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
        <result column="source_sub_type" jdbcType="VARCHAR" property="sourceSubType"/>
        <result column="config_id" jdbcType="INTEGER" property="configId"/>
        <result column="monitor_time" jdbcType="TIMESTAMP" property="monitorTime"/>
        <result column="storage_time" jdbcType="TIMESTAMP" property="storageTime"/>
        <result column="offset" jdbcType="BIGINT" property="offset"/>
        <result column="offset" jdbcType="BIGINT" property="total"/>
        <result column="lag" jdbcType="BIGINT" property="lag"/>
        <result column="is_lag" jdbcType="TINYINT" property="isLag"/>
    </resultMap>

    <sql id="Base_Column_List">
        <trim suffixOverrides=",">
            id,
            data_model_id,
            data_model_name,
            config_id,
            monitor_time,
            offset,
            lag,
            is_lag,
            source_type,
            source_sub_type,
            storage_time,
        </trim>
    </sql>

    <select id="selectTopSix" resultType="com.trs.ai.moye.monitor.response.DataModelMonitorHomeResponse">
        SELECT data_model_id     as dataModelId,
               max(storage_time) as time,
               source_type       as sourceType,
               arrayElement(
                   arrayReverseSort(groupArray((storage_time, lag))), 1
               ).2               as count
        FROM t_monitor_ods_lag
        WHERE layer = #{layer}
          and is_lag = 1
        <if test="sourceType != null and sourceType != ''">
            and source_type = #{sourceType,jdbcType=VARCHAR}
        </if>
        <if test="dataModelIds != null">
            and data_model_id in
            <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
                #{dataModelId}
            </foreach>
        </if>
        GROUP BY data_model_id, source_type
        ORDER BY time DESC
        limit 6;
    </select>
    <select id="odsMonitorHomePageStatisticsCount" resultType="java.lang.Long">
        select count(*)
        from t_monitor_ods_lag
        <where>
            layer = #{layer}
            <if test="sourceType != null and sourceType != ''">
                and source_type = #{sourceType,jdbcType=VARCHAR}
            </if>
            <if test="dataModelIds != null">
                and data_model_id in
                <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
                    #{dataModelId}
                </foreach>
            </if>
            and is_lag = 1
        </where>
    </select>
    <select id="odsMonitorStatisticsList" resultType="com.trs.ai.moye.monitor.response.OdsMonitorStatisticsResponse">
      select data_model_id as id,
      count(*) as amount,
      sumIf(is_lag = 1, 1) as `increment`,
      round(`increment` / amount * 100, 2) as proportion,
      min(monitor_time) as startTime,
      max(monitor_time) as endTime,
      arrayElement(arraySort((x) -> -x.1, arrayZip(arrayMap(x -> toUnixTimestamp(x), groupArray(monitor_time)),
      groupArray(lag))), 1).2 as lastLag
      from t_monitor_ods_lag t

      <where>
        layer = #{layer}
        <if test="request != null">
          <bind name="isLag" value="request.isLag"/>
          <bind name="sourceType" value="request.sourceType"/>
          <bind name="sourceSubType" value="request.sourceSubType"/>
          <bind name="dataModelName" value="request.dataModelName"/>
          <if test="isLag != null">
            and is_lag = #{isLag,jdbcType=INTEGER}
          </if>
          <if test="sourceType != null and sourceType != ''">
            and source_type = #{sourceType,jdbcType=VARCHAR}
          </if>
          <if test="sourceSubType != null and sourceSubType != ''">
            and source_sub_type = #{sourceSubType,jdbcType=VARCHAR}
          </if>
          <if test="dataModelName != null and dataModelName != ''">
            and data_model_name like concat('%', #{dataModelName,jdbcType=VARCHAR}, '%')
          </if>
        </if>
      </where>
      group BY data_model_id
      <choose>
        <when test="sortable == null">
          ORDER BY lastLag DESC, endTime DESC
        </when>
        <otherwise>
          ORDER BY ${sortable.field} ${sortable.order}, endTime DESC
        </otherwise>
      </choose>
    </select>
    <select id="selectLatestByDataModelIdAndConfigVersion"
        resultMap="MonitorOdsLagResultMap">
        select *
        from t_monitor_ods_lag
        where data_model_id = #{dataModelId}
          and config_id = #{configId}
        order by monitor_time DESC
        limit ${count}
    </select>

    <select id="odsMonitorDetailTable" resultMap="MonitorOdsLagResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_monitor_ods_lag
        <where>
            <if test="dataModelId != null">
                and data_model_id = #{dataModelId,jdbcType=INTEGER}
            </if>
            <if test="request != null">
                <bind name="isLag" value="request.isLag"/>
                <bind name="configId" value="request.configId"/>
                <bind name="beginDateTime" value="timeParam.minTime"/>
                <bind name="endDateTime" value="timeParam.maxTime"/>
                <if test="isLag != null">
                    and is_lag = #{isLag,jdbcType=INTEGER}
                </if>
                <if test="configId != null">
                    and config_id = #{configId,jdbcType=INTEGER}
                </if>
                <if test="beginDateTime != null">
                    and monitor_time &gt;= #{beginDateTime}
                </if>
                <if test="endDateTime != null">
                    and monitor_time &lt;= #{endDateTime}
                </if>
            </if>
        </where>
        ORDER BY monitor_time DESC
    </select>

    <select id="odsMonitorDetailPeriods" resultType="com.trs.ai.moye.monitor.response.MonitorEventPeriodResponse">
        WITH periods AS (SELECT min(monitor_time)         AS startTime,
                                max(monitor_time)         AS endTime,
                                max(lag)                  AS peakValue,
                                argMax(monitor_time, lag) AS peakTime,
                                count()                   AS monitorCount,
                                group_id
                         FROM (
                                  SELECT sum(group_mark) OVER w_sum AS group_id,
                                         monitor_time,
                                         lag
                                  FROM (
                                           SELECT monitor_time,
                                                  lag,
                                                  if((lag > 0) != (lagInFrame(lag, 1) over w_lag > 0), 1, 0) AS group_mark
                                           FROM t_monitor_ods_lag
                                           WHERE data_model_id = #{dataModelId,jdbcType=INTEGER}
                                           WINDOW w_lag AS (ORDER BY monitor_time ROWS BETWEEN 1 PRECEDING AND 1 PRECEDING)
                                           )
                                  WINDOW w_sum AS (ORDER BY monitor_time ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
                                  )
                         GROUP BY group_id),
             max_group AS (SELECT max(group_id) AS maxGroupId
                           FROM periods)
        SELECT startTime,
               peakValue,
               peakTime,
               monitorCount,
               CASE WHEN group_id = maxGroupId THEN NULL ELSE endTime END AS endTime
        FROM periods,
             max_group
        WHERE peakValue > 0
        <if test="request != null and request.timeRangeParams != null">
            <bind name="beginDateTime" value="request.timeRangeParams.minTime"/>
            <bind name="endDateTime" value="request.timeRangeParams.maxTime"/>
            <if test="beginDateTime != null">
                and startTime &gt;= #{beginDateTime}
            </if>
            <if test="endDateTime != null">
                and startTime &lt;= #{endDateTime}
            </if>
        </if>
        ORDER BY startTime DESC
    </select>

    <select id="selectMonitorOdsLagRecords" resultMap="MonitorOdsLagResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_monitor_ods_lag
    <where>
        <if test="dataModelId != null">
            and data_model_id = #{dataModelId,jdbcType=INTEGER}
        </if>
        <if test="request != null">
            <bind name="isLag" value="request.isLag"/>
            <bind name="configId" value="request.configId"/>
            <if test="isLag != null">
                and is_lag = #{isLag,jdbcType=INTEGER}
            </if>
            <if test="configId != null">
                and config_id = #{configId,jdbcType=INTEGER}
            </if>
        </if>
    </where>
    ORDER BY monitor_time ASC
</select>
<select id="selectLatestByDataModelIdAndTimeRange"
resultMap="MonitorOdsLagResultMap">
select *
from t_monitor_ods_lag
where data_model_id = #{dataModelId}
  and monitor_time &gt;= #{startTime}
  and monitor_time &lt;= #{endTime}
order by monitor_time ASC
</select>

  <select id="selectByLayer" resultMap="MonitorOdsLagResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_monitor_ods_lag
    where `layer` = #{layer}
  </select>

</mapper>