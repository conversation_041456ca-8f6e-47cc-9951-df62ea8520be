<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.monitor.dao.StorageTaskViewMapper">


    <select id="selectStatistics" resultType="com.trs.ai.moye.monitor.response.statistics.StatisticalResult">
        select sum(total_count) as totalCount,
        sum(fail_count) as exceptionCount,
        sum(success_count) as normalCount,
        sumIf(total_count, toStartOfDay(time) =
        toStartOfDay(now())) as thisDodTotalAmount,
        sumIf(fail_count, toStartOfDay(time) =
        toStartOfDay(now())) as thisDodExceptionAmount,
        sumIf(success_count, toStartOfDay(time) =
        toStartOfDay(now())) as thisDodNormalAmount,
        sumIf(total_count, toStartOfDay(time) >= toStartOfDay(date_sub(now(), INTERVAL 7 DAY))
        AND toStartOfDay(time) &lt;
        toStartOfDay(now())) AS thisWowTotalAmount,
        sumIf(total_count, toStartOfDay(time) >= toStartOfDay(date_sub(now(), INTERVAL 14 DAY))
        AND toStartOfDay(time) &lt;
        toStartOfDay(date_sub(now(), INTERVAL 7 DAY))) AS lastWowTotalAmount,
        sumIf(fail_count, toStartOfDay(time) >= toStartOfDay(date_sub(now(), INTERVAL 7 DAY))
        AND toStartOfDay(time) &lt;
        toStartOfDay(now())) AS thisWowExceptionAmount,
        sumIf(fail_count, toStartOfDay(time) >= toStartOfDay(date_sub(now(), INTERVAL 14 DAY))
        AND toStartOfDay(time) &lt;
        toStartOfDay(date_sub(now(), INTERVAL 7 DAY))) AS lastWowExceptionAmount,
        sumIf(success_count, toStartOfDay(time) >= toStartOfDay(date_sub(now(), INTERVAL 7 DAY))
        AND toStartOfDay(time) &lt;
        toStartOfDay(now())) AS thisWowNormalAmount,
        sumIf(success_count, toStartOfDay(time) >= toStartOfDay(date_sub(now(), INTERVAL 14 DAY))
        AND toStartOfDay(time) &lt;
        toStartOfDay(date_sub(now(), INTERVAL 7 DAY))) AS lastWowNormalAmount
        from home_page_ods_schedule_statistics
    </select>


    <select id="taskList" resultType="com.trs.ai.moye.monitor.response.statistics.TaskStatisticsListResponse">
      select
      data_model_id ,
      mode,
      sum(dispatch_num) as accessCount,
      sum(normal_count) as successCount,
      sum(error_count) as errorCount
      from storage_task_view tsbv
      <where>
        <if test="dataModelIds != null">
          and data_model_id in
          <foreach collection="dataModelIds" item="dataModelId" open="(" close=")" separator=",">
            #{dataModelId}
          </foreach>
        </if>
        <if test="startTime != null  and endTime != null">
          and dispatch_time between toDateTime(#{startTime}) and toDateTime(#{endTime})
        </if>
        <if test="request.mode != null">
          and mode = #{request.mode}
        </if>
      </where>
      group by data_model_id ,mode,name
      <if test="errorFlag != null and errorFlag == true">
        having errorCount > 0
      </if>
      <if test="errorFlag != null and errorFlag == false">
        having errorCount = 0
      </if>
      <include refid="SortCond"/>
    </select>
    <select id="selectAmountByTimeParams" resultType="java.lang.Long">
        select sum(dispatch_num)
        from
        (
        select dispatch_num, error_count
        from storage_task_view
        <where>
            <if test="startTime != null and endTime != null">
                and dispatch_time between #{startTime} and #{endTime}
            </if>
            <if test="request.mode != null">
                and mode = #{request.mode}
            </if>
            <if test="request.keyword != null">
                and name like concat ('%' ,#{request.keyword},'%')
            </if>
            <if test="errorFlag != null and errorFlag == true">
                and error_count > 0
            </if>
            <if test="errorFlag != null and errorFlag == false">
                and error_count = 0
            </if>
        </where>
        )
    </select>

    <sql id="SortCond">
        <if test="sortRequest != null and sortRequest.field != null">
            order by
            <if test="sortRequest.field.toString.equals('accessCount')">
                `accessCount`
            </if>
            <if test="sortRequest.field.toString.equals('successCount')">
                `successCount`
            </if>
            <if test="sortRequest.field.toString.equals('errorCount')">
                `errorCount`
            </if>
            <if test="@java.util.Objects@nonNull(sortRequest.order) and sortRequest.order.toString.equals('ASC')">
                asc
            </if>
            <if test="@java.util.Objects@nonNull(sortRequest.order) and sortRequest.order.toString.equals('DESC')">
                desc
            </if>
        </if>
    </sql>

</mapper>