<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.common.dao.DictMapper">
    <select id="getKnowledgeBaseDictList" resultType="com.trs.ai.moye.common.response.DictResponse">
        select `type`  as `value`,
               zh_name as `label`
        from knowledge_base
        <where>
            <choose>
                <when test="baseTypes == null or baseTypes.size == 0">
                    and false
                </when>
                <otherwise>
                    and type in
                    <foreach close=")" collection="baseTypes" item="baseType" open="(" separator=",">
                        #{baseType,jdbcType=VARCHAR}
                    </foreach>
                </otherwise>
            </choose>
        </where>
        order by type, zh_name
    </select>
</mapper>