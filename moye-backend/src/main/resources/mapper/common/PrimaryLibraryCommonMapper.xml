<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.common.dao.PrimaryLibraryCommonMapper">
    <!-- 检查目标表是否存在 -->
    <select id="isTableExists" resultType="boolean">
        SELECT EXISTS(
        SELECT 1 FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = #{tableName}
        )
    </select>
    <!-- 高性能空表判断（LIMIT优化） -->
    <select id="isEmptyTable" resultType="boolean">
        SELECT NOT EXISTS(SELECT 1 FROM ${tableName} LIMIT 1)
    </select>
    <!-- 删除目标表（如果存在） -->
    <update id="dropTableIfExists">
        DROP TABLE IF EXISTS ${tableName}
    </update>
    <!-- 复制表结构（CREATE TABLE LIKE语法） -->
    <update id="copyTableStructure">
        CREATE TABLE IF NOT EXISTS ${targetTable} LIKE ${sourceTable}
    </update>
    <!-- 复制表数据（INSERT SELECT语法） -->
    <insert id="copyTableData">
        INSERT INTO ${targetTable} SELECT * FROM ${sourceTable}
    </insert>
</mapper>