<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.backstage.dao.MessagePushRecordMapper">


    <resultMap id="BaseResultMap" type="com.trs.ai.moye.backstage.entity.MessagePushRecord">
        <result property="messageType" column="message_type"/>
        <result property="messageSubtype" column="message_subtype"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="pushConfId" column="push_conf_id"/>
        <result property="pushType" column="push_type"/>
        <result property="pushTime" column="push_time"/>
        <result property="pushAddress" column="push_address"/>
        <result property="pushDepts" column="push_depts"
            typeHandler="com.trs.moye.base.common.typehandler.ClickHouseArrayHandler"/>
        <result property="pushRoles" column="push_roles"
            typeHandler="com.trs.moye.base.common.typehandler.ClickHouseArrayHandler"/>
        <result property="pushUsers" column="push_users"
            typeHandler="com.trs.moye.base.common.typehandler.ClickHouseArrayHandler"/>
        <result property="isSuccess" column="is_success"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="storageTime" column="storage_time"/>
    </resultMap>

    <select id="getMessagePushRecordsByPage" resultMap="BaseResultMap">
        SELECT *
        FROM t_message_push_records
        <where>
            <if test="request != null">
                <bind name="messageType" value="request.messageType"/>
                <bind name="messageSubtype" value="request.messageSubtype"/>
                <bind name="pushType" value="request.pushType"/>
                <bind name="pushDeptId" value="request.pushDeptId"/>
                <bind name="pushRoleId" value="request.pushRoleId"/>
                <bind name="pushUserId" value="request.pushUserId"/>
                <bind name="isSuccess" value="request.isSuccess"/>
                <if test="messageType != null">
                    and message_type = #{messageType}
                </if>
                <if test="messageSubtype != null">
                    and message_subtype = #{messageSubtype}
                </if>
                <if test="pushType != null">
                    and push_type = #{pushType}
                </if>
                <if test="pushDeptId != null">
                    and has(push_depts, #{pushDeptId})
                </if>
                <if test="pushRoleId != null">
                    and has(push_roles, #{pushRoleId})
                </if>
                <if test="pushUserId != null">
                    and has(push_users, #{pushUserId})
                </if>
                <if test="isSuccess != null">
                    and is_success = #{isSuccess}
                </if>
                <if test="request.beginDateTime != null">
                    <bind name="beginDateTime" value="@com.trs.moye.base.common.utils.DateTimeUtils@formatStr(request.beginDateTime)"/>
                    and push_time &gt;= #{beginDateTime,jdbcType=TIMESTAMP}
                </if>
                <if test="request.endDateTime != null">
                    <bind name="endDateTime" value="@com.trs.moye.base.common.utils.DateTimeUtils@formatStr(request.endDateTime)"/>
                    and push_time &lt;= #{endDateTime,jdbcType=TIMESTAMP}
                </if>
            </if>
        </where>
        ORDER BY storage_time DESC
    </select>
</mapper>