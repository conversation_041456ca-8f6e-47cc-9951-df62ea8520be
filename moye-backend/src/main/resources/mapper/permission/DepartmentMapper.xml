<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.backstage.dao.DepartmentMapper">


    <resultMap id="CategoryTreeMap" type="com.trs.ai.moye.backstage.entity.DepartmentTree">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="pid" jdbcType="BIGINT" property="pid"/>
        <collection property="children" column="{id=id}" select="findAllByParentId"
            ofType="com.trs.ai.moye.backstage.entity.DepartmentTree"/>
    </resultMap>

    <select id="existsByName" resultType="java.lang.Boolean">
        select count(id)
        from departments
        where name = #{name}
    </select>
    <select id="selectDepartmentTree" resultMap="CategoryTreeMap">
        select *
        from departments
        where pid = 0
    </select>

    <select id="findAllByParentId" resultMap="CategoryTreeMap">
        select t1.id as id ,t1.name as name ,t1.pid as pid,null as en_name
        from departments t1
        where t1.pid = #{id}
    </select>
    <select id="findByNameExceptId" resultType="com.trs.ai.moye.backstage.entity.Department">
        select *
        from departments
        where name = #{name}
          and id != #{id}
    </select>


</mapper>