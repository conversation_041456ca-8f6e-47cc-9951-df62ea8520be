<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.backstage.dao.UserMapper">
    <resultMap id="BaseResultMap" type="com.trs.ai.moye.backstage.entity.User">
        <!--@mbg.generated-->
        <!--@Table users-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="account" jdbcType="VARCHAR" property="account" />
        <result column="password" jdbcType="VARCHAR" property="password" />
        <result column="department_id" jdbcType="INTEGER" property="departmentId" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="is_enable" jdbcType="INTEGER" property="enable" />
        <result column="login_failed_num" jdbcType="INTEGER" property="loginFailedNum" />
        <result column="pwd_update_time" jdbcType="TIMESTAMP" property="pwdUpdateTime" />
        <result column="type" jdbcType="INTEGER" property="type" />
        <result column="role_ids" property="roleIds" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler" />
    </resultMap>

    <select id="findRolesByUserId" resultType="com.trs.ai.moye.backstage.response.UserRoleInfoResponse">
        select r.id,
               r.name
        from role r
                 left join user_role ru on r.id = ru.role_id
        where ru.user_id = #{userId};
    </select>

    <select id="findRoleModule" resultType="java.lang.String">
        select rm.module_id
        from role_module as rm
        where rm.role_id = #{roleId};
    </select>

    <select id="findRoleOperation" resultType="java.lang.String">
        select operation
        from role_operation
        where role_id = #{roleId}
        and module_id is null
        or module_id = ''
    </select>

    <update id="updateLoginFailedNum">
        update users
        set login_failed_num = #{nums}
        where id = #{id}
    </update>

    <update id="updateUserStatus">
        update users
        set `is_enable` = (case `is_enable` when 1 then 0 else 1 end)
        where id = #{userId}
    </update>

    <select id="selectIdByAccount" resultType="java.lang.Integer">
        select id
        from users
        where account = #{account}
    </select>

    <select id="findByAccount" resultMap="BaseResultMap">
        select * from users where account=#{account}
    </select>
    <select id="selectAll" resultType="com.trs.ai.moye.backstage.entity.User">
        select *
        from users
    </select>
    <select id="countDepartmentByDepartmentId" resultType="java.lang.Integer">
        select count(id) from users where department_id in
        <foreach collection="departmentId" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getRepeatUser" resultMap="BaseResultMap">
        select
        *
        from users
        <where>
            <if test="request.account != null and request.account != ''">
                or account = #{request.account,jdbcType=VARCHAR}
            </if>
            <if test="request.telephone != null and request.telephone != ''">
                or telephone = #{request.telephone,jdbcType=VARCHAR}
            </if>
            <if test="request.email != null and request.email != ''">
                or email = #{request.email,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="userPageList" resultMap="BaseResultMap">
        select
        *
        from users
        <where>
            <if test="departmentId != null">
                and department_id = #{departmentId,jdbcType=INTEGER}
            </if>
            <if test="searchParams != null and searchParams.fields != null and searchParams.keyword != null and searchParams.keyword != ''">
                <foreach collection="searchParams.fields" item="key" open="and (" separator=" or " close=")">
                    ${key} like CONCAT('%', #{searchParams.keyword}, '%')
                </foreach>
            </if>
        </where>
        <if test="sortParams != null and sortParams.field != null and sortParams.field != ''">
            ORDER BY ${sortParams.field} ${sortParams.order}
        </if>
    </select>
    <select id="selectUserNameById" resultType="java.lang.String">
        select name from users where id = #{userId}
    </select>
    <select id="selectAllAsMap" resultType="com.trs.ai.moye.backstage.entity.User">
        select *
        from users
    </select>

    <select id="getUsersByRoleId" resultMap="BaseResultMap">
        select *
        from users
        where
        json_contains(role_ids, JSON_ARRAY(#{roleId,jdbcType=INTEGER}));
    </select>
</mapper>