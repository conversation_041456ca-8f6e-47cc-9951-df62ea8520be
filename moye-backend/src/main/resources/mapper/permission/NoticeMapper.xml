<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.backstage.dao.NoticeMapper">

    <sql id="SortCond">
        <if test="sortParams != null and sortParams.field != null">
            order by
            <if test="sortParams.field.toString.equals('publishTime')">
                `publish_time`
            </if>
            <if test="@java.util.Objects@nonNull(sortParams.order) and sortParams.order.toString.equals('ASC')">
                asc
            </if>
            <if test="@java.util.Objects@nonNull(sortParams.order) and sortParams.order.toString.equals('DESC')">
                desc
            </if>
        </if>
    </sql>

    <select id="selectNoticeList" resultType="com.trs.ai.moye.backstage.entity.Notice">
        select *
        from notice
        <where>
            <if test="messageTypeId != null">
                and notice_type = #{messageTypeId}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.keyword)">
                and (title like concat ('%',#{searchParams.keyword}, '%')
                or content like concat('%',#{searchParams.keyword},'%'))
            </if>
        </where>
        <include refid="SortCond"/>
    </select>


    <select id="userList" resultType="com.trs.ai.moye.backstage.response.NoticeResponse" useCache="false">
        select n.id, n.title, n.content, n.notice_type as noticeTypeCode,
        DATE_FORMAT(n.publish_time, '%Y-%m-%d %H:%i:%s') AS publish_time,
        n.data_model_id as dataModelId,
        r.is_read as readFlag from notice_read_relation r
        left join notice n on (r.notice_id = n.id)
        where r.user_id = #{userId}
        <if test="request.messageTypeId != null">
            and n.notice_type = #{request.messageTypeId}
        </if>
        <if test="request.isRead != null">
            <bind name="isRead" value="request.isRead"/>
            <choose>
                <when test="isRead">
                    and r.is_read = 1
                </when>
                <otherwise>
                    and (r.is_read = 0 or r.is_read is null)
                </otherwise>
            </choose>
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(request.searchParams.keyword)">
            <bind name="searchKey" value="request.searchParams.keyword"/>
            and (n.title like concat ('%',#{searchKey}, '%') or n.content like concat('%',#{searchKey},'%'))
        </if>
        <bind name="sortParams" value="request.sortParams"/>
        <include refid="SortCond"/>
    </select>

    <select id="getUnreadAll" resultType="com.trs.ai.moye.backstage.response.NoticeResponse">
        select n.id, n.title, n.content, n.notice_type as noticeTypeCode, n.publish_time, r.is_read
        from notice n
                 left join notice_read_relation r on r.notice_id = n.id
        where user_id = #{userId}
          and r.is_read = 0
        order by publish_time desc
    </select>

</mapper>