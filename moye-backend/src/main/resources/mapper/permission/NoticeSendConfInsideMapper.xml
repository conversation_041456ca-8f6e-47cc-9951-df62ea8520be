<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.backstage.dao.NoticeSendConfInsideMapper">


    <resultMap id="resultMap" type="com.trs.ai.moye.backstage.entity.NoticeSendConfInside">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="send_user_ids" property="sendUserIds"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="send_role_ids" property="sendRoleIds"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="message_type_ids" property="messageTypeIds"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="business_ids" property="businessIds"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="state" property="state"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="data_model_id" property="dataModelId"/>
        <result column="create_model" property="createModel"/>
    </resultMap>
    <update id="updateStateById">
        update notice_send_conf_inside
        set state = #{state}
        where id = #{id}
    </update>
    <delete id="deleteByDataModelId">
        delete
        from notice_send_conf_inside
        where data_model_id = #{id}
    </delete>

    <select id="getNoticePageList" resultMap="resultMap">
        select *
        from notice_send_conf_inside
        <where>
            <!-- 根据 消息类型 进行过滤 -->
            <if test="messageTypeId != null">
                AND JSON_CONTAINS(message_type_ids, JSON_ARRAY(#{messageTypeId}))
            </if>
            <if test="createType != null and createType != ''">
                AND create_model = #{createType}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.keyword)">
                <bind name="value" value="'%' + searchParams.keyword + '%'"/>
                and
                <foreach collection="searchParams.fields" item="field" open="(" separator="or" close=")">
                    <choose>
                        <when test="field == 'name'">
                            `name` like #{value}
                        </when>
                        <when test="field == 'id'">
                            id like #{value}
                        </when>
                    </choose>
                </foreach>
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    <select id="selectByName" resultType="java.lang.Integer">
        select count(id)
        from notice_send_conf_inside
        where name = #{name}
    </select>
</mapper>