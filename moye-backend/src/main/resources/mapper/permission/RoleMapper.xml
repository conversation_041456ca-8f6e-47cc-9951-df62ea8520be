<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.backstage.dao.RoleMapper">
    <!-- 结果集映射 -->
    <resultMap id="RoleMap" type="com.trs.ai.moye.backstage.entity.Role">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="operations" typeHandler="com.trs.ai.moye.common.typehandler.StringListTypeHandler"
            property="operations"/>
        <result column="credentials" typeHandler="com.trs.ai.moye.common.typehandler.IntegerListTypeHandler"
            property="credentials"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_by" jdbcType="INTEGER" property="createBy"/>
        <result column="update_by" jdbcType="INTEGER" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>

    </resultMap>
    <delete id="isAdmin">
        select type
        from role
        where id in (SELECT role_id FROM user_role WHERE user_id = #{userId})
    </delete>

    <select id="selectRoleByUserId" resultMap="RoleMap">
        select r.*
        from role r,
        user_role ur
        where r.id = ur.role_id
        and ur.user_id = #{userId}
    </select>

    <select id="getUserModule" resultType="java.lang.String">
        select DISTINCT rm.module_id
        from role_module as rm
        where rm.role_id in
        <foreach collection="roleIds" item="roleId" index="index" open="(" close=")" separator=",">
            #{roleId}
        </foreach>
    </select>

    <select id="getUserOperation" resultType="com.trs.ai.moye.backstage.entity.RoleOperation">
        select operation as id, parent_operation as pid ,module_id as moduleId
        from role_operation
        where role_id in
        <foreach collection="roleIds" item="roleId" index="index" open="(" close=")" separator=",">
            #{roleId}
        </foreach>
    </select>

    <select id="selectByIdCollection" resultMap="RoleMap">
        select
        *
        from role
        <where>
            <choose>
                <when test="idCollection == null or idCollection.size == 0">
                    and false
                </when>
                <otherwise>
                    and id in
                    <foreach close=")" collection="idCollection" item="id" open="(" separator=",">
                        #{id,jdbcType=INTEGER}
                    </foreach>
                </otherwise>
            </choose>
        </where>
    </select>
    <select id="countByNameAndExcludeId" resultType="java.lang.Integer">
        select count(1) from role
        where name = #{name}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <select id="selectPageByRoleName" resultType="com.trs.ai.moye.backstage.entity.Role">
        select *
        from role
        <where>
            <if test="roleName != null and roleName != ''">
                and name like concat('%',#{roleName},'%')
            </if>
        </where>
        <if test="sortParam.order != null">
            order by
            update_time ${sortParam.order}
        </if>
    </select>
    <select id="selectSimpleInfo" resultType="com.trs.ai.moye.backstage.response.RoleSimpleResponse">
        select id, name, type
        from role
    </select>
    <select id="selectByIdRole" resultMap="RoleMap">
        select *
        from role
        where id = #{id}
    </select>
    <select id="selectByIds" resultMap="RoleMap">
        select *
        from role
        where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="selectAll" resultMap="RoleMap">
        select *
        from role
    </select>
    <select id="selectAllAsMap" resultType="com.trs.ai.moye.backstage.entity.Role">
        select *
        from `role`
    </select>
</mapper>