<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.monitor.dao.BasicComponentDetectionStatisticsMapper">

    <select id="selectAsMap" resultType="com.trs.ai.moye.monitor.entity.BasicComponentDetectionStatistics">
        select * from basic_component_detection_statistics
    </select>

    <select id="detailList" resultType="com.trs.ai.moye.monitor.entity.BasicComponentDetection">
        select * from basic_component_detection
        where component_name = #{type}
        <if test="beginTime != null">
            and detection_time >= toDateTime(#{beginTime})
        </if>
        <if test="endTime != null">
            and detection_time &lt;= toDateTime(#{endTime})
        </if>
        <if test="status!=null">
            and status = #{status}
        </if>
        order by detection_time desc
    </select>

    <select id="selectAllOrigin" resultType="com.trs.ai.moye.monitor.entity.BasicComponentDetectionStatistics">
        select * from basic_component_detection_statistics
    </select>
</mapper>