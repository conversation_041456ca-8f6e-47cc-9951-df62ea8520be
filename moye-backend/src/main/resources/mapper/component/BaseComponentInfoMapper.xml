<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.monitor.dao.BaseComponentInfoMapper">

    <select id="getAllComponents" resultType="com.trs.ai.moye.monitor.entity.BaseComponentInfo">
        select * from base_component_info
    </select>

    <select id="selectByEnabled" resultType="com.trs.ai.moye.monitor.entity.BaseComponentInfo">
        select * from base_component_info where enabled = #{enabled}
    </select>

    <select id="selectByComponentName" resultType="com.trs.ai.moye.monitor.entity.BaseComponentInfo">
        select * from base_component_info where component_name = #{componentName}
    </select>

    <update id="updateEnabledAndXxlJobIdByComponentName">
        update base_component_info
        set enabled = #{enabled},
            xxl_job_id = #{xxlJobId}
        where component_name = #{componentName}
    </update>
</mapper>