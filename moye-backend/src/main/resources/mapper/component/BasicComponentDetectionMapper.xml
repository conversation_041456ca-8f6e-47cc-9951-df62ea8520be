<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.monitor.dao.BasicComponentDetectionMapper">

    <select id="detailList" resultType="com.trs.ai.moye.monitor.entity.BasicComponentDetection">
        select * from basic_component_detection
        where component_name = #{type}
        <if test="beginTime != null">
            and detection_time >= toDateTime(#{beginTime})
        </if>
        <if test="endTime != null">
            and detection_time &lt;= toDateTime(#{endTime})
        </if>
        <if test="status!=null">
            and status = #{status}
        </if>
        order by detection_time desc
    </select>

    <select id="selectStatistics" resultType="com.trs.ai.moye.monitor.dto.BasicComponentDetectionStatisticsDTO">
        select
            component_name as componentName,
            sum(case when status = 0 then 1 else 0 end) as errorCount,
            count(*) as detectionCount,
            MAX(detection_time) as lastDetectionTime,
            argMax(status, detection_time) as lastStatus,
            MAX(CASE WHEN status = 0 THEN detection_time END) as lastErrorTime,
            argMax(error_message, CASE WHEN status = 0 THEN detection_time ELSE toDateTime(0) END) as lastErrorMessage
        from basic_component_detection
        GROUP BY component_name
    </select>
</mapper>