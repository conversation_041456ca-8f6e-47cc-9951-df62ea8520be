<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.ability.dao.AbilityMapper">

    <resultMap id="BaseResultMap" type="com.trs.moye.ability.entity.Ability">
        <id column="id" property="id"/>
        <result column="en_name" property="enName"/>
        <result column="zh_name" property="zhName"/>
        <result column="description" property="description"/>
        <result column="type" property="type"/>
        <result column="path" property="path"/>
        <result column="operator_category_id" property="operatorCategoryId"/>
        <result column="icon_name" property="iconName"/>
        <result column="update_status" property="updateStatus"/>
        <result column="test_status" property="testStatus"/>
        <result column="http_request_config" property="httpRequestConfig"
            typeHandler="com.trs.moye.ability.typehandler.HttpRequestConfigTypeHandler"/>
        <result column="input_schema" property="inputSchema"
            typeHandler="com.trs.moye.ability.typehandler.SchemaTypeHandler"/>
        <result column="output_schema" property="outputSchema"
            typeHandler="com.trs.moye.ability.typehandler.SchemaTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="input_size" property="inputSize"/>
        <result column="output_size" property="outputSize"/>
        <result column="field_retention_mode" property="fieldRetentionMode"/>
        <result column="is_batch_supported" property="isBatchSupported"/>
        <result column="is_batch_condition_supported" property="isBatchConditionSupported"/>
        <result column="ability_center_params" property="abilityCenterParams"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="details" property="details"/>
    </resultMap>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM ability WHERE id = #{id}
    </select>

    <select id="selectBatchIds" resultMap="BaseResultMap">
        SELECT *
        FROM ability
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectAllByRequest" resultMap="BaseResultMap">
        select * from ability
        <where>
            <if test="request.testStatus != null">
                and test_status = #{request.testStatus}
            </if>
            <if test="request.updateStatus != null">
                and update_status = #{request.updateStatus}
            </if>
            <!-- 检查 request.extendedAbility 数组是否不为空 -->
            <if test="request.extendedAbility != null and request.extendedAbility.length > 0">
                and type IN
                <foreach item="typeItem" index="index" collection="request.extendedAbility" open="(" separator="," close=")">
                    #{typeItem}
                </foreach>
            </if>
            <if test="request.isBatchSupported != null">
                and is_batch_supported =
                <choose>
                    <when test="request.isBatchSupported">
                        1
                    </when>
                    <otherwise>
                        0
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

    <select id="existByEnName" resultType="java.lang.Boolean">
        select count(id) from ability where en_name = #{enName}
    </select>

    <select id="existByZhName" resultType="java.lang.Boolean">
        select count(id) from ability where zh_name = #{zhName}
    </select>

    <update id="updateCategoryByOperatorIds">
        update ability set operator_category_id = #{categoryId} where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="updateTestStatus">
        update ability set test_status = #{testStatus} where id = #{id}
    </update>

    <update id="updateUpdateStatus">
        update ability set update_status = #{status} where id = #{id}
    </update>

    <select id="existById" resultType="java.lang.Boolean">
        select count(id) from ability where id = #{id}
    </select>
    <select id="selectByEnName" resultType="com.trs.moye.ability.entity.Ability">
        SELECT * FROM ability WHERE en_name = #{enName}
    </select>

    <select id="selectByOperatorCategoryId" resultMap="BaseResultMap">
        select * from ability where operator_category_id = #{categoryId}
    </select>

    <select id="getAccessedAbilityIds" resultType="java.lang.Integer">
        select ability_center_params -> '$.abilityId' from ability o where o.type = 'ABILITY_CENTER'
    </select>
    <select id="selectByTypeEnName" resultType="com.trs.moye.ability.entity.Ability">
        SELECT * FROM ability WHERE type = #{type} and en_name = #{enName} limit 1
    </select>
</mapper>

