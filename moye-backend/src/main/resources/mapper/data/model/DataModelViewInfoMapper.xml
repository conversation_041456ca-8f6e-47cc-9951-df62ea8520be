<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.trs.ai.moye.data.model.dao.DataModelViewInfoMapper">

    <resultMap id="BaseResultMap" type="com.trs.ai.moye.data.model.entity.view.DataModelViewInfo">
        <id column="id" property="id" />
        <result column="data_model_id" property="dataModelId" />
        <result column="connection_id" property="connectionId" />
        <result column="sql" property="sql" />
        <result column="tables_fields" property="tablesFields" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler" />
        <result column="join_relations" property="joinRelations" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler" />
    </resultMap>

    <select id="selectByDataModelId" parameterType="int" resultMap="BaseResultMap">
        SELECT * FROM data_model_view_info WHERE data_model_id = #{dataModelId}
    </select>

    <delete id="deleteByDataModelId" parameterType="integer">
        DELETE FROM data_model_view_info
        WHERE data_model_id = #{dataModelId}
    </delete>

    <select id="selectDataModelIdsByConnectionId" resultType="java.lang.Integer">
        SELECT data_model_id FROM data_model_view_info
        WHERE connection_id = #{connectionId}
    </select>

</mapper>