<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.data.model.dao.DataSourceDisplayMapper">
    <resultMap id="DataSourceTypeGroupCountMap" type="com.trs.ai.moye.common.dao.GroupCount">
        <result column="value" property="value" javaType="com.trs.moye.base.data.connection.enums.ConnectionType"/>
        <result column="count" property="count"/>
    </resultMap>
    <select id="selectDataSourceTypeCounts" resultMap="DataSourceTypeGroupCountMap">
        SELECT COUNT(1)           AS count,
               dc.connection_type AS value
        FROM data_source_config dsc
                 JOIN data_model dm ON dm.id = dsc.data_model_id
                 JOIN data_connection dc ON dsc.connection_id = dc.id
        WHERE dm.layer = #{layer}
        GROUP BY dc.connection_type;
    </select>
</mapper>