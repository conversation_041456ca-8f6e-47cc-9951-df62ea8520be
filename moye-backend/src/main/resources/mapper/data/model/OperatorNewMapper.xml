<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.data.model.dao.operator.OperatorNewMapper">
    <resultMap id="OperatorMap" type="com.trs.moye.ability.entity.operator.Operator">
        <id column="id" property="id"/>
        <result column="pipeline_id" property="pipelineId"/>
        <result column="name" property="name"/>
        <result column="conditions" property="conditions"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="input_fields" property="inputFields"
            typeHandler="com.trs.moye.ability.typehandler.OperatorRowTypeHandler"/>
        <result column="output_fields" property="outputFields"
            typeHandler="com.trs.moye.ability.typehandler.OperatorRowTypeHandler"/>
        <result column="ability_id" property="abilityId"/>
        <result column="input_bind" property="inputBind"
            typeHandler="com.trs.moye.ability.typehandler.InputBindTypeHandler"/>
        <result column="output_bind" property="outputBind"
            typeHandler="com.trs.moye.ability.typehandler.OutputBindTypeHandler"/>
        <result column="enabled" property="enabled"/>
        <association column="ability_id" property="ability"
            javaType="com.trs.moye.ability.entity.Ability"
            select="com.trs.ai.moye.data.ability.dao.AbilityMapper.selectById"/>
    </resultMap>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        <choose>
            <when test="operators.size() > 0">
                INSERT INTO operator (pipeline_id, name, conditions,input_fields, output_fields, ability_id,
                input_bind,
                output_bind, enabled, storage_id, data_model_id)
                VALUES
                <foreach collection="operators" item="item" separator=",">
                    (#{item.pipelineId}, #{item.name},
                    #{item.conditions,typeHandler=com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler},
                    #{item.inputFields, typeHandler=com.trs.moye.ability.typehandler.OperatorRowTypeHandler},
                    #{item.outputFields, typeHandler=com.trs.moye.ability.typehandler.OperatorRowTypeHandler},
                    #{item.abilityId},
                    #{item.inputBind, typeHandler=com.trs.moye.ability.typehandler.InputBindTypeHandler},
                    #{item.outputBind, typeHandler=com.trs.moye.ability.typehandler.OutputBindTypeHandler},
                    #{item.enabled}, #{item.storageId}, #{item.dataModelId})
                </foreach>
            </when>
            <otherwise>select 1</otherwise>
        </choose>

    </insert>

    <delete id="deleteByPipelineId">
        DELETE
        FROM operator
        WHERE pipeline_id = #{pipelineId}
    </delete>

    <select id="selectByPipelineId" resultMap="OperatorMap">
        SELECT *
        FROM operator
        WHERE pipeline_id = #{id}
    </select>
    <select id="selectByEnName" resultType="com.trs.moye.ability.entity.operator.Operator">
        SELECT *
        FROM operator
        WHERE pipeline_id = #{pipelineId}
          AND name = #{name}
    </select>
    <select id="selectUsingDataModel" resultType="com.trs.ai.moye.data.model.response.KeyValueResponse">
        SELECT data_model_id                                                 AS `key`,
               (SELECT zh_name FROM data_model d WHERE d.id = data_model_id) AS `value`
        FROM (SELECT DISTINCT data_model_id
              FROM operator
              WHERE ability_id = #{abilityId}) a
    </select>
</mapper>