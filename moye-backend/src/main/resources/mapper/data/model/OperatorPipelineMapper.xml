<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.data.model.dao.operator.OperatorPipelineMapper">
    <resultMap id="OperatorPipelineMap" type="com.trs.moye.ability.entity.operator.OperatorPipeline">
        <id column="id" property="id"/>
        <result column="data_model_id" property="dataModelId"/>
        <result column="data_source_connection_id" property="dataSourceConnectionId"/>
        <result column="canvas" property="canvas" javaType="com.trs.moye.ability.entity.operator.OperatorCanvas"
            typeHandler="com.trs.moye.ability.typehandler.OperatorCanvasTypeHandler"/>
        <result column="field_mapping" property="fieldMapping"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="input_fields" property="inputFields"
            javaType="com.trs.moye.ability.entity.operator.OperatorRowType"
            typeHandler="com.trs.moye.ability.typehandler.OperatorRowTypeHandler"/>
        <collection property="operators" ofType="com.trs.moye.ability.entity.operator.Operator" column="id"
            select="com.trs.ai.moye.data.model.dao.operator.OperatorNewMapper.selectByPipelineId"/>
    </resultMap>

    <select id="selectByDataModelId" resultMap="OperatorPipelineMap">
        SELECT *
        FROM operator_pipeline
        WHERE data_model_id = #{dataModelId}
    </select>

    <select id="selectById" resultMap="OperatorPipelineMap">
        SELECT *
        FROM operator_pipeline
        WHERE id = #{id}
    </select>
</mapper>