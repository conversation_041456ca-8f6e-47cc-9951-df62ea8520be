<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.data.model.dao.DataStorageDisplayMapper">
    <resultMap id="DataModelStorageDetailMap" type="com.trs.moye.base.data.model.entity.DataModelConnDetail">
        <result column="id" property="id"/>
        <result column="zh_name" property="zhName"/>
        <result column="en_name" property="enName"/>
        <result column="connection_params" property="storageConnectionParams"
            typeHandler="com.trs.moye.base.common.typehandler.PolymorphismTypeHandler"/>
    </resultMap>
    <select id="selectByDataModelIds" resultMap="DataModelStorageDetailMap">
        SELECT dc.connection_params,
               dm.id,
               dm.zh_name,
               dm.en_name
        FROM data_storage ds
                 JOIN
             data_connection dc ON ds.connection_id = dc.id
                 JOIN
             data_model dm ON ds.data_model_id = dm.id
        WHERE
        ds.data_model_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectPagedByConnectionId"
        resultType="com.trs.ai.moye.data.model.dto.DataStorageWithModelAndCategoryDTO">
        SELECT ds.id          as dataStorageId,
               ds.en_name     as dataStorageName,
               ds.zh_name     as dataStorageComment,
               ds.create_time as dataStorageCreateTime,
               bc.id          as businessCategoryId,
               bc.en_name     as businessCategoryEnName,
               bc.zh_name     as businessCategoryZhName,
               dm.layer       as layer,
               dm.id          as dataModelId,
               dm.en_name     as dataModelEnName,
               dm.zh_name     as dataModelZhName
        FROM data_storage ds
                 LEFT JOIN data_model dm ON ds.data_model_id = dm.id
                 LEFT JOIN business_category bc on bc.id = dm.business_category_id
        <where>
            ds.connection_id = #{connectionId}
              AND ds.create_table_status = 'SUCCESS'
            <if test="searchParams != null and searchParams.fields != null and searchParams.keyword != null and searchParams.keyword != ''">
                <foreach collection="searchParams.fields" item="key" separator=" AND ">
                    <if test="key == 'name'">
                        AND (ds.en_name like concat('%', #{searchParams.keyword}, '%')
                            OR ds.zh_name like concat('%', #{searchParams.keyword}, '%'))
                    </if>
                </foreach>
            </if>
        </where>
    </select>

    <resultMap id="CreateTableStatusGroupCountMap" type="com.trs.ai.moye.common.dao.GroupCount">
        <result column="value" property="value" javaType="com.trs.moye.base.data.model.enums.CreateTableStatus"/>
        <result column="count" property="count"/>
    </resultMap>
    <select id="selectCreateStatusCounts" resultMap="CreateTableStatusGroupCountMap">
        select count(1)            as count,
               create_table_status as value
        from data_storage
        where data_model_id in (select id from data_model where layer = #{layer})
        group by create_table_status
    </select>
</mapper>