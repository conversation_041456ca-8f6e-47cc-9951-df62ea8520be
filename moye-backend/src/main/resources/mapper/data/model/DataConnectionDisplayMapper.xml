<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.data.model.dao.DataConnectionDisplayMapper">
    <resultMap id="DataConnectionWithMonitorConfig" type="com.trs.moye.base.data.connection.entity.DataConnection">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="connection_type" property="connectionType"/>
        <result column="connection_params" property="connectionParams"
            typeHandler="com.trs.moye.base.common.typehandler.PolymorphismTypeHandler"/>
        <result column="is_source" property="source"/>
        <result column="is_test_success" property="testSuccess"/>
        <result column="certificate_id" property="certificateId"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <association column="certificate_id" property="kerberosCertificate"
            javaType="com.trs.moye.base.data.connection.entity.KerberosCertificate"
            select="com.trs.moye.base.data.connection.dao.AuthCertificateMapper.selectById"/>
        <association property="monitorConfig" javaType="com.trs.moye.base.data.connection.entity.DataConnectionMonitorConfig">
            <id property="id" column="dcmc_id"/>
            <result property="connectionId" column="dcmc_connection_id"/>
            <result property="enabled" column="dcmc_enabled"/>
            <result property="xxlJobId" column="dcmc_xxl_job_id"/>
        </association>
    </resultMap>

    <select id="selectConnections" resultMap="DataConnectionWithMonitorConfig">
        select dc.*,
               dcmc.id as dcmc_id,
               dcmc.connection_id as dcmc_connection_id,
               dcmc.enabled as dcmc_enabled,
               dcmc.xxl_job_id as dcmc_xxl_job_id
        from data_connection dc
        left join data_connection_monitor_config dcmc on dc.id = dcmc.connection_id
        <where>
            dc.is_source = #{isSource}
            <choose>
                <when test="request.testSuccess != null">
                    <if test="request.testSuccess == true">
                        and dc.is_test_success = 1
                    </if>
                    <if test="request.testSuccess == false">
                        and dc.is_test_success = 0
                    </if>
                </when>
            </choose>
            <if test="request.autoTestEnabled != null">
                <choose>
                    <when test="request.autoTestEnabled == true">
                        and dcmc.enabled = 1
                    </when>
                    <otherwise>
                        and (dcmc.enabled is null or dcmc.enabled = 0)
                    </otherwise>
                </choose>
            </if>
            <if test="request.connectionType != null">
                and dc.connection_type = #{request.connectionType}
            </if>
            <if test="request.searchParamsValid()">
                and (dc.`name` like concat('%', #{request.searchParams.keyword}, '%') or      (
                JSON_EXTRACT(dc.connection_params, '$.host') LIKE CONCAT('%', #{request.searchParams.keyword}, '%') OR
                JSON_EXTRACT(dc.connection_params, '$.port') LIKE CONCAT('%', #{request.searchParams.keyword}, '%')
                ))
            </if>
        </where>
        order by dc.update_time desc
    </select>

    <select id="selectAllConnections" resultMap="com.trs.moye.base.data.connection.dao.DataConnectionMapper.DataConnectionMap">
        select *
        from data_connection
        <where>
            <choose>
                <when test="request.testSuccess != null">
                    <if test="request.testSuccess == true">
                        and is_test_success = 1
                    </if>
                    <if test="request.testSuccess == false">
                        and is_test_success = 0
                    </if>
                </when>
            </choose>
            <if test="request.connectionType != null">
                and connection_type = #{request.connectionType}
            </if>
            <if test="request.searchParamsValid()">
                and (`name` like concat('%', #{request.searchParams.keyword}, '%') or (
                    JSON_EXTRACT(connection_params, '$.host') LIKE CONCAT('%', #{request.searchParams.keyword}, '%') OR
                    JSON_EXTRACT(connection_params, '$.port') LIKE CONCAT('%', #{request.searchParams.keyword}, '%')
                    ))
            </if>
        </where>
        order by update_time desc
    </select>

    <select id="countStatistics" resultType="com.trs.ai.moye.data.connection.response.DataConnectionStatisticsResponse">
        select
            COUNT(dc.id) AS totalCount,
            SUM(CASE WHEN dc.is_test_success = 1 THEN 1 ELSE 0 END) AS successCount,
            SUM(CASE WHEN dc.is_test_success = 0 THEN 1 ELSE 0 END) AS failureCount,
            SUM(CASE WHEN dcmc.enabled = 1 THEN 1 ELSE 0 END) AS autoTestEnabledCount
        from data_connection dc
            left join data_connection_monitor_config dcmc on dc.id = dcmc.connection_id
        <where>
            <if test="isSource != null">
                and dc.is_source = #{isSource}
            </if>
        </where>
    </select>
</mapper>