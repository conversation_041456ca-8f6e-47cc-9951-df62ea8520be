<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.model.dao.DataModelFieldWithKnowledgeFieldsMapper">
    <!-- 结果集映射 -->
    <resultMap id="FieldWithKnowledgeBaseFieldsMap" type="com.trs.moye.base.data.model.entity.DataModelField"
        extends="com.trs.moye.base.data.model.dao.DataModelFieldMapper.BaseResultMap">
        <collection property="fields.fields" ofType="com.trs.moye.base.knowledgebase.entity.KnowledgeBaseField"
            columnPrefix="kbf_">
            <id column="id" property="id"/>
            <result column="zh_name" property="zhName"/>
            <result column="en_name" property="enName"/>
            <result column="type" property="type"/>
            <result column="type_name" property="typeName"/>
            <result column="is_multi_value" property="multiValue"/>
            <result column="is_unique" property="unique"/>
            <result column="description" property="description"/>
            <result column="advance_config" property="advanceConfig"
                typeHandler="com.trs.moye.base.common.typehandler.PolymorphismTypeHandler"/>
        </collection>
    </resultMap>
    <select id="listFieldsWithKnowledgeBaseFields" resultMap="FieldWithKnowledgeBaseFieldsMap">
        SELECT dmf.*,
               kb.id              as kb_id,
               kb.zh_name         as kb_zh_name,
               kb.en_name         as kb_en_name,
               kb.type            as kb_type,
               kbf.id             as kbf_id,
               kbf.zh_name        as kbf_zh_name,
               kbf.en_name        as kbf_en_name,
               kbf.type           as kbf_type,
               kbf.type_name      as kbf_type_name,
               kbf.is_multi_value as kbf_is_multi_value,
               kbf.is_unique      as kbf_is_unique,
               kbf.description    as kbf_description,
               kbf.advance_config as kbf_advance_config
        FROM data_model_field dmf
                 LEFT JOIN knowledge_base kb ON (dmf.type = 'COMPOUND' OR dmf.type = 'ENTITY' OR dmf.type = 'TAG')
            AND kb.type = dmf.type
            AND kb.zh_name = dmf.type_name
                 LEFT JOIN knowledge_base_field kbf ON kb.id = kbf.base_id
        WHERE dmf.data_model_id = #{dataModelId}
          AND (dmf.type = 'COMPOUND' OR dmf.type = 'ENTITY' OR dmf.type = 'TAG')
        ORDER BY dmf.id, kbf.id
    </select>

    <select id="listFieldsWithKnowledgeBaseFieldsByIds" resultMap="FieldWithKnowledgeBaseFieldsMap">
        SELECT dmf.*,
        kb.id as kb_id,
        kb.zh_name as kb_zh_name,
        kb.en_name as kb_en_name,
        kb.type as kb_type,
        kbf.id as kbf_id,
        kbf.zh_name as kbf_zh_name,
        kbf.en_name as kbf_en_name,
        kbf.type as kbf_type,
        kbf.type_name as kbf_type_name,
        kbf.is_multi_value as kbf_is_multi_value,
        kbf.is_unique as kbf_is_unique,
        kbf.description as kbf_description,
        kbf.advance_config as kbf_advance_config
        FROM data_model_field dmf
        LEFT JOIN knowledge_base kb ON (dmf.type = 'COMPOUND' OR dmf.type = 'ENTITY' OR dmf.type = 'TAG')
        AND kb.type = dmf.type
        AND kb.zh_name = dmf.type_name
        LEFT JOIN knowledge_base_field kbf ON kb.id = kbf.base_id
        WHERE dmf.id IN
        <foreach collection="fieldIds" item="fieldId" open="(" separator="," close=")">
            #{fieldId}
        </foreach>
        ORDER BY dmf.id, kbf.id
    </select>
</mapper>
