<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.data.model.dao.ProcessMapper">

    <resultMap id="TracerMap" type="com.trs.ai.moye.data.model.entity.TracerData">
        <!--@mbg.generated-->
        <!--@Table data_process_trace-->
        <result column="record_id" jdbcType="INTEGER" property="recordId"/>
        <result column="process_id" jdbcType="INTEGER" property="processId"/>
        <result column="receive_mq_type" jdbcType="VARCHAR" property="receiveMqType"/>
        <result column="receive_mq_path" jdbcType="VARCHAR" property="receiveMqPath"/>
        <result column="receive_group" jdbcType="VARCHAR" property="receiveGroup"/>
        <result column="receive_topic" jdbcType="VARCHAR" property="receiveTopic"/>
        <result column="msg_title" jdbcType="VARCHAR" property="msgTitle"/>
        <result column="data_source_id" jdbcType="INTEGER" property="dataSourceId"/>
        <result column="table_id" jdbcType="INTEGER" property="tableId"/>
        <result column="data_source_name" jdbcType="VARCHAR" property="dataSourceName"/>
        <result column="data_model_id" jdbcType="INTEGER" property="dataModelId"/>
        <result column="msg_content" jdbcType="VARCHAR" property="msgContent"/>
        <result column="results" jdbcType="VARCHAR" property="results"/>
        <result column="storage_time" jdbcType="TIMESTAMP" property="storageTime"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="processing_time" jdbcType="INTEGER" property="processingTime"/>
        <result column="node_name" jdbcType="VARCHAR" property="nodeName"/>
        <result column="node_order" jdbcType="VARCHAR" property="processingOrder"/>
        <result column="service_name" jdbcType="VARCHAR" property="serviceName"/>
        <result column="parent_processing_node" jdbcType="VARCHAR" property="parentProcessingNode"/>
        <result column="processing_name" jdbcType="VARCHAR" property="processingName"/>
        <result column="processing_type" jdbcType="VARCHAR" property="processingType"/>
        <result column="operator_id" jdbcType="INTEGER" property="operatorId"/>
        <result column="is_error" jdbcType="INTEGER" property="isError"/>
        <result column="error_msg" jdbcType="VARCHAR" property="errorMsg"/>
        <result column="store_info" jdbcType="VARCHAR" property="storeInfo"/>
        <result column="context_info" jdbcType="VARCHAR" property="contextInfo"/>
        <result column="master_node" jdbcType="VARCHAR" property="masterNode"/>
        <result column="tenant_id" jdbcType="INTEGER" property="tenantId"/>
        <result column="pod_ip" jdbcType="VARCHAR" property="podIp"/>
    </resultMap>

    <update id="updateReadErrorMessages">
        update data_process_record
        set error_msg_read_flag = 1
        where record_id = #{id}
    </update>


    <select id="selectStreamTaskMonitorList" resultType="com.trs.ai.moye.data.model.response.StreamProcessDataResponse">
        SELECT *
        FROM data_process_record
        <where>
            data_model_id = #{modelId} and storage_time >= #{beginTime} and storage_time &lt;= #{endTime}
            <if test="isError != null">
                and is_error = #{isError}
            </if>
            <if test="conditionKey != null and conditionKey != '' and operator != '' and operator != null and conditionValue != null">
                <choose>
                    <when test="conditionKey == 'EXECUTED_OPERATOR_COUNT'">
                        and executed_operator_count ${operator} #{conditionValue}
                    </when>
                    <when test="conditionKey == 'OPERATOR_COUNT'">
                        and operator_count ${operator} #{conditionValue}
                    </when>
                    <when test="conditionKey == 'PROCESSING_TIME'">
                        and processing_time ${operator} #{conditionValue}
                    </when>
                </choose>
            </if>
            <!-- 修改后的动态搜索条件 -->
            <if test="searchParams != null and searchParams.fields != null and searchParams.keyword != null and searchParams.keyword != ''">
                AND
                <trim prefix="(" suffix=")" prefixOverrides="OR | AND">
                    <foreach collection="searchParams.fields" item="field">
                        <choose>
                            <when test="field == 'msg_title'">
                                OR msg_title LIKE CONCAT('%', #{searchParams.keyword}, '%')
                            </when>
                            <when test="field == 'record_id'">
                                OR record_id = #{searchParams.keyword}
                            </when>
                        </choose>
                    </foreach>
                </trim>
            </if>
        </where>
        ORDER BY storage_time DESC
    </select>
    <select id="countErrorByDataModelId" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM data_process_record
        WHERE data_model_id = #{id}
          AND is_error = 1
          AND error_msg_read_flag = 0
    </select>
    <select id="countAllUnreadErrors" resultType="com.trs.ai.moye.common.dao.GroupCount">
        SELECT data_model_id as value, COUNT(1) as count
        FROM data_process_record
        WHERE is_error = 1
          AND error_msg_read_flag = 0
        GROUP BY data_model_id
    </select>
    <select id="selectTracerDataByProcessId" resultMap="TracerMap">
        SELECT *
        FROM data_process_trace
        WHERE record_id = #{recordId}
          AND process_id = #{processId} limit 1
    </select>
    <select id="selectTracerDataListByRecordId" resultMap="TracerMap">
        SELECT *
        FROM data_process_trace
        <where>
            record_id = #{recordId}
            AND data_source_id = #{dataModelId}
            AND master_node = #{masterNode}
        </where>
        ORDER BY node_order
    </select>


    <select id="getProcessCountByModelIdAndTimeParams" resultType="java.lang.Long">
        SELECT
        COUNT(DISTINCT record_id) AS processCount
        FROM data_process_trace
        WHERE parent_processing_node != 'DATA_ACCESS'
        <if test="startTime != null">
            and storage_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and storage_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="dataModelId != null">
            and data_source_id = #{dataModelId}
        </if>
    </select>

</mapper>