<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.data.model.dao.DataModelMonitorConfigDisplayMapper">
    <select id="monitorTypeVersionPageList" resultMap="com.trs.moye.base.monitor.dao.DataModelMonitorConfigMapper.BaseResultMap">
        select *
        from data_model_monitor_config
        <where>
            and data_model_id = #{dataModelId,jdbcType=INTEGER}
                  and type = #{query.monitorType}
            <if test="query.startTime != null">
                and update_time &gt;= #{query.startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="query.endTime != null">
                and update_time &lt;= #{query.endTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        <choose>
            <when test="query.sortParams == null">
                ORDER BY create_time DESC
            </when>
            <otherwise>
                ORDER BY create_time ${query.sortParams.order}
            </otherwise>
        </choose>
    </select>
</mapper>