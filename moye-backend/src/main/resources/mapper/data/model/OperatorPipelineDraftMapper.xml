<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.data.model.dao.operator.OperatorPipelineDraftMapper">
    <resultMap id="OperatorPipelineMap" type="com.trs.ai.moye.data.model.dto.arrangement.stream.OperatorPipelineDTO">
        <id column="id" property="id"/>
        <result column="data_model_id" property="dataModelId"/>
        <result column="data_source_connection_id" property="dataSourceConnectionId"/>
        <result column="canvas" property="canvas" javaType="com.trs.moye.ability.entity.operator.OperatorCanvas"
            typeHandler="com.trs.moye.ability.typehandler.OperatorCanvasTypeHandler"/>
        <result column="field_mapping" property="fieldMapping"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="input_fields" property="inputFields"
            javaType="com.trs.moye.ability.entity.operator.OperatorRowType"
            typeHandler="com.trs.moye.ability.typehandler.OperatorRowTypeHandler"/>
        <result column="operators" property="operators"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
    </resultMap>

    <select id="selectByDataModelId" resultMap="OperatorPipelineMap">
        SELECT *
        FROM operator_pipeline_draft
        WHERE data_model_id = #{dataModelId}
    </select>

    <insert id="insert">
        insert into operator_pipeline_draft(data_model_id,
                                            data_source_connection_id,
                                            canvas,
                                            field_mapping,
                                            input_fields,
                                            operators)
        values (#{dataModelId},
                #{dataSourceConnectionId},
                #{canvas, typeHandler=com.trs.moye.ability.typehandler.OperatorCanvasTypeHandler},
                #{fieldMapping, typeHandler=com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler},
                #{inputFields, typeHandler=com.trs.moye.ability.typehandler.OperatorRowTypeHandler},
                #{operators, typeHandler=com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler})
    </insert>

    <delete id="deleteByDataModelId">
        DELETE
        FROM operator_pipeline_draft
        WHERE data_model_id = #{dataModelId}
    </delete>
</mapper>