<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.model.dao.DataProcessMonitorConfigMapper">
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.trs.moye.base.monitor.entity.DataProcessMonitorConfig">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="data_model_id" jdbcType="INTEGER" property="dataModelId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="period_value" jdbcType="INTEGER" property="periodValue"/>
        <result column="period_unit" jdbcType="VARCHAR" property="periodUnit"/>
        <result column="is_enable" jdbcType="BIT" property="enable"/>
        <result column="schedule_status" jdbcType="VARCHAR" property="scheduleStatus"/>
        <result column="record_complete_logs" jdbcType="BIT" property="recordCompleteLogs"/>
        <result column="xxl_job_id" jdbcType="INTEGER" property="xxlJobId"/>
        <result column="create_by" jdbcType="INTEGER" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="INTEGER" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <select id="listByDataModelId" resultMap="BaseResultMap">
        select
        *
        from data_process_monitor_config
        <where>
            and data_model_id = #{dataModelId,jdbcType=INTEGER}
        </where>
    </select>
    <select id="getTypeMonitorConfig" resultMap="BaseResultMap">
        select
        *
        from data_process_monitor_config
        <where>
            and data_model_id = #{dataModelId,jdbcType=INTEGER}
            and `type` = #{type,jdbcType=VARCHAR}
        </where>
        limit 1
    </select>
</mapper>