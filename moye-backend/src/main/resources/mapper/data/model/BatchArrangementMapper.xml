<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.model.dao.BatchArrangementMapper">
    <resultMap id="BaseResultMap" type="com.trs.ai.moye.data.model.entity.BatchArrangement" >
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="data_model_id" property="dataModelId" />
        <result column="display_type" property="displayType" />
        <result column="arrangement" property="arrangement" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="code_sub_tasks" property="codeSubTasks" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="canvas" property="canvas" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="is_updated_tasks" property="isUpdatedTasks" />
    </resultMap>


</mapper>