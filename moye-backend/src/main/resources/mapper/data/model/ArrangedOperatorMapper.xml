<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.model.dao.ArrangedOperatorMapper">

    <select id="selectUsingDataModel" resultType="com.trs.ai.moye.data.model.response.KeyValueResponse">
        select data_model_id                                                 as `key`,
               (select zh_name from data_model d where d.id = data_model_id) as `value`
        from (select distinct data_model_id from stream_arranged_operator where operator_id = #{operatorId}) a
    </select>

    <select id="getStorageOperatorByDataModelId" resultType="java.lang.Boolean">
        select count(*) > 0  from stream_arranged_operator s
        join operator o on o.id = s.operator_id
        where s.data_model_id = #{dataModelId} and o.en_name = 'doSeaTunnelStorage'
    </select>
</mapper>