<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.data.model.dao.DwdModelRealtimeMonitorMapper">

    <resultMap type="com.trs.ai.moye.data.model.response.DwdRealtimeMonitorResponse"  id="MonitorResultMap">
        <id property="id" column="id"/>
        <result property="dataModelId" column="data_model_id"/>
        <result property="dataModelName" column="data_model_name"/>
        <result property="topic" column="topic"/>
        <result property="monitorTime" column="monitor_time"/>
        <result property="currentOffset" column="current_offset"/>
        <result property="endOffset" column="end_offset"/>
        <result property="lag" column="lag"/>
        <result property="avgTps" column="avg_tps"/>
        <result property="sourceType" column="source_type"/>
        <result property="sourceSubType" column="source_sub_type"/>
        <result property="storageTime" column="storage_time"/>
        <result property="dataQuality" column="data_quality"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="MonitorWhereClause">
        <where>
            data_model_id = #{dataModelId,jdbcType=INTEGER}
            <if test="timeRangeParams.minTime != null">
                AND monitor_time &gt;= #{timeRangeParams.minTime, jdbcType=TIMESTAMP}
            </if>
            <if test="timeRangeParams.maxTime != null">
                AND monitor_time &lt;= #{timeRangeParams.maxTime, jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>

    <!-- 根据要素库ID查询监控信息 -->
    <select id="dwdRealtimeMonitorPageList" resultMap="MonitorResultMap">
        SELECT
        id,
        data_model_id,
        data_model_name,
        topic,
        monitor_time,
        current_offset,
        end_offset,
        lag,
        avg_tps,
        source_type,
        source_sub_type,
        storage_time,
        data_quality,
        remark
        FROM t_monitor_dwd_realtime
        <include refid="MonitorWhereClause"/>
        ORDER BY monitor_time DESC
    </select>
    <select id="selectLagTrendList" resultType="com.trs.ai.moye.monitor.response.MonitorTrendResponse">
        SELECT
        monitor_time as time,
        lag as value
        FROM t_monitor_dwd_realtime
        <include refid="MonitorWhereClause"/>
        ORDER BY monitor_time
    </select>
    <select id="selectTpsTrendList" resultType="com.trs.ai.moye.monitor.response.MonitorTrendResponse">
        SELECT
        monitor_time as time,
        avg_tps as value
        FROM t_monitor_dwd_realtime
        <include refid="MonitorWhereClause"/>
        ORDER BY monitor_time
    </select>
    <select id="selectLastedMonitorRecord" resultMap="MonitorResultMap">
        select * from t_monitor_dwd_realtime
        <where>
            data_model_id = #{dataModelId,jdbcType=INTEGER}
        </where>
        order by monitor_time desc LIMIT 1
    </select>
</mapper>
