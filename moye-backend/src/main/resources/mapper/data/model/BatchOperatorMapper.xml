<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.data.model.dao.batch.BatchOperatorMapper">

    <sql id="tableName">batch_operator</sql>

    <delete id="deleteByArrangementId">
        DELETE
        FROM
        <include refid="tableName"/>
        WHERE arrangement_id = #{arrangementId}
    </delete>

    <delete id="deleteByDataModelId">
        DELETE
        FROM
        <include refid="tableName"/>
        WHERE data_model_id = #{dataModelId}
    </delete>

    <resultMap id="OperatorMap" type="com.trs.moye.ability.entity.operator.BatchOperator">
        <id column="id" property="id"/>
        <result column="arrangement_id" property="arrangementId"/>
        <result column="data_model_id" property="dataModelId"/>
        <result column="canvas" property="canvas"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="target_display_ids" property="targetDisplayIds"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="parent_display_ids" property="parentDisplayIds"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="type" property="type"/>
        <result column="name" property="name"/>
        <result column="desc" property="desc"/>
        <result column="enabled" property="enabled"/>
        <result column="conditions" property="conditions"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="input_fields" property="inputFields"
            typeHandler="com.trs.moye.ability.typehandler.OperatorRowTypeHandler"/>
        <result column="output_fields" property="outputFields"
            typeHandler="com.trs.moye.ability.typehandler.OperatorRowTypeHandler"/>
        <result column="input_bind" property="inputBind"
            typeHandler="com.trs.moye.ability.typehandler.InputBindTypeHandler"/>
        <result column="output_bind" property="outputBind"
            typeHandler="com.trs.moye.ability.typehandler.OutputBindTypeHandler"/>
        <result column="ability_id" property="abilityId"/>
        <result column="table_type" property="tableType"/>
        <result column="table_data_model_id" property="tableDataModelId"/>
        <result column="table_storage_id" property="tableStorageId"/>
        <result column="table_is_increment" property="tableIsIncrement"/>
        <result column="output_table_name" property="outputTableName"/>
        <association column="ability_id" property="ability"
            javaType="com.trs.moye.ability.entity.Ability"
            select="com.trs.ai.moye.data.ability.dao.AbilityMapper.selectById"/>
    </resultMap>

    <select id="selectByArrangementId" resultMap="OperatorMap">
        SELECT *
        FROM
        <include refid="tableName"/>
        WHERE arrangement_id = #{arrangementId}
    </select>

    <select id="selectConnectionStorageByDataModelId" resultType="java.lang.Integer">
        select s.id from batch_operator o left join data_storage s on s.id = o.table_storage_id
        where o.data_model_id=#{dataModelId}
        <if test="layer !=null">
            and o.table_type = #{layer}
        </if>
        and o.table_data_model_id =#{modelId}
        limit 1
    </select>

    <select id="selectUsingDataModel" resultType="com.trs.ai.moye.data.model.response.KeyValueResponse">
        SELECT data_model_id AS `key`,
        (SELECT zh_name FROM data_model d WHERE d.id = data_model_id) AS `value`
        FROM (SELECT DISTINCT data_model_id
        FROM batch_operator
        WHERE ability_id = #{abilityId}) a
    </select>

    <select id="selectStoragePointUsingBatchModel" resultType="com.trs.ai.moye.data.model.response.KeyValueResponse">
        SELECT DISTINCT data_model_id as `key`, (SELECT zh_name FROM data_model d WHERE d.id = data_model_id) AS `value`
        FROM batch_operator
        WHERE table_storage_id = #{storagePointId}
    </select>
</mapper>