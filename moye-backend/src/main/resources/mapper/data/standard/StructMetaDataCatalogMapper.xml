<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.standard.dao.StructMetaDataCatalogMapper">

    <select id="countByNameAndExcludeId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM struct_meta_data_catalog
        WHERE name = #{name}
        AND pid = #{pid}
        <if test="id != null">
            AND id != #{id}
        </if>
    </select>

    <select id="countByPid" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM struct_meta_data_catalog
        WHERE pid = #{pid}
    </select>

</mapper>