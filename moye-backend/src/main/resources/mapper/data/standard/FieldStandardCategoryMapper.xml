<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.standard.dao.FieldStandardCategoryMapper">


    <resultMap id="CategoryTreeMap" type="com.trs.ai.moye.data.standard.entity.DataStandardCategoryTree">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="pid" jdbcType="BIGINT" property="pid"/>
        <collection property="children" column="{id=id}" select="findAllByParentId"
            ofType="com.trs.ai.moye.data.standard.entity.DataStandardCategoryTree"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
                create_time,
                create_by,
                update_time,
                update_by,
                name,
                pid
    </sql>

    <insert id="insertOne">
        INSERT INTO field_standard_category
            (create_time, create_by, update_time, update_by, `name`, pid)
        VALUES (#{fieldStandardCategory.createTime},
                #{fieldStandardCategory.createBy},
                #{fieldStandardCategory.updateTime},
                #{fieldStandardCategory.updateBy},
                #{fieldStandardCategory.name},
                #{fieldStandardCategory.pid})
    </insert>
    <update id="updateOne">
        UPDATE field_standard_category
        <set>
            <if test="null != fieldStandardCategory.updateTime ">update_time = #{fieldStandardCategory.updateTime},</if>
            <if test="null != fieldStandardCategory.updateBy ">update_by = #{fieldStandardCategory.updateBy},</if>
            <if test="null != fieldStandardCategory.name and '' != fieldStandardCategory.name">name = #{fieldStandardCategory.name},</if>
            <if test="null != fieldStandardCategory.pid ">pid = #{fieldStandardCategory.pid}</if>
        </set>
        WHERE id = #{fieldStandardCategory.id}
    </update>

    <delete id="deleteOne">
        DELETE
        FROM field_standard_category
        WHERE id = #{id}
    </delete>

    <select id="selectCategoryTree" resultMap="CategoryTreeMap" >
        select *
        from field_standard_category
        where pid = 0
    </select>

    <select id="findAllByParentId" resultMap="CategoryTreeMap">
        select t1.id as id ,t1.name as name ,t1.pid as pid,null as en_name
        from field_standard_category t1
        where t1.pid = #{id}
    </select>
    <select id="checkCategoryName" resultType="java.lang.Boolean">
        select count(0) from field_standard_category
        <where>
            name=#{request.name}
            <if test="@java.util.Objects@nonNull(request.pid)">
                and  pid = #{request.pid}
            </if>
            <if test="@java.util.Objects@nonNull(request.id)">
                and id  != #{request.id}
            </if>
        </where>
    </select>
    <select id="countByPid" resultType="java.lang.Integer">
        select count(0)
        from field_standard_category
        where pid = #{pid}
    </select>

</mapper>

