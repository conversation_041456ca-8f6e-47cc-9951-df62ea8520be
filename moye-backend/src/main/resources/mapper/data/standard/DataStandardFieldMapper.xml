<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.standard.dao.DataStandardFieldMapper">
    <!-- 结果集映射 -->
    <resultMap id="DataStandardFieldMap" type="com.trs.moye.base.data.standard.entity.DataStandardField">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="category_id" jdbcType="BIGINT" property="categoryId"/>
        <result column="zh_name" jdbcType="VARCHAR" property="zhName"/>
        <result column="en_name" jdbcType="VARCHAR" property="enName"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="type_name" jdbcType="VARCHAR" property="typeName"/>
        <result column="is_multi_value" jdbcType="BIT" property="multiValue"/>
        <result column="is_nullable" jdbcType="BIT" property="nullable"/>
        <result column="is_statistic" jdbcType="BIT" property="statistic"/>
        <result column="is_built_in" jdbcType="BIT" property="builtIn"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="description" jdbcType="LONGVARCHAR" property="description"/>
        <result column="default_value" jdbcType="VARCHAR" property="defaultValue"/>
        <result column="advance_config" property="advanceConfig"
            typeHandler="com.trs.moye.base.common.typehandler.PolymorphismTypeHandler"/>
    </resultMap>
    <!-- 属性列表 -->
    <sql id="Base_Column_List">
        <trim suffixOverrides=",">
            id,
            category_id,
            zh_name,
            en_name,
            type,
            type_name,
            is_multi_value,
            is_nullable,
            is_statistic,
            is_built_in,
            create_by,
            create_time,
            update_by,
            update_time,
            description,
            default_value,
            advance_config,
        </trim>
    </sql>
    <delete id="deleteByCategoryId">
        delete
        from data_standard_field
        where category_id = #{id}
    </delete>

    <select id="getByZhNameOrEnName" resultMap="DataStandardFieldMap">
        SELECT
        *
        FROM
        data_standard_field
        <where>
            (category_id = #{categoryId,jdbcType=INTEGER} OR category_id = 0)
            <if test="zhName != null and zhName != '' and enName != null and enName != ''">
                AND zh_name = #{zhName,jdbcType=VARCHAR}
                AND en_name = #{enName,jdbcType=VARCHAR}
            </if>
            <if test="(zhName == null or zhName == '') and (enName != null or enName != '')">
                AND en_name = #{enName,jdbcType=VARCHAR}
            </if>
            <if test="(enName == null or enName == '') and (zhName != null or zhName != '')">
                AND zh_name = #{zhName,jdbcType=VARCHAR}
            </if>
        </where>
        LIMIT 1
    </select>
    <select id="selectAll" resultMap="DataStandardFieldMap">
        select *
        from data_standard_field
    </select>
    <select id="getDataStandardList" resultMap="DataStandardFieldMap">
        select
        *
        from data_standard_field
        <where>
            <if test="request.categoryId != null">
                (category_id = #{request.categoryId} or (category_id = 0 and is_built_in = 1))
            </if>
            <if test="request.dataModelId != null">
                and en_name not in (select en_name from data_model_field where data_model_id =
                #{request.dataModelId,jdbcType=INTEGER})
                and zh_name not in (select zh_name from data_model_field where data_model_id =
                #{request.dataModelId,jdbcType=INTEGER})
            </if>
            <if test="request.keyword != null and request.keyword != ''">
                and (zh_name like concat('%',#{request.keyword},'%') or en_name like concat('%',#{request.keyword},'%'))
            </if>
            <if test="request.type != null">
                and `type` = #{request.type}
            </if>
        </where>

        order by
            is_built_in DESC,
            update_time desc
    </select>
    <select id="selectByPrimaryKey" resultMap="DataStandardFieldMap">
        select *
        from data_standard_field
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByIdCollection" resultMap="DataStandardFieldMap">
        select
        *
        from data_standard_field
        <where>
            <choose>
                <when test="idCollection == null or idCollection.size == 0">
                    and false
                </when>
                <otherwise>
                    and id in
                    <foreach close=")" collection="idCollection" item="id" open="(" separator=",">
                        #{id,jdbcType=BIGINT}
                    </foreach>
                </otherwise>
            </choose>
        </where>
    </select>
    <select id="selectByEnNameCollection" resultMap="DataStandardFieldMap">
        select
        *
        from data_standard_field
        <where>
            category_id = #{categoryId,jdbcType=INTEGER} OR category_id = 0
            <choose>
                <when test="enNameCollection == null or enNameCollection.size == 0">
                    and false
                </when>
                <otherwise>
                    and en_name in
                    <foreach close=")" collection="enNameCollection" item="enName" open="(" separator=",">
                        #{enName,jdbcType=VARCHAR}
                    </foreach>
                </otherwise>
            </choose>
        </where>
    </select>
    <select id="getByZhName" resultMap="DataStandardFieldMap">
        select
        *
        from data_standard_field
        <where>
            and zh_name = #{zhName,jdbcType=VARCHAR}
        </where>
    </select>
    <select id="getByEnName" resultMap="DataStandardFieldMap">
        select
        *
        from data_standard_field
        <where>
            and en_name = #{enName,jdbcType=VARCHAR}
        </where>
    </select>
    <select id="listByCategoryId" resultMap="DataStandardFieldMap">
        select
        *
        from data_standard_field
        <where>
            and category_id = #{categoryId,jdbcType=BIGINT}
        </where>
    </select>
    <select id="listByZhNameCollection" resultMap="DataStandardFieldMap">
        select
        *
        from data_standard_field
        <where>
            <choose>
                <when test="zhNameCollection == null or zhNameCollection.size == 0">
                    and false
                </when>
                <otherwise>
                    and zh_name in
                    <foreach close=")" collection="zhNameCollection" item="zhName" open="(" separator=",">
                        #{zhName,jdbcType=VARCHAR}
                    </foreach>
                </otherwise>
            </choose>
        </where>
    </select>
    <select id="listByEnNameCollection" resultMap="DataStandardFieldMap">
        select
        *
        from data_standard_field
        <where>
            <choose>
                <when test="enNameCollection == null or enNameCollection.size == 0">
                    and false
                </when>
                <otherwise>
                    and en_name in
                    <foreach close=")" collection="enNameCollection" item="enName" open="(" separator=",">
                        #{enName,jdbcType=VARCHAR}
                    </foreach>
                </otherwise>
            </choose>
        </where>
    </select>
    <select id="listByCategoryIdCollection" resultMap="DataStandardFieldMap">
        select
        *
        from data_standard_field
        <where>
            <choose>
                <when test="categoryIdCollection == null or categoryIdCollection.size == 0">
                    and false
                </when>
                <otherwise>
                    and category_id in
                    <foreach close=")" collection="categoryIdCollection" item="categoryId" open="(" separator=",">
                        #{categoryId,jdbcType=BIGINT}
                    </foreach>
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="listBuiltInFields" resultMap="DataStandardFieldMap">
        select *
        from data_standard_field
        where is_built_in = 1
    </select>
    <select id="countByCategoryId" resultType="java.lang.Integer">
        select count(0)
        from data_standard_field
        where category_id = #{categoryId}
    </select>

    <select id="selectByType" resultMap="DataStandardFieldMap">
        select *
        from data_standard_field
        where `type` = #{type}
    </select>
    <select id="selectByTypeTypeName" resultMap="DataStandardFieldMap">
        select *
        from data_standard_field
        where `type` = #{type}
          and type_name = #{typeName}
    </select>
</mapper>