<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.standard.dao.StructMetaDataMapper">

    <select id="countByCatalogId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM struct_meta_data
        WHERE catalog_id = #{catalogId}
    </select>

    <select id="countByEnNameAndExcludeId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM struct_meta_data
        WHERE en_name = #{enName}
        <if test="id != null">
            AND id != #{id}
        </if>
    </select>

    <select id="countByNameAndCatalogIdAndExcludeId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM struct_meta_data
        WHERE catalog_id = #{catalogId}
        AND (zh_name = #{zhName} OR en_name = #{enName})
        <if test="id != null">
            AND id != #{id}
        </if>
    </select>
    <select id="countByEnNameAndCatalogId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM struct_meta_data
        WHERE en_name = #{enName}
          AND catalog_id = #{catalogId}
    </select>
    <select id="countByEnNameOrZhNameAndExcludeId" resultType="java.lang.Integer">

        SELECT COUNT(*)
        FROM struct_meta_data
        <where>
            catalog_id = #{catalogId}
            <if test="zhName != null and zhName != '' and enName != null and enName != ''">
                AND zh_name = #{zhName,jdbcType=VARCHAR}
                AND en_name = #{enName,jdbcType=VARCHAR}
            </if>
            <if test="(zhName == null or zhName == '') and (enName != null or enName != '')">
                AND en_name = #{enName,jdbcType=VARCHAR}
            </if>
            <if test="(enName == null or enName == '') and (zhName != null or zhName != '')">
                AND zh_name = #{zhName,jdbcType=VARCHAR}
            </if>
            <if test="id != null">
                AND id != #{id}
            </if>
        </where>

    </select>


</mapper>