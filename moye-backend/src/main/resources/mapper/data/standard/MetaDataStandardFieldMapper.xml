<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.standard.dao.MetaDataStandardFieldMapper">

  <resultMap id="BaseResultMap" type="com.trs.moye.base.data.standard.entity.MetaDataStandardField">
    <id property="id" column="id" jdbcType="INTEGER"/>
    <result property="metaDataStandardId" column="meta_data_standard_id" jdbcType="INTEGER"/>
    <result property="zhName" column="zh_name" jdbcType="VARCHAR"/>
    <result property="enName" column="en_name" jdbcType="VARCHAR"/>
    <result property="description" column="description" jdbcType="VARCHAR"/>
    <result property="type" column="type" jdbcType="VARCHAR"/>
    <result property="fields" column="fields" jdbcType="VARCHAR" typeHandler="com.trs.moye.base.data.standard.typehandler.TagEdgeFieldsTypeHandler"/>
    <result property="primaryKey" column="primary_key" jdbcType="VARCHAR"/>
    <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
    <result property="createTime" column="create_time" jdbcType="DATE"/>
    <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    <result property="updateTime" column="update_time" jdbcType="DATE"/>
  </resultMap>

  <sql id="Base_Column_List">
    id
    ,meta_data_standard_id,zh_name,
        en_name,description,type,
        fields,primary_key,create_by,
        create_time,update_by,update_time
  </sql>
  <delete id="deleteByStandardId">
    delete from meta_data_standard_field
    where meta_data_standard_id = #{standardId}
  </delete>
  <select id="selectSpecifiedFieldsByStandardId"
    resultMap="BaseResultMap">
    select *
    from meta_data_standard_field
    where meta_data_standard_id = #{param.metaDataStandardId}
    and type = #{param.fieldType}
    <if test="param.keyWord != null and param.keyWord != ''">
      and (
          zh_name like concat('%',#{param.keyWord},'%')
              or en_name like concat('%',#{param.keyWord},'%')
          )
    </if>
  </select>

  <select id="selectByMetaDataStandardId" resultMap="BaseResultMap">
    select *
    from meta_data_standard_field
    where meta_data_standard_id = #{metaDataStandardId}
  </select>

  <select id="selectByEnNameAndStandardId"
          resultType="com.trs.moye.base.data.standard.entity.MetaDataStandardField">
    select *
    from meta_data_standard_field
    where meta_data_standard_id = #{metaDataStandardId}
      and en_name = #{enName}
  </select>

  <select id="countByEnNameAndExcludeId" resultType="java.lang.Integer">
    select count(1)
    from meta_data_standard_field
    where meta_data_standard_id = #{metaDataStandardId}
      and en_name = #{enName}
    <if test="id != null">
      and id != #{id}
    </if>
  </select>

  <select id="selectByEnNameListAndStandardId" resultType="java.lang.String">
    select en_name
    from meta_data_standard_field
    where meta_data_standard_id = #{metaDataStandardId}
      and en_name in
    <foreach collection="enNameList" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <delete id="deleteByEnNameListAndStandardId">
    delete from meta_data_standard_field
    where meta_data_standard_id = #{metaDataStandardId}
    and
    <foreach collection="type2EnNames.entrySet()" index="type" item="enNames" separator=" OR " open="(" close=")">
      (
          `type` = #{type}
          AND en_name in
          <foreach collection="enNames" item="enName" separator=", " open="(" close=")">
            #{enName}
          </foreach>
      )
    </foreach>
  </delete>

  <select id="selectTagByEnName"
      resultType="com.trs.moye.base.data.standard.entity.MetaDataStandardField">
    select *
    from meta_data_standard_field
    where
    meta_data_standard_id = #{metaDataStandardId}
    and `type` = 'GRAPHICS_TAG'
    and en_name = #{enName}
    limit 1
  </select>
  <select id="selectEdgeByEnName"
      resultType="com.trs.moye.base.data.standard.entity.MetaDataStandardField">
    select *
    from meta_data_standard_field
    where
    meta_data_standard_id = #{metaDataStandardId}
    and `type` = 'GRAPHICS_EDGE'
    and en_name = #{enName}
    limit 1
  </select>
  <update id="updateFieldsName">
    UPDATE meta_data_standard_field
    SET en_name = CASE
        <foreach collection="fields" item="field" index="index">
          WHEN en_name = #{field.oldName} AND `type` = #{field.type} THEN #{field.newName}
        </foreach>
        ELSE en_name
    END
    WHERE meta_data_standard_id = #{metaDataStandardId}
  </update>
</mapper>
