<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.standard.dao.MetaDataStandardCatalogMapper">

    <resultMap id="BaseResultMap" type="com.trs.ai.moye.data.standard.entity.MetaDataStandardCatalogTree">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="pid" column="pid" jdbcType="INTEGER"/>
            <collection property="children" column="{id=id}" select="findAllByParentId"
                ofType="com.trs.ai.moye.data.standard.entity.MetaDataStandardCatalog"/>
    </resultMap>

    <sql id="Base_Column_List">
      id,
      name,
      description,
      pid,
      create_by,
      create_time,
      update_by,
      update_time
    </sql>

  <select id="selectCatalogTree" resultMap="BaseResultMap">
        select *
        from meta_data_standard_catalog
        where pid is null or pid = 0
    </select>

    <select id="findAllByParentId" resultMap="BaseResultMap">
        select *
        from meta_data_standard_catalog
        where pid = #{id}
    </select>

  <select id="selectByCatalogName" resultType="com.trs.ai.moye.data.standard.entity.MetaDataStandardCatalog">
    select *
    from meta_data_standard_catalog
    where name = #{name}
  </select>
  <select id="countByNameAndExcludeId" resultType="java.lang.Integer">
    select count(1)
    from meta_data_standard_catalog
    where name = #{name}
    <if test="id != null">
      and id != #{id}
    </if>
  </select>
</mapper>
