<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.standard.dao.MetaDataStandardMapper">

  <resultMap id="BaseResultMap" type="com.trs.ai.moye.data.standard.entity.GraphicsMetaDataStandard">
    <id property="id" column="id" jdbcType="INTEGER"/>
    <result property="zhName" column="zh_name" jdbcType="VARCHAR"/>
    <result property="enName" column="en_name" jdbcType="VARCHAR"/>
    <result property="type" column="type" jdbcType="VARCHAR"/>
    <result property="description" column="description" jdbcType="VARCHAR"/>
    <result property="metaDataStandardCatalogId" column="meta_data_standard_catalog_id" jdbcType="INTEGER"/>
    <result property="vidType" column="vid_type" jdbcType="VARCHAR"/>
    <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
  </resultMap>

  <sql id="Base_Column_List">
    id
    ,zh_name,en_name,
        type,description,meta_data_standard_catalog_id,
        vid_type,create_by,create_time,
        update_by,update_time
  </sql>
  <update id="updateOne">
    update meta_data_standard
    set zh_name     = #{metaDataStandard.zhName},
        en_name     = #{metaDataStandard.enName},
        description = #{metaDataStandard.description},
        vid_type    = #{metaDataStandard.vidType}
    where id = #{metaDataStandard.id}
  </update>

  <select id="selectByCatalogId" resultType="com.trs.ai.moye.data.standard.entity.GraphicsMetaDataStandard">
    select *
    from meta_data_standard
    where meta_data_standard_catalog_id = #{catalogId}
  </select>

  <select id="selectByEnName" resultType="com.trs.ai.moye.data.standard.entity.GraphicsMetaDataStandard">
    select *
    from meta_data_standard
    where en_name = #{enName}
  </select>

  <select id="countByEnNameAndExcludeId" resultType="java.lang.Integer">
    select count(1)
    from meta_data_standard
    where en_name = #{enName}
    <if test="id != null">
      and id != #{id}
    </if>
  </select>
  <select id="selectPageByKeyword" resultType="com.trs.moye.base.data.standard.entity.MetaDataStandard">
    select *
    from meta_data_standard
    <if test="standardType != null and standardType != ''">
      where type = #{standardType}
    </if>
  </select>
  <select id="selectByStandardType" resultType="com.trs.moye.base.data.standard.entity.MetaDataStandard">
    select *
    from meta_data_standard
    <if test="standardType != null and standardType != ''">
      where type = #{standardType}
    </if>
  </select>

  <select id="selectByDataModelId" resultType="com.trs.ai.moye.data.standard.entity.GraphicsMetaDataStandard">
    select *
    from meta_data_standard
    where id = (select meta_data_standard_id from data_model where id = #{dataModelId})
  </select>
</mapper>
