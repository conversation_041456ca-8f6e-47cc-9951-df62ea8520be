<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.standard.dao.StructMetaDataFieldMapper">

    <resultMap id="BaseResultMap" type="com.trs.ai.moye.data.standard.entity.StructMetaDataField">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="struct_meta_data_id" jdbcType="BIGINT" property="structMetaDataId"/>
        <result column="zh_name" jdbcType="VARCHAR" property="zhName"/>
        <result column="en_name" jdbcType="VARCHAR" property="enName"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="type_name" jdbcType="VARCHAR" property="typeName"/>
        <result column="is_multi_value" jdbcType="BIT" property="multiValue"/>
        <result column="is_nullable" jdbcType="BIT" property="nullable"/>
        <result column="is_statistic" jdbcType="BIT" property="statistic"/>
        <result column="is_built_in" jdbcType="BIT" property="builtIn"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="description" jdbcType="LONGVARCHAR" property="description"/>
        <result column="default_value" jdbcType="VARCHAR" property="defaultValue"/>
        <result column="advance_config" property="advanceConfig"
            typeHandler="com.trs.moye.base.common.typehandler.PolymorphismTypeHandler"/>
    </resultMap>
    <select id="countByStructMetaDataId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM struct_meta_data_field
        WHERE struct_meta_data_id = #{structMetaDataId}
    </select>
    <select id="selectByStructMetaDataId" resultMap="BaseResultMap">
        select *
        from struct_meta_data_field
        where struct_meta_data_id = #{structMetaDataId}
    </select>

    <select id="selectPageList" resultMap="BaseResultMap">
        select
        *
        from struct_meta_data_field
        <where>
            <if test="request.metaDataId != null">
                struct_meta_data_id = #{request.metaDataId}
            </if>
            <if test="request.keyword != null and request.keyword != ''">
                and (zh_name like concat('%',#{request.keyword},'%') or en_name like concat('%',#{request.keyword},'%'))
            </if>
            <if test="request.type != null">
                and type = #{request.type}
            </if>
        </where>
        order by update_time desc

    </select>
    <select id="countByEnNameOrZhNameAndExcludeId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM struct_meta_data_field
        <where>
            struct_meta_data_id = #{structMetaDataId}
            <if test="zhName != null and zhName != '' and enName != null and enName != ''">
                AND zh_name = #{zhName,jdbcType=VARCHAR}
                AND en_name = #{enName,jdbcType=VARCHAR}
            </if>
            <if test="(zhName == null or zhName == '') and (enName != null or enName != '')">
                AND en_name = #{enName,jdbcType=VARCHAR}
            </if>
            <if test="(enName == null or enName == '') and (zhName != null or zhName != '')">
                AND zh_name = #{zhName,jdbcType=VARCHAR}
            </if>
            <if test="id != null">
                AND id != #{id}
            </if>
        </where>
    </select>

</mapper>