<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.service.dao.DataServiceConfigMapper">
    <resultMap id="DataServiceConfigMap" type="com.trs.ai.moye.data.service.entity.DataServiceConfig">
        <id column="id" property="id"/>
        <result column="data_service_id" property="dataServiceId"/>
        <result column="type" property="type"/>
        <result column="params" property="params"
            typeHandler="com.trs.ai.moye.data.service.typehandler.DataServiceConfigParamsTypeHandler"/>
    </resultMap>
    <update id="updateByDataServiceId">
        update data_service_config
        <set>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="params != null">
                params = #{params, typeHandler=com.trs.ai.moye.data.service.typehandler.DataServiceConfigParamsTypeHandler},
            </if>
        </set>
        where data_service_id = #{dataServiceId}
    </update>
    <delete id="deleteByDataServiceId">
        delete
        from data_service_config
        where data_service_id = #{dataServiceId}
    </delete>
    <select id="selectOneByDataServiceId" resultMap="DataServiceConfigMap">
        select *
        from data_service_config
        where data_service_id = #{dataServiceId}
        limit 1
    </select>
</mapper>

