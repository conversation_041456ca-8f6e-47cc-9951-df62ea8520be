<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.service.dao.DataServiceCategoryMapper">
    <resultMap id="CategoryTreeMap" type="com.trs.ai.moye.data.service.entity.DataServiceCategoryTree">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="pid" jdbcType="BIGINT" property="pid"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="description" jdbcType="LONGVARCHAR" property="description"/>
        <collection property="children" column="{id=id}" select="findAllByParentId"
            ofType="com.trs.ai.moye.data.service.entity.DataServiceCategory"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        create_time,
        create_by,
        update_time,
        update_by,
        name,
        pid,
        description
    </sql>
    <select id="selectCategoryTree" resultMap="CategoryTreeMap">
        select *
        from data_service_category
        where pid is null or pid = 0
    </select>

    <select id="findAllByParentId" resultMap="CategoryTreeMap">
        select *
        from data_service_category
        where pid = #{id}
    </select>
    <select id="checkCategoryName" resultType="java.lang.Boolean">
        select count(0) from data_service_category
        <where>
            name=#{category.name}
            <if test="@java.util.Objects@nonNull(category.pid)">
                and pid = #{category.pid}
            </if>
            <if test="@java.util.Objects@nonNull(category.id)">
                and id != #{category.id}
            </if>
        </where>
    </select>
    <select id="selectCategoryTreeByPid" resultMap="CategoryTreeMap">
        select *
        from data_service_category
        where pid = #{pid}
    </select>
    <select id="selectCatalogTree" resultType="com.trs.ai.moye.data.service.response.DataServiceOutResponse">
      select
      name,
      id as catalogId
      from data_service_category
      where
      <choose>
        <when test="isRoot==true">
          pid = 0
        </when>
        <otherwise>
          pid = #{parentId}
        </otherwise>
      </choose>
    </select>

</mapper>

