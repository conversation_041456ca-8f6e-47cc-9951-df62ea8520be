<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.service.dao.DataServiceMapper">
    <resultMap id="DataServiceMap" type="com.trs.ai.moye.data.service.entity.DataService">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="connection_id" property="connectionId"/>
        <result column="storage_id" property="storageId"/>
        <result column="category_id" property="categoryId"/>
        <result column="create_mode" property="createMode"/>
        <result column="publish_status" property="publishStatus"/>
        <result column="health_status" property="healthStatus"/>
        <result column="ability_center_id" property="abilityCenterId"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="DataServiceDtoMap" type="com.trs.ai.moye.data.service.dto.DataServiceDto" extends="DataServiceMap">
        <association property="dataStorage" javaType="com.trs.moye.base.data.storage.DataStorage">
            <result column="data_storage_id" property="id"/>
            <result column="data_storage_en_name" property="enName"/>
            <result column="data_storage_zh_name" property="zhName"/>
            <result column="data_storage_connection_id" property="connectionId"/>
            <result column="data_model_id" property="dataModelId"/>
            <result column="create_table_status" property="createTableStatus"/>
            <result column="field_ids" property="fieldIds"
                typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
            <result column="settings" property="settings"
                typeHandler="com.trs.moye.base.data.storage.setting.DataStorageSettingsTypeHandler"/>
        </association>
        <association property="dataModel" javaType="com.trs.moye.base.data.model.entity.DataModel">
            <id column="data_model_id" jdbcType="BIGINT" property="id"/>
            <result column="data_model_create_mode" jdbcType="VARCHAR" property="createMode"/>
            <result column="execute_status" jdbcType="VARCHAR" property="executeStatus"/>
            <result column="data_model_zh_name" jdbcType="VARCHAR" property="zhName"/>
            <result column="data_model_en_name" jdbcType="VARCHAR" property="enName"/>
            <result column="business_category_id" jdbcType="INTEGER" property="businessCategoryId"/>
            <result column="layer" jdbcType="VARCHAR" property="layer"/>
            <result column="data_model_description" jdbcType="VARCHAR" property="description"/>
        </association>
        <association property="dataModelCategory" javaType="com.trs.moye.base.data.model.entity.BusinessCategory">
            <result column="data_model_category_id" property="id"/>
            <result column="category_zh_name" property="zhName"/>
            <result column="category_en_name" property="enName"/>
            <result column="category_description" property="description"/>
        </association>
        <association property="dataServiceConfig" column="id"
            select="com.trs.ai.moye.data.service.dao.DataServiceConfigMapper.selectOneByDataServiceId"/>
        <association column="connection_id" property="connection"
            javaType="com.trs.moye.base.data.connection.entity.DataConnection"
            select="com.trs.moye.base.data.connection.dao.DataConnectionMapper.selectById"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        code,
        name,
        description,
        storage_id,
        category_id,
        create_mode,
        publish_status,
        health_status,
        ability_center_id,
        create_by,
        update_by,
        create_time,
        update_time
    </sql>


    <select id="checkName" resultType="java.lang.Boolean">
        select count(0) from data_service
        <where>
            name=#{checkNameParam.name}
            <if test="@java.util.Objects@nonNull(checkNameParam.categoryId)">
                and category_id = #{checkNameParam.categoryId}
            </if>
            <if test="@java.util.Objects@nonNull(checkNameParam.id)">
                and id != #{checkNameParam.id}
            </if>
        </where>
    </select>
    <select id="selectByCode" resultMap="DataServiceMap">
        select *
        from data_service
        where code = #{code}
    </select>

    <!-- 提取公共的查询逻辑 -->
    <sql id="commonSelect">
        SELECT ds.*,

               dsg.*,
               dsg.id            as data_storage_id,
               dsg.zh_name       as data_storage_zh_name,
               dsg.en_name       as data_storage_en_name,
               dsg.connection_id as data_storage_connection_id,

               dm.*,
               dm.en_name        as data_model_en_name,
               dm.zh_name        as data_model_zh_name,
               dm.id             as data_model_id,
               dm.create_mode    as data_model_create_mode,
               dm.description    as data_model_description,
               bc.id             as data_model_category_id,
               bc.zh_name        as category_zh_name,
               bc.en_name        as category_en_name,
               bc.description    as category_description
        FROM data_service ds
                 LEFT JOIN data_storage dsg ON ds.storage_id = dsg.id
                 LEFT JOIN data_model dm ON dm.id = dsg.data_model_id
                 LEFT JOIN business_category bc ON dm.business_category_id = bc.id
    </sql>

    <select id="selectDtoById" resultMap="DataServiceDtoMap">
        <include refid="commonSelect"/>
        <where>
            ds.id = #{id}
        </where>
    </select>
    <select id="selectByStorageIds" resultType="com.trs.ai.moye.data.service.entity.DataService">
        SELECT * FROM data_service
        WHERE storage_id IN
        <foreach item="id" collection="storageIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectDtoByCode" resultMap="DataServiceDtoMap">
        <include refid="commonSelect"/>
        <where>
            ds.code = #{code}
        </where>
    </select>
    <select id="selectDtoByStorageIds" resultMap="DataServiceDtoMap">
        <include refid="commonSelect"/>
        <where>
            ds.storage_id IN
            <foreach item="id" collection="storageIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>
    <select id="selectByCategoryId" resultMap="DataServiceMap">
        select *
        from data_service
        where category_id = #{categoryId}
    </select>

    <select id="selectByIds" resultType="com.trs.ai.moye.data.service.entity.DataService">
        select *
        from data_service
        where id in
        <foreach collection="ids" index="index" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>
    <select id="selectByCategoryIds" resultType="com.trs.ai.moye.data.service.response.DataServiceOutResponse">
        select
        id,
        name,
        `category_id` as businessId, `description` as `desc`
        from `data_service`
        where
        <foreach collection="businessIds" item="item" open=" `category_id` in (" close=")" separator="," index="index">
            #{item}
        </foreach>
        <include refid="ModeCond"></include>
        <include refid="SearchCond"></include>
    </select>

    <sql id="SearchCond">
        <if test="@java.util.Objects@nonNull(searchRequest)">
            <if test="searchRequest.fields != null and searchRequest.fields.size() != 0 and searchRequest.keyword != ''">
                <foreach collection="searchRequest.fields" item="field" index="index" open="and (" close=")"
                    separator="or">
                    <if test="field == 'name'">
                        `name` like concat ('%', #{searchRequest.keyword,jdbcType=VARCHAR} ,'%')
                    </if>
                </foreach>
            </if>
        </if>
    </sql>
    <sql id="ModeCond">
        <if test="@java.util.Objects@nonNull(mode)">
            and create_mode = #{mode}
        </if>
    </sql>

    <update id="updatePublishStatusById">
        update data_service
        <set>
            publish_status = #{publishStatus}
        </set>
        where id = #{id}
    </update>
    <update id="updateCategoryIdByIds">
        UPDATE data_service
        SET category_id = #{categoryId}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

    </update>

    <select id="selectDtoByBusinessCategoryId" resultMap="DataServiceDtoMap">
        <include refid="commonSelect"/>
        <where>
            dm.business_category_id = #{businessCategoryId}
        </where>
    </select>

</mapper>
