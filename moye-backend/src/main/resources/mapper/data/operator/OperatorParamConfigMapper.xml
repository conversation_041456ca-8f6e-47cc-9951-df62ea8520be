<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.operator.dao.OperatorParamConfigMapper">

    <select id="selectByOperatorId" resultMap="mybatis-plus_OperatorParamConfig">
        select * from operator_param_config where operator_id = #{operatorId}
    </select>

    <select id="selectByOperatorIdAndPosition" resultMap="mybatis-plus_OperatorParamConfig">
        select * from operator_param_config where operator_id = #{operatorId} and position = #{position}
    </select>

    <delete id="deleteByOperatorId">
        delete from operator_param_config where operator_id = #{operatorId};
    </delete>

    <delete id="deleteByOperatorIds">
        delete from operator_param_config
        <where>
            <choose>
                <when test="operatorIds.isEmpty == false">
                    operator_id in
                    <foreach collection="operatorIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    1=0
                </otherwise>
            </choose>
        </where>
    </delete>
    <select id="listByTypeTypeName" resultMap="mybatis-plus_OperatorParamConfig">
        SELECT DISTINCT opc.*
        FROM operator_param_config opc,
        JSON_TABLE(
        opc.param_detail,
        '$[*]' COLUMNS (
        fieldType VARCHAR(50) PATH '$.fieldType',
        fieldTypeName VARCHAR(255) PATH '$.fieldTypeName'  -- 根据实际情况调整字段长度
        )
        ) AS jt
        WHERE jt.fieldType = #{type}
        AND jt.fieldTypeName = #{typeName};
    </select>
</mapper>