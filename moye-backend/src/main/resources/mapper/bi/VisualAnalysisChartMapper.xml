<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.bi.dao.VisualAnalysisChartMapper">

    <resultMap id="ChartResponseMap" type="com.trs.ai.moye.bi.domain.response.ChartResponse">
        <result column="subject_id" property="subjectId"/>
        <result column="chart_title" property="chartTitle"/>
        <result column="data_model_id" property="dataModelId"/>
        <result column="storage_id" property="storageId"/>
        <result column="statistic_groups" property="statisticGroups"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="layer" property="modelLayer"/>
        <association property="dataModel">
            <result column="data_model_id" property="id"/>
            <result column="data_model_name" property="name"/>
        </association>
        <association property="dataModelBusinessCategory">
            <result column="category_id" property="id"/>
            <result column="category_name" property="name"/>
        </association>
        <association property="storage">
            <result column="storage_id" property="id"/>
            <result column="storage_name" property="name"/>
            <result column="storage_en_name" property="storageEnName"/>
        </association>
    </resultMap>

    <select id="selectChartResponseById" resultMap="ChartResponseMap">
        select c.subject_id,
        c.chart_title,
        c.statistic_groups,
        c.data_model_id,
        dm.zh_name as data_model_name,
        bc.id as category_id,
        bc.zh_name as category_name,
        dm.layer as layer,
        c.storage_id,
        dc.name as storage_name,
        ds.en_name as storage_en_name
        from visual_analysis_chart c
        left join data_model dm on dm.id = c.data_model_id
        left join data_storage ds on c.storage_id = ds.id
        left join business_category bc on dm.business_category_id = bc.id
        left join data_connection dc on dc.id = ds.connection_id
        where c.id = #{id}
    </select>

    <select id="selectByStoragePointId" resultType="com.trs.ai.moye.bi.domain.entity.VisualAnalysisChart">
        (
        SELECT vaps.subject_id ,jt.*
        FROM
        visual_analysis_published_subject vaps,
        JSON_TABLE(
        charts,
        '$[*]' COLUMNS(
        id JSON PATH '$.id',
        chart_title JSON PATH '$.chartTitle',
        storage_id INT PATH '$.storageId'
        )
        ) AS jt
        WHERE jt.storage_id = #{storagePointId,jdbcType=INTEGER})
        UNION
        (SELECT subject_id, id, chart_title, storage_id FROM visual_analysis_chart WHERE storage_id = #{storagePointId,jdbcType=INTEGER})
    </select>
</mapper>