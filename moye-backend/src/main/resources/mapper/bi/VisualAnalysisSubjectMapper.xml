<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.bi.dao.VisualAnalysisSubjectMapper">

    <select id="selectByPublishStatus" resultMap="mybatis-plus_VisualAnalysisSubject">
        select * from visual_analysis_subject
        <where>
            <if test="isPublish == true">
                is_publish = #{isPublish}
            </if>
        </where>
    </select>

    <update id="updatePositionById">
        update visual_analysis_subject
        set position_config = #{position, typeHandler=com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler}
        where id = #{id}
    </update>

    <update id="moveByCategoryId">
        update visual_analysis_subject
        set category_id = #{categoryId}
        <where>
            <choose>
                <!--ids为空，不进行操作-->
                <when test="ids == null or ids.isEmpty()">
                    1=-1
                </when>
                <otherwise>
                    id in
                    <foreach collection="ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </otherwise>
            </choose>
        </where>
    </update>

    <select id="selectByDataModelId" resultType="com.trs.ai.moye.bi.domain.entity.VisualAnalysisIdName">
        select s.id as subjectId,s.name as subjectName,c.id chartId,c.chart_title as chartName
        from visual_analysis_subject s
        left join visual_analysis_chart c on c.subject_id = s.id
        where c.data_model_id = #{dataModelId}
    </select>

    <select id="selectPublishedByDataModelId"
        resultType="java.lang.String">
        SELECT GROUP_CONCAT(jt.chartTitle SEPARATOR ', ') AS chartTitles
        FROM visual_analysis_published_subject,
             JSON_TABLE(charts, '$[*]'
                        COLUMNS (
                            dataModelId INT PATH '$.dataModelId',
                            chartTitle VARCHAR(255) PATH '$.chartTitle'
                            )
             ) AS jt
        WHERE jt.dataModelId = #{dataModelId};
    </select>
</mapper>