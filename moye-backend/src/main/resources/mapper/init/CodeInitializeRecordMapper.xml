<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.init.dao.CodeInitializeRecordMapper">
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.trs.ai.moye.init.entity.CodeInitializeRecord">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="order" jdbcType="INTEGER" property="order"/>
        <result column="bean_class" jdbcType="VARCHAR" property="beanClass"/>
        <result column="bean_method" jdbcType="VARCHAR" property="beanMethod"/>
        <result column="execute_time" jdbcType="TIMESTAMP" property="executeTime"/>
        <result column="execute_duration" jdbcType="BIGINT" property="executeDuration"/>
        <result column="execute_result" jdbcType="VARCHAR" property="executeResult"/>
        <result column="details" jdbcType="LONGVARCHAR" property="details"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
    </resultMap>
    <select id="getByName" resultMap="BaseResultMap">
        select
        *
        from code_initialize_record
        <where>
            and name = #{name,jdbcType=VARCHAR}
        </where>
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        *
        from code_initialize_record
    </select>
</mapper>