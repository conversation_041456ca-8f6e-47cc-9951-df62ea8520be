<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.init.dao.MysqlMoveScriptMapper">
    <resultMap id="TwoTupleMap"  type="com.trs.moye.base.common.entity.TwoTuple">
        <result property="first" column="first" javaType="java.lang.Integer"/> <!-- 明确指定为 long -->
        <result property="second" column="second" javaType="string"/>
    </resultMap>
    <select id="getDataProcessOldMonitorConfigList" resultMap="TwoTupleMap">
        SELECT
        data_model_id AS `first`,
        schedule_info AS `second`
        FROM data_model_schedule_config
        WHERE
        execute_mode = 'REALTIME'
        AND data_model_id in (
        SELECT id FROM data_model WHERE layer = 'DWD'
        );
    </select>

    <select id="getExistDataProcessMonitorDataModelIds" resultType="integer">
        SELECT data_model_id FROM data_process_monitor_config WHERE `type` = 'DATA_PROCESS'
    </select>
</mapper>