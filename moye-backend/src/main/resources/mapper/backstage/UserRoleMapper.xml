<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.backstage.dao.UserRoleMapper">
    <resultMap id="BaseResultMap" type="com.trs.ai.moye.backstage.entity.UserRole">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="user_id" jdbcType="INTEGER" property="userId" />
        <result column="role_id" jdbcType="INTEGER" property="roleId" />
    </resultMap>
    <delete id="deleteByUserId">
        delete from user_role
        where user_id = #{userId,jdbcType=BIGINT}
    </delete>
</mapper>