<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.backstage.dao.AuthCertificateKerberosMapper">

    <sql id="Base_Column_List">
        id,krb5_path,principal,
        keytab_path,create_by,create_time,
        update_by,update_time
    </sql>
  <select id="countByPrincipalAndExcludeId" resultType="java.lang.Integer">
    select count(1)
    from auth_certificate_kerberos
    where principal = #{principal}
    <if test="id != null">
      and id =#{id};
    </if>
  </select>
  <select id="pageAuthCertificateKerberosList"
          resultType="com.trs.ai.moye.backstage.entity.AuthCertificateKerberos">
    select * from
    auth_certificate_kerberos
    <where>
      <if test="principal != null and principal != ''">
        principal like concat('%',#{principal},'%')
      </if>
    </where>
  </select>
</mapper>
