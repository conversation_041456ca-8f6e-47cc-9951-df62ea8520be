<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.backstage.dao.UserLogMapper">
    <resultMap id="BaseResultMap" type="com.trs.ai.moye.backstage.entity.UserLog">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="login_time" jdbcType="TIMESTAMP" property="loginTime"/>
        <result column="logout_time" jdbcType="TIMESTAMP" property="logoutTime"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
    </resultMap>
    <delete id="deleteByUserId">
        delete
        from user_log
        where user_id = #{userId,jdbcType=BIGINT}
    </delete>
    <select id="selectByAccountAndLoginTime" resultMap="BaseResultMap">
        select *
        from user_log
                 left join users u on user_log.user_id = u.id
        where u.account = #{account}
          and login_time = #{loginTime}
    </select>


</mapper>