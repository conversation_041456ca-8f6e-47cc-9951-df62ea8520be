<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.backstage.dao.StreamExclusiveNodeConfigMapper">
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.trs.ai.moye.backstage.entity.StreamExclusiveNodeConfig">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="data_models" property="dataModels" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="expect_node_count" jdbcType="INTEGER" property="expectNodeCount"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="concurrent_threads" jdbcType="INTEGER" property="concurrentThreads"/>
        <result column="rate_limit" jdbcType="INTEGER" property="rateLimit"/>
        <result column="create_by" jdbcType="INTEGER" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="INTEGER" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        *
        from stream_exclusive_node_config
    </select>

    <update id="updateById" parameterType="java.util.List">
        <foreach collection="configs" item="config" separator=";">
            update stream_exclusive_node_config
            set data_models        = #{config.dataModels, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            expect_node_count  = #{config.expectNodeCount},
            priority           = #{config.priority},
            concurrent_threads = #{config.concurrentThreads},
            rate_limit         = #{config.rateLimit},
            update_by          = #{config.updateBy},
            update_time        = #{config.updateTime}
            where id = #{config.id}
        </foreach>
    </update>

</mapper>