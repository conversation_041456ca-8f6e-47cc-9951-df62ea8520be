package spark.ability.batch;

import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity;
import java.util.Objects;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.functions;
import org.apache.spark.sql.types.DataTypes;
import spark.ability.base.Ability;
import spark.ability.base.AbilityInterface;
import spark.ability.base.InputBindingParser;
import spark.entity.OperatorColumn;
import spark.entity.TaskInfo;
import spark.exception.ParamValidationException;

/**
 * 字段映射, 定制开发的算子
 *
 * <AUTHOR>
 * @since 2025/8/5 14:21
 */
public class FieldMapping extends Ability implements AbilityInterface {

    @Override
    public Dataset<Row> process(SparkSession sparkSession, BatchOperatorRuntimeEntity operator, TaskInfo taskInfo) {
        Dataset<Row> df = sparkSession.table(operator.getInputTables().values().iterator().next());

        // 遍历输出绑定处理每个目标字段
        Column[] columnsWithAlias = operator.getOutputBind().getBindings().values().stream()
            // 筛选勾选的
            .filter(binding -> Boolean.TRUE.equals(binding.getChecked()))
            .map(binding -> {
                // 有映射走映射
                if (StringUtils.isNotBlank(binding.getJsonPath())) {
                    OperatorColumn column = InputBindingParser.getFieldFromJsonPath(binding.getJsonPath(),
                        operator.getInputTables());
                    return new Column(column.getColumnName()).alias(binding.getFieldName());
                } else {
                    // 没映射的走常量null
                    return functions.lit(null).cast(DataTypes.StringType).alias(binding.getFieldName());
                }
            }).toArray(Column[]::new);

        return df.select(columnsWithAlias);
    }

    @Override
    public void validate(BatchOperatorRuntimeEntity operator) throws ParamValidationException {
        validateInputTables(operator.getInputTables(), 1, 1);

        // 输出参数必选一个
        if (Objects.isNull(operator.getOutputBind()) || MapUtils.isEmpty(operator.getOutputBind().getBindings())) {
            throw new ParamValidationException("[FieldMapping] outputBind is required");
        }
    }

}
