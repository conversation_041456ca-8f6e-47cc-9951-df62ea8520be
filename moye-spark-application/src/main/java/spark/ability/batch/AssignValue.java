package spark.ability.batch;

import com.trs.moye.ability.entity.InputBind.Binding;
import com.trs.moye.ability.entity.InputBind.FixedValueBinding;
import com.trs.moye.ability.entity.InputBind.PropertyBinding;
import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity;
import com.trs.moye.base.common.utils.conditions.SqlUtils;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.service.entity.Condition;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.functions;
import spark.ability.base.Ability;
import spark.ability.base.AbilityInterface;
import spark.ability.base.InputBindingParser;
import spark.entity.TaskInfo;
import spark.exception.ParamValidationException;

/**
 * 赋值
 * 执行条件相当于case when，不满足条件的保留原值或赋值null
 *
 * <AUTHOR>
 * @since 2025/1/8 11:41
 */
public class AssignValue extends Ability implements AbilityInterface {

    /**
     * zhName: 值或属性
     * type: String
     * desc: 用于赋值的固定值或参考字段
     * required: true
     * 单值 或 单属性
     */
    public static final String REFERENCE = "reference";

    @Override
    public Dataset<Row> process(SparkSession sparkSession, BatchOperatorRuntimeEntity operator, TaskInfo taskInfo) {
        Dataset<Row> resourceDf = sparkSession.table(operator.getInputTables().values().iterator().next());

        Binding referenceBinding = operator.getInputBind().getBinding(REFERENCE);
        if (referenceBinding == null) {
            throw new ParamValidationException("Source column binding is missing");
        }

        // 根据绑定类型获取source列（固定值或字段名对应的列）
        Column sourceCol;
        if (referenceBinding instanceof FixedValueBinding) {
            sourceCol = functions.lit(InputBindingParser.getBindingValue(referenceBinding));
        } else if (referenceBinding instanceof PropertyBinding) {
            sourceCol = functions.col(InputBindingParser.getPropertyBindingColumnName(referenceBinding));
        } else {
            throw new ParamValidationException("Unsupported source column binding type");
        }

        // 使用 select 方法同时更新多个列
        List<Column> selectColumns = new ArrayList<>();
        Set<String> existColumns = new HashSet<>(Arrays.asList(resourceDf.columns()));
        // 遍历输出绑定，根据绑定方法进行赋值处理
        Set<String> updatedColumns = new HashSet<>();
        for (String targetColumn : operator.getOutputBind().getBindings().keySet()) {
            selectColumns.add(createConditionCol(operator.getConditions(), sourceCol, targetColumn, existColumns).alias(targetColumn));
            updatedColumns.add(targetColumn);
        }
        // 无需更新的列原样返回
        existColumns.removeAll(updatedColumns);
        for (String existColumn : existColumns) {
            selectColumns.add(functions.col(existColumn));
        }
        return resourceDf.select(selectColumns.toArray(new Column[0]));
    }

    private Column createConditionCol(List<Condition> conditions, Column sourceCol, String targetColumn, Set<String> columnNames) {
        if (CollectionUtils.isEmpty(conditions)) {
            return sourceCol;
        } else {
            // 检查列是否存在
            boolean columnExists = columnNames.contains(targetColumn);

            String conditionSql = SqlUtils.buildSqlWhere(conditions, ConnectionType.HIVE);
            // 满足条件则赋值, 不满足条件的如果列本身存在则保留原值, 否则赋值null
            return functions.when(
                functions.expr(StringUtils.isBlank(conditionSql) ? "true" : conditionSql),
                sourceCol
            ).otherwise(
                columnExists ? functions.col(targetColumn) : functions.lit(null)
            );
        }
    }


    @Override
    public void validate(BatchOperatorRuntimeEntity operator) throws ParamValidationException {
        validateInputTables(operator.getInputTables(), 1, 1);

        // 验证 source column 绑定
        Binding sourceColumnBinding = operator.getInputBind().getBinding(REFERENCE);
        if (sourceColumnBinding == null) {
            throw new ParamValidationException("Source column binding is required");
        }
        if (!(sourceColumnBinding instanceof FixedValueBinding || sourceColumnBinding instanceof PropertyBinding)) {
            throw new ParamValidationException("Source column must be a property binding or fixed value binding");
        }

        // 验证输出绑定
        if (operator.getOutputBind() == null
            || operator.getOutputBind().getBindings().isEmpty()) {
            throw new ParamValidationException("Output bindings are required");
        }
    }

}
