package spark.ability.base;

import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity;
import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity.Type;
import com.trs.moye.ability.enums.AbilityType;
import java.util.HashMap;
import java.util.Map;
import spark.ability.batch.AssignValue;
import spark.ability.batch.BackFill;
import spark.ability.batch.DatetimeFormat;
import spark.ability.batch.Distinct;
import spark.ability.batch.FieldMapping;
import spark.ability.batch.FillNullValue;
import spark.ability.batch.Filter;
import spark.ability.batch.FloatRound;
import spark.ability.batch.GroupCount;
import spark.ability.batch.Join;
import spark.ability.batch.MultiJoin;
import spark.ability.batch.Sum;
import spark.ability.batch.Union;
import spark.ability.connection.Input;
import spark.ability.connection.Save;
import spark.ability.stream.StreamAbilitySdk;

/**
 * 能力
 *
 * <AUTHOR>
 */
public class AbilityFactory {

    private AbilityFactory() {}

    private static final Map<String, AbilityInterface> ABILITIES = new HashMap<>();

    static {
        ABILITIES.put("filter", new Filter());
        ABILITIES.put("distinct", new Distinct());
        ABILITIES.put("backFill", new BackFill());
        ABILITIES.put("datetimeFormat", new DatetimeFormat());
        ABILITIES.put("floatRound", new FloatRound());
        ABILITIES.put("join", new Join());
        ABILITIES.put("groupCount", new GroupCount());
        ABILITIES.put("multiJoin", new MultiJoin());
        ABILITIES.put("fillNullValue", new FillNullValue());
        ABILITIES.put("sum", new Sum());
        ABILITIES.put("assignValue", new AssignValue());
        ABILITIES.put("union", new Union());
        ABILITIES.put("fieldMapping", new FieldMapping());
    }

    /**
     * 获取能力
     * 输入, 输出, 流处理能力, 批处理能力
     *
     * @param entity 能力英文名
     * @return 能力
     */
    public static AbilityInterface getAbility(BatchOperatorRuntimeEntity entity) {
        if (Type.SOURCE.equals(entity.getType())) {
            return new Input();
        } else if (Type.STORAGE.equals(entity.getType())) {
            return new Save();
        } else if (!AbilityType.BATCH.equals(entity.getAbility().getType())) {
            return new StreamAbilitySdk();
        }
        return ABILITIES.get(entity.getAbility().getEnName());
    }

}
