package com.trs.moye.ability.schema;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.moye.ability.entity.AbilityFieldObjectType;
import com.trs.moye.ability.entity.OutputBind;
import com.trs.moye.ability.enums.AbilityFieldType;
import com.trs.moye.ability.utils.ValueParseUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

class OutputBindTest {

    @Test
    void testAddAppendBinding() {
        // 输入数据
        String dataJson = "{\"field\": \"123\"}\n";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);
        String resultJson = "{\"source\": { \"newField1\": \"456\" ,\"newField2\": \"789\"}}";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 配置绑定规则：将 source.newField 追加到 target.field
        OutputBind outputBind = new OutputBind();
        outputBind.addAppendBinding("/source/newField1", "target.newField1")
            .addAppendBinding("/source/newField2", "target.newField2");

        // 执行追加
        JsonNode result = processBindings(dataNode, outputBind, resultNode);

        // 验证结果
        assertEquals("456", result.at("/target/newField1").asText());
        assertEquals("789", result.at("/target/newField2").asText());
    }

    @Test
    void testAddReplaceBinding() {
        // 输入数据
        String dataJson = "{\"field\": \"123\"}\n";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);
        String resultJson = "{\"source\": { \"newField\": \"456\" }}";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 配置绑定规则：将 source.newField 替换到 target.field
        OutputBind outputBind = new OutputBind();
        outputBind.addReplaceBinding("/source/newField", "target.field");

        // 执行替换
        JsonNode result = processBindings(dataNode, outputBind, resultNode);

        // 验证结果
        assertEquals("456", result.at("/target/field").asText());
    }

    @Test
    void testAddReplaceBindingWithNonArrayTarget() {
        // 输入数据
        String dataJson = "{\"field\": \"123\",\"field2\": \"234\"}\n";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);
        String resultJson = "{\"result\": { \"field\": \"456\" }}";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 配置绑定规则：将 source.newField 替换到 target.field
        OutputBind outputBind = new OutputBind();
        outputBind.addReplaceBinding("/result/field", "field")
            .addReplaceBinding("/result/field", "field2");

        // 执行替换
        JsonNode result = processBindings(dataNode, outputBind, resultNode);

        // 验证结果
        assertEquals("456", result.at("/field").asText());
        assertEquals("456", result.at("/field2").asText());
    }

    @Test
    void testConcatBinding() {
        // 输入数据
        String dataJson = "{\"source\": [1, 2, 3]}\n";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);
        String resultJson = "{\"field\": 456,\"field1\": 789}";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 配置绑定规则：将 source.newField 拼接到 target.field
        OutputBind outputBind = new OutputBind();
        outputBind.addConcatBinding("/field", "source")
            .addConcatBinding("/field1", "source");

        // 执行拼接
        JsonNode result = processBindings(dataNode, outputBind, resultNode);

        // 验证结果
        JsonNode array = result.at("/source");
        assertTrue(array.isArray(), "Result should be an array");
        assertEquals("[1,2,3,789]", array.toString());
    }

    @Test
    void testAddAppendBindingWithNonArrayTarget() {
        // 输入数据
        String dataJson = "{}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);
        String resultJson = "{\"result\" : [{\"field\": \"123\"},{ \"field\": \"456\"}]}";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 配置绑定规则：将 source.newField 追加到 target.field
        OutputBind outputBind = new OutputBind();
        outputBind.addAppendBinding("/result/[*]/field", "target.field");

        // 执行追加
        JsonNode result = processBindings(dataNode, outputBind, resultNode);

        // 验证结果
        assertEquals("[\"123\",\"456\"]", result.at("/target/field").toString());
    }


    @Test
    void testReplaceNestedField() {
        // 输入数据
        String dataJson = "{\n" +
                          "  \"field1\": { \"field1_2\": \"123\" },\n" +
                          "  \"field2\": { \"name\": \"test\", \"field2_2\": \"456\" }\n" +
                          "}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);
        String resultJson = "{\n" +
                            "  \"a\": { \"sub1\": \"new_value\" },\n" +
                            "  \"b\": { \"target\": \"old_value\" }\n" +
                            "}";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 配置绑定规则：将 a.sub1 替换到 field1和field2里的字段
        OutputBind outputBind = new OutputBind();
        outputBind.addReplaceBinding("/a/sub1", "field1.field1_2");
        outputBind.addReplaceBinding("/a/sub1", "field2.name");

        // 执行替换
        JsonNode result = processBindings(dataNode, outputBind, resultNode);

        // 验证结果
        assertEquals("new_value", result.at("/field1/field1_2").asText());
        assertEquals("new_value", result.at("/field2/name").asText());
    }

    @Test
    void testConcatArray() {
        // 输入数据
        String dataJson = "{\"arrays\": { \"existingArray\": [5, 6] }}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);
        String resultJson = "{\"source\": { \"newItems\": [1, 2, 3] }}";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 配置绑定规则：将 source.newItems 拼接到 arrays.existingArray
        OutputBind outputBind = new OutputBind();
        outputBind.addConcatBinding("/source/newItems", "arrays.existingArray");

        // 执行拼接
        JsonNode result = processBindings(dataNode, outputBind, resultNode);

        // 验证结果
        JsonNode array = result.at("/arrays/existingArray");
        assertTrue(array.isArray());
        assertEquals(5, array.size());
        assertArrayEquals(new int[]{5, 6, 1, 2, 3}, JsonUtils.getObjectMapper().convertValue(array, int[].class));
    }

    @Test
    void testAutoCreateNestedPath() {
        // 输入数据
        String dataJson = "{ \"existing\": { \"field\": \"value\" } }";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);
        String resultJson = "{\n" +
                            "    \"source\": {\n" +
                            "        \"data\": \"new content\"\n" +
                            "    },\n" +
                            "    \"source2\": {\n" +
                            "        \"test\": \"test content\"\n" +
                            "    }\n" +
                            "}\n";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 配置绑定规则：将 source.data 设置到 new.nested.field
        OutputBind outputBind = new OutputBind();
        outputBind.addReplaceBinding("/source/data", "new.nested.field");
        outputBind.addReplaceBinding("/source2/test", "new.nested.test");

        // 执行替换
        JsonNode result = processBindings(dataNode, outputBind, resultNode);

        // 验证路径自动创建
        assertEquals("new content", result.at("/new/nested/field").asText());
        assertEquals("test content", result.at("/new/nested/test").asText());
    }

    @Test
    void testConcatToNonArrayFieldShouldThrow() {
        // 输入数据
        String dataJson = "{\"target\": { \"nonArray\": \"string value\" }}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);
        String resultJson = "{\"source\": { \"arrayData\": [1, 2, 3] }}";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 配置绑定规则：尝试将数组拼接到非数组字段
        OutputBind outputBind = new OutputBind();
        outputBind.addConcatBinding("/source/arrayData", "target.nonArray");

        // 验证异常
        assertThrows(IllegalArgumentException.class, () -> processBindings(dataNode, outputBind, resultNode));
    }

    @Test
    void testReplaceBindingConvertToString() {
        // 输入数据
        String dataJson = "{\"target\": { \"field\": \"12345\" }}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        // 能力返回结果 - 包含非字符串数据
        String resultJson = "{\"source\": { \"numberValue\": 9999 }}";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 配置绑定规则：将 source.numberValue 替换到 target.field，并指定字段类型为STRING
        OutputBind outputBind = new OutputBind();
        OutputBind.ReplaceBinding binding = new OutputBind.ReplaceBinding(
            "/source/numberValue",
            "target.field",
            AbilityFieldType.STRING,
            0,
            null
        );
        outputBind.getBindings().put("target.field", binding);

        // 执行替换
        JsonNode result = processBindings(dataNode, outputBind, resultNode);

        // 验证结果 - 确保结果是字符串，而非数字
        JsonNode stringValue = result.at("/target/field");
        assertTrue(stringValue.isTextual(), "Result should be a text node");
        assertEquals("9999", stringValue.asText());
    }

    @Test
    void testAppendBindingConvertToString() {
        // 输入数据
        String dataJson = "{\"target\": { }}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        // 能力返回结果 - 包含JSON对象
        String resultJson = "{\n" +
                            "  \"source\": {\n" +
                            "    \"objectValue\": {\n" +
                            "      \"key1\": \"value1\",\n" +
                            "      \"key2\": 123\n" +
                            "    }\n" +
                            "  }\n" +
                            "}";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 配置绑定规则：将 source.objectValue 追加到 target.field，并指定字段类型为STRING
        OutputBind outputBind = new OutputBind();
        OutputBind.AppendBinding binding = new OutputBind.AppendBinding(
            "/source/objectValue",
            "target.field",
            "测试字段",
            AbilityFieldType.STRING,
            0,
            AbilityFieldType.STRING
        );
        outputBind.getBindings().put("target.field", binding);

        // 执行追加
        JsonNode result = processBindings(dataNode, outputBind, resultNode);

        // 验证结果 - 确保结果是字符串表示的JSON对象
        JsonNode stringValue = result.at("/target/field");
        assertTrue(stringValue.isTextual(), "Result should be a text node");
        assertEquals("{\"key1\":\"value1\",\"key2\":123}", stringValue.asText().replaceAll("\\s", ""));
    }

    @Test
    void testConcatBindingConvertToString() {
        // 输入数据 - 包含现有数组
        String dataJson = "{\"arrays\": { \"existingArray\": [5, 6] }}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        // 能力返回结果 - 包含要拼接的数组
        String resultJson = "{\"source\": { \"newItems\": [1, 2, 3] }}";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 配置绑定规则：将 source.newItems 拼接到 arrays.existingArray，并指定字段类型为STRING
        OutputBind outputBind = new OutputBind();
        OutputBind.ConcatBinding binding = new OutputBind.ConcatBinding(
            "/source/newItems",
            "arrays.existingArray",
            AbilityFieldType.STRING,
            0,
            null
        );
        outputBind.getBindings().put("arrays.existingArray", binding);

        // 执行拼接
        JsonNode result = processBindings(dataNode, outputBind, resultNode);

        // 验证结果 - 确保结果是字符串，格式为 "[1,2,3,5,6]"
        JsonNode stringValue = result.at("/arrays/existingArray");
        assertTrue(stringValue.isTextual(), "Result should be a text node");

        // 注意：ConcatBinding的实现将数组内容转为字符串
        String resultText = stringValue.asText().replaceAll("\\s", "");
        assertTrue(
            resultText.equals("[1,2,3,5,6]") || resultText.equals("[5,6,1,2,3]"),
            "Array should be converted to string: " + resultText
        );
    }

    @Test
    void testConcatBindingExtractArray() {
        // 模拟输入数据 - 包含现有数组
        String dataJson = "{ \"embedding\":[ [ 7, 8, 9 ] ] }";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        String bindingJson = "{\n"
            + "                    \"embedding\": {\n"
            + "                        \"method\": \"CONCAT\",\n"
            + "                        \"fieldName\": \"embedding\",\n"
            + "                        \"fieldType\": \"ARRAY\",\n"
            + "                        \"order\": 1,\n"
            + "                        \"itemType\": \"DOUBLE\",\n"
            + "                        \"jsonPath\": \"/[*]\"\n"
            + "                    }\n"
            + "                }";
        OutputBind outputBind = OutputBind.fromJson(JsonUtils.parseJsonNode(bindingJson));

        // 模拟能力返回结果 - 包含要拼接的数组
        String resultJson = "[ [ 1, 2, 3 ], [ 4,5,6 ] ]";
        JsonNode resultNode = JsonUtils.toJsonNode(resultJson);

        for (Map.Entry<String, OutputBind.Binding> entry : outputBind.getBindings().entrySet()) {
            OutputBind.Binding binding = entry.getValue();
            binding.parseValue(dataNode, resultNode);
        }

        assertEquals(dataNode.at("/embedding").size(), 3);
    }

    @Test
    void testConcatBindingWithNonArraySource() {
        // 输入数据 - 包含现有数组
        String dataJson = "{\"arrays\": { \"existingArray\": [5, 6] }}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        // 能力返回结果 - 包含非数组值
        String resultJson = "{\"source\": { \"singleValue\": 99 }}";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 配置绑定规则：将 source.singleValue 拼接到 arrays.existingArray，并指定字段类型为STRING
        OutputBind outputBind = new OutputBind();
        OutputBind.ConcatBinding binding = new OutputBind.ConcatBinding(
            "/source/singleValue",
            "arrays.existingArray",
            AbilityFieldType.STRING,
            0,
            null
        );
        outputBind.getBindings().put("arrays.existingArray", binding);

        // 执行拼接
        JsonNode result = processBindings(dataNode, outputBind, resultNode);

        // 验证结果 - 确保结果是字符串，包含所有值
        JsonNode stringValue = result.at("/arrays/existingArray");
        assertTrue(stringValue.isTextual(), "Result should be a text node");

        // 注意：ConcatBinding的实现将数组内容转为字符串
        String resultText = stringValue.asText().replaceAll("\\s", "");
        assertTrue(
            resultText.equals("[99,5,6]") || resultText.equals("[5,6,99]"),
            "Array should be converted to string: " + resultText
        );
    }

    // 辅助方法：应用所有绑定规则
    private JsonNode processBindings(ObjectNode data, OutputBind outputBind, ObjectNode abilityResultNode) {
        outputBind.getBindings().values().forEach(binding -> binding.parseValue(data, abilityResultNode));
        return data;
    }


    @ParameterizedTest
    @CsvSource({
        "/person/[*]/name, $.person.[*].name",
        "/[*]/name,$.[*].name",
    })
    void convertToStandardJsonPath(String raw, String expected) {
        // 测试转换
        String result = ValueParseUtils.convertToStandardJsonPath(raw);
        assertEquals(expected, result);
    }

    @Test
    void testArrayWithItemTypeConversion() {
        // 输入数据：目标对象有一个字段
        String dataJson = "{\"target\": {}}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        // 能力返回结果：包含一个数字数组
        String resultJson = "{\"source\": {\"numbers\": [1, 2, 3]}}";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 测试场景1：数组类型，元素转为字符串
        OutputBind outputBind = new OutputBind();
        OutputBind.ReplaceBinding binding1 = new OutputBind.ReplaceBinding(
            "/source/numbers",
            "target.stringArray",
            AbilityFieldType.ARRAY,
            0,
            AbilityFieldType.STRING
        );
        outputBind.getBindings().put("target.stringArray", binding1);

        // 测试场景2：数组类型，元素转为布尔值
        OutputBind.ReplaceBinding binding2 = new OutputBind.ReplaceBinding(
            "/source/numbers",
            "target.boolArray",
            AbilityFieldType.ARRAY,
            1,
            AbilityFieldType.BOOLEAN
        );
        outputBind.getBindings().put("target.boolArray", binding2);

        // 执行转换
        JsonNode result = processBindings(dataNode, outputBind, resultNode);

        // 验证结果1：数组中的元素应该被转换为字符串
        JsonNode stringArray = result.at("/target/stringArray");
        assertTrue(stringArray.isArray(), "结果应该是数组");
        assertEquals(3, stringArray.size(), "数组应该有3个元素");
        assertTrue(stringArray.get(0).isTextual(), "数组元素应该是文本类型");
        assertEquals("1", stringArray.get(0).asText());
        assertEquals("2", stringArray.get(1).asText());
        assertEquals("3", stringArray.get(2).asText());

        // 验证结果2：数组中的元素应该被转换为布尔值
        JsonNode boolArray = result.at("/target/boolArray");
        assertTrue(boolArray.isArray(), "结果应该是数组");
        assertEquals(3, boolArray.size(), "数组应该有3个元素");
        assertTrue(boolArray.get(0).isBoolean(), "数组元素应该是布尔类型");
        assertTrue(boolArray.get(0).booleanValue(), "非0数字应转为true");
        assertFalse(boolArray.get(1).booleanValue());
        assertFalse(boolArray.get(2).booleanValue());
    }

    @Test
    void testArrayWithMixedItemTypes() {
        // 输入数据
        String dataJson = "{\"target\": {}}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        // 能力返回结果：包含一个混合类型的数组
        String resultJson = "{\"source\": {\"mixed\": [1, \"true\", 0, \"false\", \"yes\", \"no\", \"是\"]}}";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 测试：数组类型，元素转为布尔值
        OutputBind outputBind = new OutputBind();
        OutputBind.ReplaceBinding binding = new OutputBind.ReplaceBinding(
            "/source/mixed",
            "target.booleanArray",
            AbilityFieldType.ARRAY,
            0,
            AbilityFieldType.BOOLEAN
        );
        outputBind.getBindings().put("target.booleanArray", binding);

        // 执行转换
        JsonNode result = processBindings(dataNode, outputBind, resultNode);

        // 验证结果：所有元素都应该被转换为对应的布尔值
        JsonNode boolArray = result.at("/target/booleanArray");
        assertTrue(boolArray.isArray());
        assertEquals(7, boolArray.size());

        // 验证转换结果：数字1->true, "true"->true, 0->false, "false"->false, "yes"->true, "no"->false
        assertTrue(boolArray.get(0).booleanValue()); // 1 应转为 true
        assertTrue(boolArray.get(1).booleanValue()); // "true" 应转为 true
        assertFalse(boolArray.get(2).booleanValue()); // 0 应转为 false
        assertFalse(boolArray.get(3).booleanValue()); // "false" 应转为 false
        assertTrue(boolArray.get(4).booleanValue()); // "yes" 应转为 true
        assertFalse(boolArray.get(5).booleanValue()); // "no" 应转为 false
        assertTrue(boolArray.get(6).booleanValue()); // "是" 应转为 true
    }

    @Test
    void testNestedArraysWithItemTypeConversion() {
        // 输入数据
        String dataJson = "{\"target\": {}}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        // 能力返回结果：包含一个嵌套数组
        String resultJson = "{\"source\": {\"nested\": [[\"1.1\", \"1.2\"],[\"2.1\", \"2.2\"]]}}";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 测试：数组类型，元素转为浮点值
        OutputBind outputBind = new OutputBind();
        OutputBind.ReplaceBinding binding = new OutputBind.ReplaceBinding(
            "/source/nested/[*]/[*]",
            "target.floatArray",
            AbilityFieldType.ARRAY,
            0,
            AbilityFieldType.FLOAT
        );
        outputBind.getBindings().put("target.floatArray", binding);

        // 执行转换
        JsonNode result = processBindings(dataNode, outputBind, resultNode);

        // 验证结果：所有数字字符串应该被转换为浮点数
        JsonNode floatArray = result.at("/target/floatArray");
        assertTrue(floatArray.isArray());
        assertEquals(4, floatArray.size());

        // 确认所有元素都是浮点数，并且值正确
        assertTrue(floatArray.get(0).isNumber());
        assertEquals(1.1f, floatArray.get(0).floatValue(), 0.0001);
        assertEquals(1.2f, floatArray.get(1).floatValue(), 0.0001);
        assertEquals(2.1f, floatArray.get(2).floatValue(), 0.0001);
        assertEquals(2.2f, floatArray.get(3).floatValue(), 0.0001);
    }

    @Test
    void testNonArraySourceWithArrayType() {
        // 输入数据
        String dataJson = "{\"target\": {}}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        // 能力返回结果：包含一个非数组值
        String resultJson = "{\"source\": {\"singleValue\": \"123\"}}";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 测试：指定字段类型为数组，但源数据不是数组，应该创建只包含一个已转换元素的数组
        OutputBind outputBind = new OutputBind();
        OutputBind.ReplaceBinding binding = new OutputBind.ReplaceBinding(
            "/source/singleValue",
            "target.intArray",
            AbilityFieldType.ARRAY,
            0,
            AbilityFieldType.INT
        );
        outputBind.getBindings().put("target.intArray", binding);

        // 执行转换
        JsonNode result = processBindings(dataNode, outputBind, resultNode);

        // 验证结果：应该是一个只包含一个整数123的数组
        JsonNode intArray = result.at("/target/intArray");
        assertTrue(intArray.isArray());
        assertEquals(1, intArray.size());
        assertTrue(intArray.get(0).isInt());
        assertEquals(123, intArray.get(0).intValue());
    }

    @Test
    void testObjectTypeBindings() {
        // 创建测试用的AbilityFieldObjectType
        AbilityFieldObjectType objectType = new AbilityFieldObjectType();
        objectType.setKnowledgeBaseId(123);
        objectType.setKnowledgeBaseEnName("testBase");
        objectType.setKnowledgeBaseZhName("测试知识库");
        objectType.setKnowledgeBaseType(KnowledgeBaseType.COMPOUND);

        // 测试数据
        String dataJson = "{ \"target\": {} }";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);

        String resultJson = "{\"source\": {\"objData\": {\"name\": \"测试对象\",\"value\": 100}}}";
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);

        // 创建带有objectType的ReplaceBinding
        OutputBind outputBind = new OutputBind();
        OutputBind.ReplaceBinding binding = new OutputBind.ReplaceBinding(
            "/source/objData",
            "target.objField",
            AbilityFieldType.OBJECT,
            0,
            null,
            null,
            objectType
        );
        outputBind.getBindings().put("target.objField", binding);

        // 执行绑定
        JsonNode result = processBindings(dataNode, outputBind, resultNode);

        // 验证结果
        JsonNode objField = result.at("/target/objField");
        assertTrue(objField.isObject());
        assertEquals("测试对象", objField.get("name").asText());
        assertEquals(100, objField.get("value").asInt());

        // 验证binding对象的objectType属性被正确保存
        OutputBind.Binding savedBinding = outputBind.getBinding("target.objField");
        assertEquals(objectType, savedBinding.getObjectType());
        assertEquals(123, savedBinding.getObjectType().getKnowledgeBaseId());
        assertEquals("testBase", savedBinding.getObjectType().getKnowledgeBaseEnName());
        assertEquals("测试知识库", savedBinding.getObjectType().getKnowledgeBaseZhName());
        assertEquals(KnowledgeBaseType.COMPOUND, savedBinding.getObjectType().getKnowledgeBaseType());
    }

    @Test
    void testCreateOutputBindFromJsonWithObjectType() {
        // 创建包含objectType的JSON
        String bindingJson = "{\n" +
                             "  \"objField\": {\n" +
                             "    \"method\": \"REPLACE\",\n" +
                             "    \"fieldName\": \"target.objField\",\n" +
                             "    \"fieldType\": \"OBJECT\",\n" +
                             "    \"order\": 1,\n" +
                             "    \"jsonPath\": \"/data/object\",\n" +
                             "    \"objectType\": {\n" +
                             "      \"knowledgeBaseId\": 456,\n" +
                             "      \"knowledgeBaseEnName\": \"knowledgeBase\",\n" +
                             "      \"knowledgeBaseZhName\": \"样本知识库\",\n" +
                             "      \"knowledgeBaseType\": \"TAG\"\n" +
                             "    }\n" +
                             "  }\n" +
                             "}\n";

        // 修正：使用parseObjectNode替代parseObject
        ObjectNode bindingsNode = JsonUtils.parseObjectNode(bindingJson);

        // 从JSON创建OutputBind对象
        OutputBind outputBind = OutputBind.fromJson(bindingsNode);

        // 验证创建的binding包含正确的objectType
        OutputBind.Binding binding = outputBind.getBinding("target.objField");
        assertNotNull(binding);
        assertEquals("REPLACE", binding.getMethod().name());
        assertEquals(AbilityFieldType.OBJECT, binding.getFieldType());

        AbilityFieldObjectType objectType = binding.getObjectType();
        assertNotNull(objectType);
        assertEquals(456, objectType.getKnowledgeBaseId());
        assertEquals("knowledgeBase", objectType.getKnowledgeBaseEnName());
        assertEquals("样本知识库", objectType.getKnowledgeBaseZhName());
        assertEquals(KnowledgeBaseType.TAG, objectType.getKnowledgeBaseType());
    }

    @Test
    void testDifferentBindingTypesWithObjectType() {
        // 创建测试用的AbilityFieldObjectType
        AbilityFieldObjectType objectType = new AbilityFieldObjectType();
        objectType.setKnowledgeBaseId(789);
        objectType.setKnowledgeBaseEnName("appendBase");
        objectType.setKnowledgeBaseZhName("追加知识库");
        objectType.setKnowledgeBaseType(KnowledgeBaseType.ENTITY);
        // 创建OutputBind，包含不同类型的binding
        OutputBind outputBind = new OutputBind();
        // AppendBinding with objectType
        OutputBind.AppendBinding appendBinding = new OutputBind.AppendBinding(
            "/source/appendObj",
            "target.appendField",
            "追加字段",
            AbilityFieldType.OBJECT,
            1,
            null,
            null,
            objectType
        );
        outputBind.getBindings().put("target.appendField", appendBinding);
        // ConcatBinding with objectType
        AbilityFieldObjectType concatObjectType = new AbilityFieldObjectType();
        concatObjectType.setKnowledgeBaseId(999);
        concatObjectType.setKnowledgeBaseEnName("concatBase");

        OutputBind.ConcatBinding concatBinding = new OutputBind.ConcatBinding(
            "/source/concatObj",
            "target.concatField",
            AbilityFieldType.OBJECT,
            2, null,
            null,
            concatObjectType);
        outputBind.getBindings().put("target.concatField", concatBinding);
        // 验证AppendBinding结果
        OutputBind.Binding savedAppendBinding = outputBind.getBinding("target.appendField");
        assertEquals(objectType, savedAppendBinding.getObjectType());
        assertEquals(789, savedAppendBinding.getObjectType().getKnowledgeBaseId());
        assertEquals(KnowledgeBaseType.ENTITY, savedAppendBinding.getObjectType().getKnowledgeBaseType());

        // 验证ConcatBinding结果
        OutputBind.Binding savedConcatBinding = outputBind.getBinding("target.concatField");
        assertEquals(concatObjectType, savedConcatBinding.getObjectType());
        assertEquals(999, savedConcatBinding.getObjectType().getKnowledgeBaseId());
        assertEquals("concatBase", savedConcatBinding.getObjectType().getKnowledgeBaseEnName());
        // 测试数据
        String dataJson = "{ \"target\": {} }";
        String resultJson = "{\"source\": {\"appendObj\": { \"a\": 1 },\"concatObj\": { \"b\": 2 }}}";
        ObjectNode dataNode = JsonUtils.parseObjectNode(dataJson);
        ObjectNode resultNode = JsonUtils.parseObjectNode(resultJson);
        // 执行绑定
        JsonNode result = processBindings(dataNode, outputBind, resultNode);
        // 验证数据绑定结果
        JsonNode appendField = result.at("/target/appendField");
        assertTrue(appendField.isObject());
        assertEquals(1, appendField.get("a").asInt());
        JsonNode concatField = result.at("/target/concatField");
        assertTrue(concatField.isArray());
        assertEquals(2, concatField.get(0).get("b").asInt());
    }

    @Test
    void testArrayBinding_FieldTypeObject() {
        JsonNode bindNode = JsonUtils.parseObjectNode("{\"alrm_mark_result\":{\"method\":\"REPLACE\",\"fieldName\":\"alrm_mark_result\",\"fieldType\":\"OBJECT\",\"order\":1,\"jsonPath\":\"/result\"}}");
        OutputBind outputBind = OutputBind.fromJson(bindNode);

        JsonNode result = JsonUtils.parseJsonNode("{\"result\":[{\"class2\":\"境外重点网站（域名）\",\"class1\":\"其他类\",\"alarm_level\":\"5级\",\"id\":1,\"keyword\":\"西藏\"},{\"class2\":\"境外重点网站（站点名）\",\"class1\":\"其他类\",\"alarm_level\":\"5级\",\"id\":2,\"keyword\":\"西藏\"}]}");
        ObjectNode data = JsonUtils.parseObjectNode("{\"alrm_mark_result\": []}");


        //根据输出绑定配置将结果回填data
        for (Map.Entry<String, OutputBind.Binding> entry : outputBind.getBindings().entrySet()) {
            OutputBind.Binding binding = entry.getValue();
            JsonNode resultNode = JsonUtils.toJsonNode(JsonUtils.toJsonString(result));
            binding.parseValue(data, resultNode);
        }

        assertTrue(data.has("alrm_mark_result"));
        assertInstanceOf(ArrayNode.class, data.get("alrm_mark_result"));
        assertEquals(2, data.get("alrm_mark_result").size());
    }
}
