package com.trs.moye.ability.entity.operator;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.trs.moye.ability.deserializer.OperatorViewInfoDeserializer;
import com.trs.moye.ability.entity.OperatorStyleInfo;
import com.trs.moye.ability.enums.ArrangeNodeType;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.utils.JsonUtils;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 算子编排画布显示信息
 *
 * <AUTHOR>
 * @since 2025/03/07 15:14:43
 */
@Data
@JsonDeserialize(using = OperatorViewInfoDeserializer.class)
public class OperatorViewInfo {

    public static final String DATA_MODEL_ID = "dataModelId";
    public static final String STORAGE_OPERATOR_CONFIGS = "storageOperatorConfigs";
    public static final String TABLE_TYPE = "tableType";
    /**
     * 算子id
     */
    private Integer operatorId;

    /**
     * 名字
     */
    private String name;

    /**
     * 节点描述
     */
    private String desc;

    /**
     * 算子编排位置信息
     */
    private OperatorStyleInfo style;

    /**
     * 算子连接节点
     */
    private List<Long> targetIds;

    /**
     * 用于前端绘制算子编排的id
     */
    private Long displayId;

    /**
     * 顺序
     */
    private Integer order;

    /**
     * 前端回显使用 table/operator
     */
    private ArrangeNodeType type;

    /**
     * 图标
     */
    private String iconName;

    /**
     * 是否是来源表
     */
    private Boolean isSource;

    /**
     * 来源源表视图信息
     *
     * <AUTHOR>
     * @since 2025/03/10 16:50:32
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class SourceTableViewInfo extends OperatorViewInfo {

        /**
         * 数据建模id
         */
        private Integer dataModelId;

        /**
         * 表类型
         */
        private ModelLayer tableType;
    }

    /**
     * 存储表视图信息
     *
     * <AUTHOR>
     * @since 2025/03/10 16:50:28
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class StorageTableViewInfo extends SourceTableViewInfo {

        /**
         * 存储 ID
         */
        private List<StorageOperatorConfig> storageOperatorConfigs;

    }


    /**
     * 从 JSON 节点创建
     *
     * @param json JSON 格式
     * @return {@link OperatorViewInfo }
     */
    public static OperatorViewInfo fromJson(JsonNode json) {
        if (json == null) {
            return null;
        }

        final OperatorViewInfo instance;
        // 如果 JSON 中的 type 字段为 TABLE，则是 StorageTableViewInfo 或 SourceTableViewInfo
        if (checkTextValueEquals(json, "type", ArrangeNodeType.TABLE.name())) {
            // 如果是贴源表或isSource为true，则是 SourceTableViewInfo
            if (checkTextValueEquals(json, "tableType", ModelLayer.ODS.name())
                || checkBoolValueEquals(json, "isSource", true)) {
                instance = new SourceTableViewInfo();
                parseSourceTableFields(json, (SourceTableViewInfo) instance);
            } else {
                // 否则是 StorageTableViewInfo
                instance = new StorageTableViewInfo();
                parseStorageTableFields(json, (StorageTableViewInfo) instance);
            }
        } else {
            // 否则是 OperatorViewInfo
            instance = new OperatorViewInfo();
        }

        // 解析公共字段
        parseCommonFields(json, instance);
        return instance;
    }

    /**
     * 检查 JSON 节点中指定字段的文本值是否等于给定值
     *
     * @param jsonNode  JSON 节点
     * @param fieldName 字段名
     * @param value     要比较的值
     * @return 如果 JSON 节点中存在指定字段且其文本值等于给定值，则返回 true；否则返回 false
     */
    private static boolean checkTextValueEquals(JsonNode jsonNode, String fieldName, String value) {
        return jsonNode != null && jsonNode.has(fieldName) && jsonNode.get(fieldName).isTextual()
            && jsonNode.get(fieldName).textValue().equals(value);
    }

    /**
     * 检查 JSON 节点中指定字段的布尔值是否等于给定值
     *
     * @param jsonNode  JSON 节点
     * @param fieldName 字段名
     * @param value     要比较的值
     * @return 如果 JSON 节点中存在指定字段且布尔值等于给定值，则返回 true；否则返回 false
     */
    private static boolean checkBoolValueEquals(JsonNode jsonNode, String fieldName, boolean value) {
        return jsonNode != null && jsonNode.has(fieldName) && jsonNode.get(fieldName).isBoolean()
            && jsonNode.get(fieldName).booleanValue() == value;
    }

    private static void parseCommonFields(JsonNode json, OperatorViewInfo instance) {
        instance.setOperatorId(getIntValue(json, "operatorId"));
        instance.setDesc(json.has("desc") ? json.get("desc").asText() : null);
        instance.setName(json.has("name") ? json.get("name").asText() : null);
        instance.setIconName(json.has("iconName") ? json.get("iconName").asText() : null);
        instance.setStyle(parseStyle(json.get("style")));
        instance.setTargetIds(parseLongList(json.get("targetIds")));
        instance.setDisplayId(json.has("displayId") ? json.get("displayId").longValue() : null);
        instance.setOrder(getIntValue(json, "order"));
        instance.setType(parseEnum(json, "type", ArrangeNodeType.class));
    }

    private static void parseSourceTableFields(JsonNode json, SourceTableViewInfo instance) {
        instance.setDataModelId(getIntValue(json, DATA_MODEL_ID));
        instance.setTableType(parseEnum(json, TABLE_TYPE, ModelLayer.class));
    }

    private static void parseStorageTableFields(JsonNode json, StorageTableViewInfo instance) {
        instance.setDataModelId(getIntValue(json, DATA_MODEL_ID));
        instance.setTableType(parseEnum(json, TABLE_TYPE, ModelLayer.class));
        List<StorageOperatorConfig> configs = new ArrayList<>();
        JsonNode configsNode = json.get(STORAGE_OPERATOR_CONFIGS);
        for (JsonNode configNode : configsNode) {
            StorageOperatorConfig config = JsonUtils.parseObject(configNode.toString(), StorageOperatorConfig.class);
            configs.add(config);
        }

        instance.setStorageOperatorConfigs(configs);
    }

    private static Integer getIntValue(JsonNode node, String field) {
        return node.has(field) ? node.get(field).intValue() : null;
    }

    private static OperatorStyleInfo parseStyle(JsonNode node) {
        return node != null ? JsonUtils.parseObject(node.toString(), OperatorStyleInfo.class) : null;
    }

    private static <E extends Enum<E>> E parseEnum(JsonNode node, String field, Class<E> enumType) {
        if (!node.has(field)) {
            return null;
        }
        String value = node.get(field).asText().toUpperCase();
        return Enum.valueOf(enumType, value);
    }

    private static List<Long> parseLongList(JsonNode node) {
        return node != null ? JsonUtils.toList(node.toString(), Long.class) : new ArrayList<>();
    }

}
