package com.trs.moye.ability.invoker;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.InputBind;
import com.trs.moye.ability.entity.Schema;
import com.trs.moye.ability.entity.Schema.ObjectTypeSchema;
import com.trs.moye.ability.enums.AbilityFieldType;
import com.trs.moye.ability.exception.AbilityInvokeException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import org.jetbrains.annotations.NotNull;

/**
 * 能力执行器 - 用于执行 Ability 定义的能力方法
 */
public class LocalAbilityInvoker extends AbstractLocalAbilityInvoker implements AbilityInvoker {

    /**
     * 执行能力方法
     *
     * @param ability   能力定义
     * @param input     输入数据（JSON 格式）
     * @param inputBind 数据绑定规则
     * @return 方法执行结果
     * @throws Throwable Throwable
     */
    public Object invoke(Ability ability, JsonNode input, InputBind inputBind) throws Throwable {
        try {
            // 1. 获取目标类和方法
            String path = ability.getPath();
            String[] parts = path.split("\\.");
            // 修正：手动拼接类名
            String className = buildClassName(parts);
            String methodName = parts[parts.length - 1];

            Class<?> clazz = Class.forName(className);
            Method method = findMethod(clazz, methodName, ability.getInputSchema());

            // 2. 绑定输入数据
            Object[] args = bindParameters(method, input, inputBind);

            // 3. 反射调用方法
            return method.invoke(null, args); // 假设方法是静态的
        } catch (InvocationTargetException ite) {
            //抛出调用方法的异常
            throw ite.getTargetException();
        } catch (Exception e) {
            throw new AbilityInvokeException(
                "调用内部算子失败: " + ability.getPath() + "\n"
                    + " [dataBind]: " + inputBind + "\n"
                , e);
        }
    }

    @NotNull
    private static String buildClassName(String[] parts) {
        StringBuilder classNameBuilder = new StringBuilder();
        for (int i = 0; i < parts.length - 1; i++) {
            if (i > 0) {
                classNameBuilder.append(".");
            }
            classNameBuilder.append(parts[i]);
        }
        return classNameBuilder.toString();
    }

    /**
     * 查找目标方法
     *
     * @param clazz       目标类
     * @param methodName  方法名称
     * @param inputSchema 输入参数模式
     * @return 目标方法
     * @throws NoSuchMethodException 如果方法未找到
     */
    private static Method findMethod(Class<?> clazz, String methodName, Schema inputSchema)
        throws NoSuchMethodException {
        for (Method method : clazz.getDeclaredMethods()) {
            if (method.getName().equals(methodName)
                && ((inputSchema instanceof ObjectTypeSchema && method.getParameterCount() == ((ObjectTypeSchema) inputSchema).getProperties().size())
                || (method.getParameterCount() == 0 && inputSchema.getType().equals(AbilityFieldType.VOID)))) {
                return method;
            }

        }
        throw new NoSuchMethodException("Method not found: " + methodName);
    }

}